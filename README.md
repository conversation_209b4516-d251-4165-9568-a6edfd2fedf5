# Front-end E-closion v3 PRESTIGE
Base de code pour la création d'un site web e-closion v3.
Les sites web de courtiers sont conçus sur un modèle SPA (Single Page App) avec un accès asynchrone à un backend centralisé via un [API](https://documenter.getpostman.com/view/3816130/Rztpp7Xg#e32a8521-6890-4732-8da1-521f3963c987) REST.

Cette base de code correspond à l'offre __PRESTIGE__ [https://www.e-closion.ca/solution-prestige/]

## DevOps
**Prérequis d'installation**
* yarn@1.22.0
* node@v14.17.6
* npm@8.3.2

### Dev
1. Git clone dans un dossier vite
2. Installer les dépendances: $ yarn
3. Copier le fichier d'environnement: $ cp src/environments/environment.dist.ts src/environments/environment.ts && cp src/environments/environment.dist.ts src/environments/environment.prod.ts
4. Modifier les fichiers d'environnement pour réfléter votre configuration locale
5. Servir: $ sh serve.sh
6. Ouvrir http://localhost:4200 

### Staging / Nouveau site
1. Créer l’espace d’hébergement et faire pointer le webroot dans le dossier ./www
2. Se connecter via ssh au webroot
3. S’assurer que le webroot est vide et git clone le site dans le dossier courant
4. Installer les modules node: $ yarn
5. Rouler le script de build: $ sh build.sh (l’output sera dans un dossier ./dist)
6. Créer un dossier www vide
7. Rouler le script de déploiement $ sh deploy.sh (./www sera déplacé dans ./wwwbkp et ./dist sera déplacé vers ./www)
9. Se connecter à l’admin et ajouter le domaine de staging dans la section Domaines autorisés pour l'API en prenant soin de l’associer au bon site.
10. Créer un token MapBox pour la staging (QA) --> https://docs.google.com/document/d/1XBLdeVx5CgL006mU2-Vb6xTKTlpzE28VmcoR4YXBXvw/edit#heading=h.gdow9edswwxx
11. Créer un token Recaptcha pour la staging (QA) --> https://docs.google.com/document/d/1XBLdeVx5CgL006mU2-Vb6xTKTlpzE28VmcoR4YXBXvw/edit#heading=h.yoopo9salh0p

### Production / Nouveau site
1. Créer l’espace d’hébergement et faire pointer le webroot dans le dossier ./www
2. Se connecter via ssh au webroot
3. S’assurer que le webroot est vide et git clone le site dans le dossier courant
4. Installer les modules node: $ yarn
5. Rouler le script de build: $ sh build.sh (l’output sera dans un dossier ./dist)
6. Créer un dossier www vide
7. Rouler le script de déploiement $ sh deploy.sh (./www sera déplacé dans ./wwwbkp et ./dist sera déplacé vers ./www)
8. Tester en pointant le domaine vers l’ip du serveur dans le fichier host local 
9. Se connecter à l’admin et ajouter le domaine de production dans la section Domaines autorisés pour l'API en prenant soin de l’associer au bon site.
10. Toujours dans l’admin, atteindre la configuration du site et cocher “Activer la gateway ?” (toujours) et “Activer les rapports mensuels ?” (si applicable).
11. Une fois les tests d’affichage concluants, faire pointer les DNS du domaine principal sur l’ip du serveur et attendre la propagation.
12. Installer la clé SSL sur l’espace d’hébergement et faire les redirections nécessaires.

### Staging + Production / MAJ site
1. Se connecter via ssh au webroot
2. Git pull les changements
3. Rouler le script de build: $ sh build.sh (l’output sera dans un dossier ./dist)
4. Rouler le script de déploiement $ sh deploy.sh (./www sera déplacé dans ./wwwbkp et ./dist sera déplacé vers ./www)
5. Tester

### Mise en ligne et PréMise en ligne
1. Faire un Vhost local pointant sur le serveur de prod pour valider son bon fonctionnement
2. Changer les DNS afin de pointer sur l'IP du serveur.
3. Créer les codes de vue GA3 et GA4 et remplacer uniquement l'identifiant dans le code à intégrer dans le code existant dans le `index.html` et décommenter le tous ainsi que les 4 ligne ga et gtag dans `app/app.component.ts`.
4. Donner les permissions administratives aux vues Google Analytics (GA3 et GA4) pour inséerer le code de vue dans l'Admin E-Closion (GA3 Pour le moment)
