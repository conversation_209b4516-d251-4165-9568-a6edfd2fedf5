# Générateur de Variables SCSS à partir de Tokens Figma

Ce script permet de convertir automatiquement vos tokens de design Figma (exportés en JSON) en variables CSS SCSS.

## Installation

Aucune installation supplémentaire n'est nécessaire si vous avez Node.js installé.

## Utilisation

## Generer fichier json
En utilisant https://www.figma.com/community/plugin/1345399750040406570/figma-variables-to-json
Color format : hex
Alpha channel : when used
Group by : variant
Naming convention : kebab-case
Collection : custom : Typography, Colors, Interface

### Scripts NPM (recommandé)

```bash
# Générer les variables en mode light (par défaut)
npm run generate-variables

# Générer les variables en mode dark
npm run generate-variables-dark
```

### Utilisation directe

```bash
# Mode light (par défaut)
node generate-scss-variables.js

# Mode light explicite  
node generate-scss-variables.js light

# Mode dark
node generate-scss-variables.js dark

# Aide
node generate-scss-variables.js --help
```

## Structure des tokens

Le script traite 3 catégories principales de tokens :

### 1. Typography
- **Strings** (font-family) : Créent des variables CSS avec media queries automatiques pour mobile si différent
- **Numbers** (font-size, font-weight) : Utilisent la fonction `responsive-size()` pour les tailles, valeurs directes pour les poids

### 2. Colors
- Utilise la clé `light` ou `dark` selon le mode sélectionné
- Convertit automatiquement les underscores en tirets pour les noms de variables CSS

### 3. Interface  
- **Numbers** : Utilisent `responsive-size()` si les valeurs desktop/mobile diffèrent
- **Strings** : Variables CSS directes avec media queries si nécessaire

## Format de sortie

Le fichier généré `src/assets/sass/configs/_generated-variables.scss` contient :
- Variables CSS dans `:root`
- Media queries pour les overrides mobile
- Commentaires avec métadonnées de génération

## Exemple de tokens.json

```json
{
  "typography": {
    "font-family-title": {
      "desktop": "The Seasons",
      "mobile": "The Seasons"
    },
    "text-title-h-1-size": {
      "desktop": 72,
      "mobile": 56
    }
  },
  "colors": {
    "color-text-primary": {
      "light": "#151515", 
      "dark": "#ffffff"
    }
  },
  "interface": {
    "radius-small": {
      "desktop": 16,
      "mobile": 16
    }
  }
}
```

## Exemple de sortie SCSS

```scss
:root {
    // TYPOGRAPHY VARIABLES
    --font-family-title: "The Seasons";
    --text-title-h-1-size: #{responsive-size(56px, 72px, $desktop-lg)};

    // COLOR VARIABLES  
    --color-text-primary: #151515; /* mode light */

    // INTERFACE VARIABLES
    --radius-small: 16px;
}
```

## Configuration

Modifiez les constantes au début du script pour personnaliser :
- `INPUT_FILE` : Fichier de tokens d'entrée (défaut: `tokens.json`)  
- `OUTPUT_FILE` : Fichier SCSS de sortie (défaut: `src/assets/sass/configs/_generated-variables.scss`)

## Fonctionnalités

✅ Support des thèmes light/dark  
✅ Media queries automatiques pour mobile  
✅ Fonction `responsive-size()` pour les valeurs numériques  
✅ Gestion intelligente des font-weights (sans unité px)  
✅ Conversion automatique underscore → tiret pour CSS  
✅ Validation des modes de thème  
✅ Messages d'erreur détaillés  
✅ Métadonnées de génération dans les commentaires
