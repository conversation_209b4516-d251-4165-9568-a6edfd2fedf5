@use 'sass:math';

// Preload critical stuff (might move to a separate file)

// 0. Load tools and helpers
@import "assets/sass/tools/functions";
@import "assets/sass/tools/mixins";

// 0. Load configuration files
@import "assets/sass/configs/colors";
@import "assets/sass/configs/config";
@import "assets/sass/configs/variables";
@import "assets/sass/configs/generated-variables";

// 0. Load reset and normalize
@import "assets/sass/common/reset";
@import "~normalize.css/normalize.css";

// 0. Load layout
@import 'assets/sass/common/layout';

// 1. Import node modules Vendors css
@import "~@ng-select/ng-select/themes/default.theme.css";
// @import "~@fortawesome/fontawesome-free/css/brands.min.css";
// @import "~@fortawesome/fontawesome-free/css/solid.min.css";
// @import "~@fortawesome/fontawesome-free/css/fontawesome.min.css";
// @import '~@ngx-share/button/themes/default/default-theme';
// @import '~@ngx-share/button/styles/share-buttons';
// @import '~@ngx-share/button/styles/themes/default/default-theme';

// @import '../../node_modules/swiper/swiper.scss';
// @import '../../node_modules/swiper/modules/autoplay.scss';
// @import '../../node_modules/swiper/modules/effect-fade.scss';


// 2. Import fonts and icons
@import 'assets/sass/configs/fonts';
@import 'assets/sass/configs/icons';

// -------- HELPERS --------
@import 'assets/sass/utilities/helpers';

// 3. Set global page layout and containers, typography and links/buttons
@import 'assets/sass/common/grid';
@import 'assets/sass/common/typography';
@import 'assets/sass/common/links';
@import 'assets/sass/base/commons';

@import 'assets/sass/common/animations';
// 4. Load lararberg and forms
@import "assets/sass/configs/laraberg-base";
@import 'assets/sass/common/forms';

// 4.5 Load animations scroll utils
@import 'assets/sass/common/animations';

// 5. Load components
@import "assets/sass/components/alert/alert-form-cpn";

@import "assets/sass/components/blog/blog-list-cpn";
@import "assets/sass/components/blog/custom-content-cpn";

@import "assets/sass/components/common/footer-cpn";
@import "assets/sass/components/common/pagination-cpn";
@import "assets/sass/components/common/text-card-1-cpn";
@import "assets/sass/components/common/text-card-2-cpn";

@import "assets/sass/components/common/headers/header1/header-cpn";
@import "assets/sass/components/common/headers/header2/header-2";
@import "assets/sass/components/common/headers/header3/header-3";
@import "assets/sass/components/common/headers/header4/header-4";
@import "assets/sass/components/common/headers/header5/header-5";

@import "assets/sass/components/common/headers/header1/panel-cpn";
@import "assets/sass/components/common/headers/header-last-properties-cpn";

@import "assets/sass/components/contact/broker-contact-form.scss";
@import "assets/sass/components/contact/broker-contact-header-1.scss";
@import "assets/sass/components/contact/broker-contact-header-2.scss";
@import "assets/sass/components/contact/broker-contact-list.scss";

@import "assets/sass/components/cta/cta-alert-cpn";
@import "assets/sass/components/cta/cta-evaluation-cpn";
@import "assets/sass/components/cta/cta-alert-small-cpn";
@import "assets/sass/components/cta/cta-evaluation-small-cpn";
@import "assets/sass/components/cta/cta-broker-cpn";
@import "assets/sass/components/cta/cta-info-box-cpn";
@import "assets/sass/components/cta/cta-pdf-cpn";

@import "assets/sass/components/evaluation/evaluation-form-cpn";
@import "assets/sass/components/evaluation/evaluation-form-single-page-cpn";
@import "assets/sass/components/evaluation/evaluation-start-cpn";

@import "assets/sass/components/hero/hero-landing-cpn";

@import "assets/sass/components/home-hero-cpn";

@import "assets/sass/components/homestaging/homestaging-card-cpn";
@import "assets/sass/components/homestaging/homestaging-slider-cpn";

@import "assets/sass/components/landing/landing-footer-cpn";
@import "assets/sass/components/landing/landing-form-cpn";
@import "assets/sass/components/landing/landing-header-cpn";
@import "assets/sass/components/landing/landing-sheet-cpn";

@import "assets/sass/components/openhouse/openhouses-list-cpn";
@import "assets/sass/components/openhouse/openhouse-card-large-cpn";

@import "assets/sass/components/programs-list-cpn";
@import "assets/sass/components/instagram-gallery-cpn";

@import "assets/sass/components/property/property-addenda-cpn";
@import "assets/sass/components/property/property-characteristics-cpn";
@import "assets/sass/components/property/property-description-cpn";
@import "assets/sass/components/property/property-details-cpn";
@import "assets/sass/components/property/property-downloads-cpn";
@import "assets/sass/components/property/property-exclusion-cpn";
@import "assets/sass/components/property/property-favorite-cpn";
@import "assets/sass/components/property/properties-featured-cpn";
@import "assets/sass/components/property/properties-slider-cpn";
@import "assets/sass/components/property/property-form-contact";
@import "assets/sass/components/property/property-hero-cpn";
@import "assets/sass/components/property/property-inclusion-cpn";
@import "assets/sass/components/property/properties-list-cpn";
@import "assets/sass/components/property/properties-sold-map-cpn";
@import "assets/sass/components/property/property-map-cpn";
@import "assets/sass/components/property/property-navigation-cpn";
@import "assets/sass/components/property/property-pane-cpn";
@import "assets/sass/components/property/property-room-cpn";
@import "assets/sass/components/property/property-statistics-cpn";
@import "assets/sass/components/property/property-share-cpn";
@import "assets/sass/components/property/property-sheet-cpn";
@import "assets/sass/components/property/property-tools-cpn";
@import "assets/sass/components/property/property-openhouse-cpn";

@import "assets/sass/components/propertygroup/propertygroup-card-cpn";

@import "assets/sass/components/search/search-buy-cpn";
@import "assets/sass/components/search/search-filters-cpn";
@import "assets/sass/components/search/search-full-cpn";
@import "assets/sass/components/search/search-map-pane-cpn";
@import "assets/sass/components/search/search-properties-pane-cpn";
@import "assets/sass/components/search/search-sell-cpn";
@import "assets/sass/components/search/search-simple-cpn";
@import "assets/sass/components/search/search-sortable-cpn";
@import "assets/sass/components/laraberg-modifs";

@import "assets/sass/components/specialists/specialist-card-cpn";

@import "assets/sass/components/slider/slider-content-cpn";
@import "assets/sass/components/slider/slider-default-cpn";

@import "assets/sass/components/team/award-card-cpn";
@import "assets/sass/components/team/awards-slider-cpn";
@import "assets/sass/components/team/team-card-1-cpn";
@import "assets/sass/components/team/team-card-2-cpn";
@import "assets/sass/components/team/team-card-3-cpn";
@import "assets/sass/components/team/team-card-4-cpn";
@import "assets/sass/components/team/team-hero-1-cpn";
@import "assets/sass/components/team/team-hero-2-cpn";
@import "assets/sass/components/team/team-hero-3-cpn";
@import "assets/sass/components/team/team-hero-4-cpn";
@import "assets/sass/components/team/team-hero-5-cpn";
@import "assets/sass/components/team/team-interstice-1-cpn";
@import "assets/sass/components/team/team-interstice-2-cpn";
@import "assets/sass/components/team/team-interstice-3-cpn";

@import "assets/sass/components/testimonials/testimonial-card-cpn";
@import "assets/sass/components/testimonials/testimonials-slider-cpn";

@import "assets/sass/components/neighborhood/neighborhood-map-cpn";
@import "assets/sass/components/neighborhood/neighborhood-pane-cpn";
@import "assets/sass/components/neighborhood/neighborhood-hero-cpn";
@import "assets/sass/components/neighborhood/neighborhood-highlights-cpn";
@import "assets/sass/components/neighborhood/neighborhood-photos-cpn";
@import "assets/sass/components/neighborhood/neighborhood-demographics-cpn";
@import "assets/sass/components/neighborhood/neighborhood-avgcost-cpn";
@import "assets/sass/components/neighborhood/neighborhood-grid-cpn";

@import "assets/sass/components/video-viewer-cpn";

@import "assets/sass/components/googlemybusiness/google-my-business-cpn";


// 5. Load components-v2
@import "assets/sass/components-v2/main";


// 6. Load pages (mapped by default menu order)

    // Team
    @import "assets/sass/pages/home-page";
    @import "assets/sass/pages/team-page";
    @import "assets/sass/pages/specialists-page";
    @import "assets/sass/pages/testimonials-page";
    @import "assets/sass/pages/career-contact-page";

    // Properties
    @import "assets/sass/pages/properties-page";
    @import "assets/sass/pages/property-page";
    @import "assets/sass/pages/real-estate-project-page";
    @import "assets/sass/pages/neighborhoods-page";
    @import "assets/sass/pages/neighborhood-page";

    // Buy
    @import "assets/sass/pages/alert-page";
    @import "assets/sass/pages/buy-page";
    @import "assets/sass/pages/open-house-page";

    // Sell
    @import "assets/sass/pages/evaluation-page";
    @import "assets/sass/pages/sell-page";
    @import "assets/sass/pages/homestaging-page";

    // Blog
    @import "assets/sass/pages/blogs-page";
    @import "assets/sass/pages/blog-details-page";

    // Contact
    @import "assets/sass/pages/contact-page";

    // Special pages
    @import "assets/sass/pages/favorites-page";
    @import "assets/sass/pages/404-page";
    @import "assets/sass/pages/privacy-policy-cpn";


// 98. Style override for page printing
@import 'assets/sass/common/print';

// 99. This file contains hacky code to eventually put elsewhere, load it at the very end
@import 'assets/sass/common/shame';