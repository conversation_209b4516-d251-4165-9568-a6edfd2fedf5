import { Component, OnInit, Input } from '@angular/core';

@Component({
  selector: 'lib-property-addenda',
  templateUrl: './property-addenda.component.html'
})

export class PropertyAddendaComponent implements OnInit {
  @Input() addenda;

  constructor () { }

  ngOnInit () {
  }

  onOpenTable ($event) {
    $event.preventDefault();
    $event.currentTarget.parentNode.classList.add('-opened');
  }
}
