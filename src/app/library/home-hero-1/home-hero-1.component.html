<div *ngIf="loading" class="hero-loader">
  <div class="loading-circle"></div>
</div>

<div class="home-hero-ctn style-1" [class.loading]="loading">
	<div class="swiper js-swiper">
		<div class="swiper-container">

			<div class="swiper-wrapper">
				<div class="swiper-slide" *ngFor="let property of properties">

					<ng-container *ngIf="property.ext_coverfull">
						<img class="slide-img" src="{{ property.ext_coverfull }}" alt="{{ property.address_street }} - {{ property.property_category }}">
						<div class="container">
							<a [routerLink]="['urls.property-single' | translate, property.municipality_slug, property.ext_address_slug, property.mls ]" class="home-bloc-info-ctn">
								<div class="home-bloc-head">
									<div class="info-ctn">
										<p class="price">{{ property.price_sale | currency:'CAD':'symbol-narrow':'2.0-0':this.translateService.currentLang }}</p>
										<div class="numbers">

											<ng-container *ngIf="property.rooms_bedroom_total_number">
												<div class="icon-ctn">
													<img src="/assets/images/SVG/icons/bed.svg" alt="">
													<p>{{ property.rooms_bedroom_total_number }}</p>
												</div>
											</ng-container>

											<ng-container *ngIf="property.rooms_bathroom_number">
												<div class="icon-ctn">
													<img src="/assets/images/SVG/icons/sink.svg" alt="">
													<p>{{ property.rooms_bathroom_number }}</p>
												</div>
											</ng-container>

										</div>
									</div>
									<p class="location">{{ property.municipality.description }}</p>
								</div>

								<div class="home-hero-btn-ctn">
									<div class="info-ctn">
										<ng-container *ngIf="property.address_civic_start && property.address_civic_end">
											<p>{{ property.address_civic_start }} - {{ property.address_civic_end }}, <span *ngIf="property.address_street">{{ property.address_street }}</span></p>
										</ng-container>

										<ng-container *ngIf="property.address_civic_start && !property.address_civic_end">
											<p>{{ property.address_civic_start }}, <span *ngIf="property.address_street">{{ property.address_street }}</span></p>
										</ng-container>

										<p>{{ property.property_category }}</p>
									</div>
									<a [routerLink]="['urls.property-single' | translate, property.municipality_slug, property.ext_address_slug, property.mls ]" class="home-hero-btn"></a>
								</div>
							</a>
						</div>
          </ng-container>

				</div>
			</div>

			<div class="swiper-button-prev swiper-btn"><i></i></div>
			<div class="swiper-button-next swiper-btn"><i></i></div>

		</div>
	</div>
</div>

<lib-search-simple></lib-search-simple>
