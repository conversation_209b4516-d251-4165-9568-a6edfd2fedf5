import { Component, OnInit, Input } from '@angular/core';
import { PhonePipe } from '../../pipes/phone.pipe';

@Component({
  selector: 'lib-property-broker-card-1',
  templateUrl: './property-broker-card-1.component.html'
})

export class PropertyBrokerCard1Component implements OnInit {
  @Input() broker;

  constructor () { }

  ngOnInit () {
  }

  onOpenFullScreen () {
    document.querySelectorAll('#contact-pop-up')[0].classList.add('-opened');
    document.documentElement.classList.toggle('-no-scroll');
    document.querySelectorAll('#dynamic-broker-name')[0].innerHTML = this.broker.firstname + ' ' + this.broker.lastname;
    document.querySelectorAll('#dynamic-broker-name')[0].setAttribute('data-code', this.broker.id);
    document.querySelectorAll('#dynamic-broker-name')[0].setAttribute('data-burcode', this.broker.officeCode);
    document.querySelectorAll('#dynamic-broker-role')[0].innerHTML = this.broker.professionnal_title;
    document.querySelectorAll('#dynamic-broker-phone')[0].innerHTML = new PhonePipe().transform(this.broker.phonenumber_1);
    document.querySelectorAll('#dynamic-broker-phone')[0].setAttribute('href', 'tel:' + this.broker.phonenumber_1);
    if (this.broker.url_photo) { document.querySelectorAll('#dynamic-broker-image')[0].setAttribute('src', this.broker.url_photo); } else { document.querySelectorAll('#dynamic-broker-image-ctn')[0].classList.add('hide'); }
  }
}
