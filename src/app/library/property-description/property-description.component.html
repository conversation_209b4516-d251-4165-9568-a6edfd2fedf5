<div class="property-description-cpn" itemscope itemtype="http://schema.org/House">
  <h1 class="title -small -no-top-space" itemprop="address">
    <ng-container *ngIf="property.address_civic_start && property.address_civic_end">
      {{ property.address_civic_start }} - 
      {{ property.address_civic_end }},
      {{ property.address_street }}
      <span *ngIf="property.address_apt">
        {{ 'library.property-details.apt' | translate  }}.{{ property.address_apt }}
      </span>
    </ng-container>
  
    <ng-container *ngIf="property.address_civic_start && !property.address_civic_end">
      {{ property.address_civic_start }},
      {{ property.address_street }}
      <span *ngIf="property.address_apt">
        {{ 'library.property-details.apt' | translate  }}.{{ property.address_apt }}
      </span>
    </ng-container>
    
    <ng-container *ngIf="!property.address_civic_start">
      {{ property.address_street }}
    </ng-container>
  </h1>

	<h2 class="location" itemprop="address" itemscope itemtype="http://schema.org/PostalAddress">
    <span class="municipality">
      <ng-container *ngIf="property.neighborhood_label">
        {{ property.municipality_label }}, 
        {{ property.neighborhood_label }}
      </ng-container>

      <ng-container *ngIf="!property.neighborhood_label">
        {{ property.municipality_label }}
      </ng-container>
    </span>

    <span class="type">{{ property.property_type }}</span>
    <span class="mls">#{{ property.mls }}</span>
	</h2>

	<div class="icon-info-ctn">
		<ng-container *ngIf="property.rooms_bedroom_total_number">
			<div class="icon-wrap">
				<i class="icon-bed"></i>
				<p itemprop="numberOfRooms">{{ property.rooms_bedroom_total_number }}</p>
			</div>
		</ng-container>

		<ng-container *ngIf="property.rooms_washroom_number">
			<div class="icon-wrap">
				<i class="icon-sink"></i>
				<p itemprop="numberOfRooms">{{ property.rooms_washroom_number }}</p>
			</div>
		</ng-container>

		<ng-container *ngIf="property.rooms_bathroom_number">
			<div class="icon-wrap">
				<i class="icon-shower"></i>
				<p itemprop="numberOfRooms">{{ property.rooms_bathroom_number }}</p>
			</div>
		</ng-container>

		<div class="icon-wrap" *ngIf="property.building_dimensions_living_area && property.building_dimensions_living_area_units">
			<i class="icon-area"></i>
			<p>
				{{ property.building_dimensions_living_area | numberFormat }}
				{{ property.building_dimensions_living_area_units }}
			</p>
		</div>

		<div class="icon-wrap" *ngIf="!property.building_dimensions_living_area && !property.building_dimensions_living_area_units">
			<i class="icon-area"></i>
			<p>N/D</p>
		</div>
	</div>

	<p itemprop="description" class="description" *ngIf="property.notes?.length > 0">{{ property.notes[0].note }}</p>
</div>
