import { Component, Input, OnInit } from '@angular/core';
import { BlocksService } from '@/services/v3/contentblocks/contentblocks.service';

@Component({
  selector: 'lib-team-hero-4',
  templateUrl: './team-hero-4.component.html'
})

export class TeamHero4Component implements OnInit {
  @Input() teamMember;

  public blockTitle: string;
  public blockContent: string;

  constructor (private blocksService: BlocksService) {
    this.blocksService.getBlock('bloc-equipe').subscribe(data => {
      this.blockTitle = data.title;
      this.blockContent = data.text;
    });
  }

  ngOnInit () {
  }
}
