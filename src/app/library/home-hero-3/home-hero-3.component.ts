import { Component, OnInit, Input } from '@angular/core';
import { BlocksService } from '@/services/v3/contentblocks/contentblocks.service';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
@Component({
  selector: 'lib-home-hero-3',
  templateUrl: './home-hero-3.component.html'
})

export class HomeHero3Component implements OnInit {
  @Input() customContent;
  public blockTitle: string;
	cleanVideo

  constructor (
    private blocksService: BlocksService,
    private sanitizer: DomSanitizer
  ) {

    

    this.blocksService.getBlock('bloc-accueil').subscribe(data => {
      this.blockTitle = data.title;
    });
  }

  ngOnInit () {
    this.cleanVideo	= this.sanitizer.bypassSecurityTrustResourceUrl(this.customContent.banner_video_formatted)
  }
}
