import { Component, OnInit } from '@angular/core';
// import { LangChangeEvent, TranslateService } from '@ngx-translate/core';
// import { Router } from '@angular/router';

import { InscriptionsService } from '@/services/v3/inscriptions/inscriptions.service';

@Component({
  selector: 'lib-header-last-properties',
  templateUrl: './header-last-properties.component.html'
})

export class HeaderLastPropertiesComponent implements OnInit {
  properties;

  constructor (
    private inscriptionsService: InscriptionsService
  ) {

  }

  ngOnInit () {
    // Fetch the 3 newest properties to show in properties menu
    const search = { sold: 0, sort: 'newest' };
    this.inscriptionsService
      .getInscriptions(3, search)
      .subscribe(({ data }) => {
        this.properties = data.map((p) => {
          const url = '/' + p.municipality_slug + (p.neighborhood_slug ? '/' + p.neighborhood_slug : '') + '/' + p.ext_address_slug + '/' + p.mls;
          return { ...p, url };
        });
      });
  }
}
