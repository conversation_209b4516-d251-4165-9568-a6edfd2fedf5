import { Component, Input, OnInit } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { faCoffee } from '@fortawesome/free-solid-svg-icons';
@Component({
  selector: 'lib-blog-details',
  templateUrl: './blog-details.component.html'
})

export class BlogDetailsComponent implements OnInit {
  @Input() post;
  @Input() iframeUrl;
  faCoffee = faCoffee;
  public currentUrl = '';
  public contentSanitized: any;

  constructor (
    private sanitizer: DomSanitizer
  ) {
    this.currentUrl = window.location.href;
  }

  ngOnInit () {
    this.contentSanitized = this.sanitizer.bypassSecurityTrustHtml(this.post.content);
  }

  onPrint () {
    window.print();
  }

  backTop(){
    document.querySelector('#imgTop').scrollIntoView({ behavior: 'smooth', block: 'center' });
  }
}
