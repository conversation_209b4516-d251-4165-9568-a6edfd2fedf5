import { Component, OnInit, Input, ElementRef } from "@angular/core";
import { TranslateService } from "@ngx-translate/core";

@Component({
  selector: "lib-openhouse-card",
  templateUrl: "./openhouse-card.component.html"
})
export class OpenhouseCardComponent implements OnInit {
  @Input() property;
  @Input() date;

  public currentLang: any;
  propertyUrl: any;

  constructor(
    private hostElement: ElementRef,
    public translate: TranslateService
  ) {
    const userLang = window.location.pathname.split("/")[1];
    this.currentLang = userLang || "fr";
  }

  ngOnInit() {
    if (this.property.neighborhood_slug) {
      this.propertyUrl =
        this.translate.instant("urls.property-single") +
        "/" +
        this.property.municipality_slug +
        "/" +
        this.property.neighborhood_slug +
        "/" +
        this.property.ext_address_slug +
        "/" +
        this.property.mls;
    } else {
      this.propertyUrl =
        this.translate.instant("urls.property-single") +
        "/" +
        this.property.municipality_slug +
        "/" +
        this.property.ext_address_slug +
        "/" +
        this.property.mls;
    }
  }

  ngAfterViewInit() {
    const elems =
      this.hostElement.nativeElement.querySelectorAll(".jsProperty");
    const scope = this;

    const currentElem = Array.prototype.slice.call(elems)[0];

    currentElem.addEventListener("mouseover", function () {
      const addressHeight = Array.prototype.slice.call(
        scope.hostElement.nativeElement.querySelectorAll(".jsProperty .address")
      )[0].offsetHeight;
      const alignHeight = Array.prototype.slice.call(
        scope.hostElement.nativeElement.querySelectorAll(".jsProperty .align")
      )[0].offsetHeight;
      const maxHeight = alignHeight + addressHeight + 42;
      Array.prototype.slice.call(
        scope.hostElement.nativeElement.querySelectorAll(
          ".jsProperty .properties-info"
        )
      )[0].style.maxHeight = maxHeight + "px";
    });

    currentElem.addEventListener("mouseleave", function () {
      Array.prototype.slice.call(
        scope.hostElement.nativeElement.querySelectorAll(
          ".jsProperty .properties-info"
        )
      )[0].style.maxHeight = "42px";
    });
  }
}
