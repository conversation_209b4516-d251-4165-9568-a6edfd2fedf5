<a class="jsProperty" [routerLink]="propertyUrl">
  <!-- <span
    *ngIf="property.start_time && property.end_time"
    class="properties-label -label-view"
    >{{ "library.properties-open-house.visit-from" | translate }}
    {{ " " + property.start_time }}
    {{ "library.properties-open-house.visit-to" | translate }}
    {{ " " + property.end_time }}</span
  > -->
  <div class="containerBandeau" *ngIf="date">
    <span href="" class="properties-label -label-view"
      >{{ "library.properties.visit" | translate }}
      {{ date | localizedDate : "longDate" }}</span
    >
  </div>
  <div class="img-ctn" *ngIf="property.ext_coverphoto">
    <img
      src="{{ property.ext_coverphoto }}"
      alt="{{ 'library.properties.alt-image' | translate }}"
    />
  </div>
  <div class="gradiant"></div>
  <div class="filter"></div>

  <div class="properties-info">
    <div class="bloc-head">
      <ng-container *ngIf="!property.status">
        <p class="price" *ngIf="property.price_sale != false">
          {{
            property.price_sale
              | currency : "CAD" : "symbol-narrow" : "2.0-0" : this.currentLang
          }}
        </p>

        <p
          class="price"
          *ngIf="property.price_rental && property.price_sale == false"
        >
          <ng-container *ngIf="property.property_category != 'Commercial'">
            <span class="c -inner"
              >{{
                property.price_rental
                  | currency
                    : "CAD"
                    : "symbol-narrow"
                    : "2.0-0"
                    : this.currentLang
              }}
              {{ "library.property-hero.per-month" | translate }}</span
            >
          </ng-container>

          <ng-container *ngIf="property.property_category == 'Commercial'">
            <ng-container *ngIf="property.rental_period == 'A'">
              <span class="c a -inner"
                >{{
                  property.price_rental
                    | currency
                      : "CAD"
                      : "symbol-narrow"
                      : "2.0-0"
                      : this.currentLang
                }}
                {{ "library.property-hero.commercial-year" | translate }}</span
              >
            </ng-container>

            <ng-container *ngIf="property.rental_period != 'A'">
              <span class="c m -inner"
                >{{
                  property.price_rental
                    | currency
                      : "CAD"
                      : "symbol-narrow"
                      : "2.0-0"
                      : this.currentLang
                }}
                {{ "library.property-hero.commercial-month" | translate }}</span
              >
            </ng-container>
          </ng-container>
        </p>
      </ng-container>

      <p class="type">{{ property.property_type }}</p>
    </div>
    <div class="more-info">
      <p class="address">
        <ng-container
          *ngIf="property.address_civic_start && property.address_civic_end"
        >
          <span
            >{{ property.address_civic_start }}
            -
            {{ property.address_civic_end }},
          </span>
        </ng-container>
        <ng-container
          *ngIf="property.address_civic_start && !property.address_civic_end"
        >
          <span>{{ property.address_civic_start }}, </span>
        </ng-container>
        <span *ngIf="property.address_street">{{
          property.address_street
        }}</span>
        <span *ngIf="property.address_apt">
          {{ "library.property-details.apt" | translate }}.
          {{ property.address_apt }}</span
        >
      </p>
      <div class="align">
        <p class="location">
          {{ property.municipality_label | shorten : 25 : "..." }}
        </p>
        <div class="numbers">
          <div class="icon-ctn" *ngIf="property.rooms_bedroom_total_number">
            <i class="icon-bed"></i>
            <p>{{ property.rooms_bedroom_total_number }}</p>
          </div>
          <div class="icon-ctn" *ngIf="property.rooms_bathroom_number">
            <i class="icon-sink"></i>
            <p>{{ property.rooms_bathroom_number }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</a>
