<div class="homestaging-slider-cpn">
    <div class="container">
        <div class="grid" *ngIf="homestagingsItems?.length > 0">
            <div class="text-ctn col-12 col-t-lg-6">
                <h2 class="-no-top-space title">{{blockTitle}}</h2>
                <div class="description page-description" [innerHTML]="blockContent"></div>
            </div>
            <div class="slider-ctn col-12 col-t-lg-6">
                <lib-slider-default [slides]="homestagingsItems" [showDesc]="true"></lib-slider-default>
            </div>
        </div>

        <div class="team-info-box -large">
            <div class="team-info-box-content grid">
                <div class="col-12 col-t-lg-8">
                    <p class="-h4 -no-space">{{ 'library.homestaging-slider.infobox.title' | translate }}</p>
                    <p class="description">{{ 'library.homestaging-slider.infobox.description' | translate }}</p>
                </div>
                <div class="col-12 col-t-lg-4">
                    <a [routerLink]="['urls.contact' | translate ]" class="main-button -primary">{{ 'library.homestaging-slider.infobox.button' | translate }}</a>
                </div>
            </div>
        </div>
    </div>
</div>