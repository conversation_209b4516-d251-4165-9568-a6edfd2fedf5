import { Component, OnInit } from '@angular/core';
import Instafeed from 'instafeed.js';

@Component({
  selector: 'lib-instagram-gallery',
  templateUrl: './instagram-gallery.component.html'
})

export class InstagramGalleryComponent implements OnInit {
  // 1. Login to Meta Developers and create a new app
  // 2. Add Instagram basic display product to app
  // 3. Add a the instagram account as a test user and accept permissions on account
  // 4. Use the Token Generator in Developer

  // Test token
  token = 'IGQVJYVUZA0MEdRb3BOdWNseUdIRUJMMXNZARTlfLThucGh6TXcyZAW91VXNhUUluZA19nWjZAjT1U3M3hjWkpUNGhXS3hVcXRvTkZAjV2t3dFg3Tl9mczNVdkxsUWZA4d2xlTGlHbjc5eTJtbDZA3ZA1h6OHZATLQZDZD';

  constructor () { }

  ngOnInit () {
    const settings = {
      accessToken: this.token,
      limit: 6
    };

    const feed = new Instafeed(settings);
    feed.run();
  }
}
