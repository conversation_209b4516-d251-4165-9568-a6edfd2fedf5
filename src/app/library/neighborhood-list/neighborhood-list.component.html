<div class="properties-list-cpn" *ngIf="neighborhood && properties?.length > 0">
	<div class="container">
		<div class="cpn-head">
			<h2 class="title" *ngIf="neighborhood.name">
				{{ formatTitle(neighborhood.name, 'library.neighborhood-list.title' | translate) }}
			</h2>
			<a [routerLink]="['urls.search-properties' | translate ]" class="small-link right -hide-mobile">{{ "library.properties-list.all" | translate }}<i class="icon-arrow-right"></i></a>
		</div>
		<div *ngIf="properties" class="properties-list-ctn">
			<div class="properties" *ngFor="let property of properties">
				<lib-properties [property]="property"></lib-properties>
			</div>
		</div>

		<a [routerLink]="['urls.search-properties' | translate ]" class="small-link right -show-mobile">{{ "library.properties-list.all" | translate }}<i class="icon-arrow-right"></i></a>
	</div>
</div>
