import { Component, OnInit } from '@angular/core';
import { BlocksService } from '@/services/v3/contentblocks/contentblocks.service';

@Component({
  selector: 'lib-buy-sheet',
  templateUrl: './buy-sheet.component.html'
})

export class BuySheetComponent implements OnInit {
  public blockTitle: string;
  public blockContent: string;

  constructor (
    private blocksService: BlocksService
  ) {}

  ngOnInit () {
    this.blocksService.getBlock('bloc-acheter').subscribe(data => {
      this.blockTitle = data.title;
      this.blockContent = data.text;
      document.querySelector('.buy-sheet-page').classList.add('show');
    });
  }
}
