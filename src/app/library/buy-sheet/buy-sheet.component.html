<div class="buy-sheet-page container grid">

	<div class="page-list-ctn col-12 col-t-lg-8">
		<h1 *ngIf="blockTitle" class="page-title">{{ blockTitle }}</h1>
		<div class="page-description" [innerHtml]="blockContent"></div>
	</div>
	
	<div class="team-info-wrap col-12 col-t-lg-4">
		<div class="team-info-box">
			<div class="team-info-box-content">
				<p class="title">{{ 'library.buy-sheet.team.title' | translate }}</p>
				<p class="description">{{ 'library.buy-sheet.team.description' | translate }}</p>
				<a [routerLink]="['urls.contact' | translate ]" class="main-button -primary">{{ 'library.buy-sheet.team.btn' | translate }}</a>
			</div>
		</div>
	</div>

</div>