import { Component, Input, OnInit } from "@angular/core";
import { BlogService } from "@/services/v3/blog/blog.service";

@Component({
  selector: "lib-blog-list-4",
  templateUrl: "./blog-list-4.component.html",
})
export class BlogList4Component implements OnInit {
  @Input() currentPost: any = {};
  public blogPosts = [];

  constructor(private blog: BlogService) {}

  ngOnInit() {
    this.blog
      .getPosts(4, this.currentPost.category_slug)
      .subscribe(({ data }) => {
        // Limit is +1 to allow filtering out the current post
        this.blogPosts = data.filter((post) => post.id !== this.currentPost.id);
      });
  }
}
