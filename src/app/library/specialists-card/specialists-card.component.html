<div class="specialist-wrap" itemscope itemtype="http://schema.org/Organisation">
	<div class="specialist-content">
		<div class="img-ctn" *ngIf="specialist.logo">
			<img itemprop="logo" src="{{specialist.logo}}" alt="">
		</div>

		<h3 itemprop="legalName" *ngIf="specialist.name" class="title">{{specialist.name}}</h3>
		<div itemprop="knowsAbout" class="description" [innerHtml]="specialist.description"></div>

		<div class="user-ctn">
      <p *ngIf="specialist.contact" class="name">{{specialist.contact}}</p>
			<p class="address" itemprop="address">
				<ng-container *ngIf="specialist.address">
					{{specialist.address}},
      			</ng-container>
      			{{specialist.city}} {{specialist.province}} <br>{{specialist.postal_code}}</p>

			<div class="contact-ctn">
				<a itemprop="telephone" *ngIf="specialist.phone" href="tel:{{ specialist.phone | phone }}" class="mobile"><i class="icon-mobile"></i> {{ specialist.phone | phone }}</a>
				<a item="sameAs" *ngIf="specialist.website" href="{{specialist.website}}" target="_blank" class="website"><i class="icon-website"></i>{{ 'library.specialists-card.website' | translate }}</a>
				<a itemprop="email" *ngIf="specialist.email" href="mailto:{{specialist.email}}" class="website"><i class="icon-mail"></i>Courriel</a>
			</div>
		</div>
	</div>
</div>
