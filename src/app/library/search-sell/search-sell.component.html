<div class="search-sell-cpn">
	<div class="container -narrow">

    <h2 class="-center"><span class="icon icon-eval-1"></span>{{ 'library.search-sell.title' | translate }}</h2>
    <p>{{ 'library.search-sell.description' | translate }}</p>
    
    <form class="form-ctn" (ngSubmit)="onSubmitSell()">
      <div class="input-ctn">
        <input type="search" id="address-input" class="large" [(ngModel)]="addressText" [ngModelOptions]="{ standalone: true }"
          placeholder="{{ 'library.search-sell.input-placeholder' | translate }}"/>
        <button class="main-button -primary-small" type="submit"><span>{{ 'library.search-sell.button' | translate }}</span></button>
      </div>
    </form>

	</div>
</div>
