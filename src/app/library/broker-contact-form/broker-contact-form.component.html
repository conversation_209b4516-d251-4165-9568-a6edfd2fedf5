<div class="broker-contact-form-cpn" [ngClass]="{ send: formSend, narrow: narrow }">
  <form [formGroup]="contactForm" [ngClass]="{ loading: formLoading }" (ngSubmit)="onSubmit()" class="contact-form">
    <div class="form-column">
      <div class="form-row form-group" formGroupName="name">
        <div class="input-ctn -dual">
          <label>{{ "library.broker-contact-form.firstname" | translate }}</label>
          <input type="text" class="form-control" [ngClass]="{'-error': firstName.invalid && (firstName.dirty || firstName.touched)}" formControlName="firstName" name="first_name">
          <div class="form-control-feedback"
            *ngIf="firstName.errors && (firstName.dirty || firstName.touched)">
            <p>{{ "library.broker-contact-form.errors.firstname_required" | translate }}</p>
          </div>
        </div>
        <div class="input-ctn -dual">
          <label>{{ "library.broker-contact-form.lastname" | translate }}</label>
          <input type="text" class="form-control" [ngClass]="{'-error': lastName.invalid && (lastName.dirty || lastName.touched)}" formControlName="lastName" name="last_name">
          <div class="form-control-feedback"
            *ngIf="lastName.errors && (lastName.dirty || lastName.touched)">
            <p *ngIf="lastName.errors.required">{{ "library.broker-contact-form.errors.lastname_required" | translate }}</p>
          </div>
        </div>
      </div>

      <ng-container *ngIf="!landing">
        <div class="form-row -bottom" formGroupName="phone">
          <div class="input-ctn -dual">
            <label>{{ "library.broker-contact-form.phone" | translate }}</label>
            <input type="text" class="form-control" mask="************" [ngClass]="{'-error': phoneNumber.invalid && (phoneNumber.dirty || phoneNumber.touched)}" formControlName="phoneNumber" name="phone">
          </div>
          <div class="input-ctn -dual -small">
            <label></label>
            <input type="text" name="ext" class="form-control" formControlName="phoneExt"  placeholder="Ext">
          </div>
          <div class="form-control-feedback" *ngIf="phoneNumber.errors && (phoneNumber.dirty || phoneNumber.touched)">
            <p *ngIf="phoneNumber.errors.required">{{ "library.broker-contact-form.errors.phone_required" | translate }}</p>
            <p *ngIf="phoneNumber.errors.pattern">{{ "library.broker-contact-form.errors.phone_invalid" | translate }}</p>
            <p *ngIf="phoneNumber.errors.minlength">{{ "library.broker-contact-form.errors.phone_invalid" | translate }}</p>
          </div>
        </div>

        <div class="form-row form-group">
          <div class="input-ctn" >
            <label>{{ "library.broker-contact-form.email" | translate }}</label>
            <input type="email" class="form-control" (focus)="isTyping = true" (blur)="isTyping = false" [ngClass]="{'-error': email.invalid && (email.dirty || email.touched) && !isTyping}" formControlName="email" name="email">
            <div class="form-control-feedback" *ngIf="email.errors && (email.dirty || email.touched) && !isTyping">
              <p *ngIf="email.errors.required">{{ "library.broker-contact-form.errors.email_required" | translate }}</p>
              <p *ngIf="email.errors.pattern">{{ "library.broker-contact-form.errors.email_invalid" | translate }}</p>
            </div>
          </div>
        </div>
      </ng-container>

      <ng-container *ngIf="landing">
        <div class="form-row">
          <div class="input-ctn -dual" formGroupName="phone">
            <label>{{ "library.broker-contact-form.phone" | translate }}</label>
            <input type="text" class="form-control" mask="************" [ngClass]="{'-error': phoneNumber.invalid && (phoneNumber.dirty || phoneNumber.touched)}" formControlName="phoneNumber" name="phone">
            <div class="form-control-feedback" *ngIf="phoneNumber.errors && (phoneNumber.dirty || phoneNumber.touched)">
              <p *ngIf="phoneNumber.errors.required">{{ "library.broker-contact-form.errors.phone_required" | translate }}</p>
              <p *ngIf="phoneNumber.errors.pattern">{{ "library.broker-contact-form.errors.phone_invalid" | translate }}</p>
              <p *ngIf="phoneNumber.errors.minlength">{{ "library.broker-contact-form.errors.phone_invalid" | translate }}</p>
            </div>
          </div>

          <div class="input-ctn -dual" >
            <label>{{ "library.broker-contact-form.email" | translate }}</label>
            <input type="email" class="form-control" (focus)="isTyping = true" (blur)="isTyping = false" [ngClass]="{'-error': email.invalid && (email.dirty || email.touched) && !isTyping}" formControlName="email" name="email">
            <div class="form-control-feedback" *ngIf="email.errors && (email.dirty || email.touched) && !isTyping">
              <p *ngIf="email.errors.required">{{ "library.broker-contact-form.errors.email_required" | translate }}</p>
              <p *ngIf="email.errors.pattern">{{ "library.broker-contact-form.errors.email_invalid" | translate }}</p>
            </div>
          </div>
        </div>
      </ng-container>
    </div>

    <div class="form-column">
      <div class="form-row">
        <div class="input-ctn">
          <label>{{ "library.broker-contact-form.message" | translate }}</label>
          <textarea name="message" [ngClass]="{'-error': message.invalid && (message.dirty || message.touched)}" class="form-control" formControlName="message"></textarea>
          <div class="form-control-feedback"
            *ngIf="message.errors && (message.dirty || message.touched)">
            <p *ngIf="message.errors.required">{{ "library.broker-contact-form.errors.message_required" | translate }}</p>
          </div>
        </div>
      </div>

      <button class="main-button -primary" [disabled]="!contactForm.valid" type="submit"><span>{{ "library.broker-contact-form.submit" | translate }}</span></button>
    </div>
  </form>

  <div class="form-loader" *ngIf="formLoading"><div class="loading-circle"></div></div>

  <div class="form-response" *ngIf="formSend">
    <h1 class="-page-title">{{ "library.broker-contact-form.successTitle" | translate }}</h1>

    <ng-container *ngIf="successMessage">
      <p class="warning-message">{{ "library.broker-contact-form.successMessage" | translate }}</p>
      <div class="button-ctn">
        <a class="main-button -primary" (click)="resetForm()">{{ 'library.broker-contact-form.back' | translate }}</a>
      </div>
    </ng-container>

    <ng-container *ngIf="errorMessage">
      <p class="warning-message">{{ "library.broker-contact-form.errorMessage" | translate }}</p>
      <div class="button-ctn">
        <a class="main-button -primary" (click)="retry()">{{ 'library.broker-contact-form.back' | translate }}</a>
      </div>
    </ng-container>

  </div>
</div>
