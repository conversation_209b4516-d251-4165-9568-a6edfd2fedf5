import { Component, OnInit, Input } from '@angular/core';
import { UntypedFormBuilder, UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { Subscription } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import { ReCaptchaV3Service } from 'ng-recaptcha';

import { ContactService } from '@/services/v3/contact/contact.service';

@Component({
  selector: 'lib-broker-contact-form',
  templateUrl: './broker-contact-form.component.html'
})

export class BrokerContactFormComponent implements OnInit {
  @Input() narrow;
  @Input() formSubject;
  @Input() landing = false; // Email and phone fields are on same line on landing form

  contactForm: UntypedFormGroup;
  firstName: UntypedFormControl;
  lastName: UntypedFormControl;
  phoneNumber: UntypedFormControl;
  phoneExt: UntypedFormControl;
  email: UntypedFormControl;
  message: UntypedFormControl;

  subject: any;
  isTyping = false;

  successMessage: boolean = false;
  errorMessage: boolean = false;
  formSend: boolean = false;
  formLoading: boolean = false;

  // Recaptcha v3 Subscription
  private submitExecutionSubscription: Subscription

  constructor (
    private recaptchaV3Service: ReCaptchaV3Service,
    private formBuilder: UntypedFormBuilder,
    private contactService: ContactService,
    private translate: TranslateService
  ) {}

  ngOnInit () {
    this.createFormControls();
    this.createForm();
    this.delayRCDisplay();
  }

  delayRCDisplay () {
    setTimeout(() => {
      const result = document.getElementsByClassName('grecaptcha-badge');
      const recaptcha = result.item(0) as HTMLElement;
      recaptcha.style.visibility = 'visible';
    }, 2000);
  }

  ngOnDestroy () {
    const result = document.getElementsByClassName('grecaptcha-badge');
    const recaptcha = result.item(0) as HTMLElement;
    recaptcha.style.visibility = 'hidden';

    if (this.submitExecutionSubscription) {
      this.submitExecutionSubscription.unsubscribe();
    }
  }

  private createFormControls () {
    this.subject = new UntypedFormControl('');
    this.firstName = new UntypedFormControl('', Validators.required);
    this.lastName = new UntypedFormControl('', Validators.required);
    this.phoneNumber = new UntypedFormControl('', [
      Validators.required,
      Validators.pattern('[0-9]+'),
      Validators.minLength(10)
    ]);
    this.email = new UntypedFormControl('', [
      Validators.required,
      Validators.pattern('^\\w+([\\.-]?\\w+)*@\\w+([\\.-]?\\w+)*(\\.\\w{2,3})+$')
    ]);
    this.phoneExt = new UntypedFormControl('');
    this.message = new UntypedFormControl('', Validators.required);
  }

  private createForm () {
    this.contactForm = this.formBuilder.group({
      name: new UntypedFormGroup({
        firstName: this.firstName,
        lastName: this.lastName
      }),
      phone: new UntypedFormGroup({
        phoneNumber: this.phoneNumber,
        phoneExt: this.phoneExt
      }),
      email: this.email,
      message: this.message,
      subject: this.subject
    });
  }

  onSubmit () {
    if (!this.contactForm.valid) return false;
    this.formLoading = true;

    this.submitExecutionSubscription = this.recaptchaV3Service.execute('submit').subscribe(token => {
      this.contactForm.value.subject = this.formSubject ? this.formSubject : this.translate.instant('library.broker-contact-form.subject');
      this.contactForm.value.token_captcha = token;

      this.contactService.postContact(this.contactForm.value).subscribe(response => {
        this.formSend = true;
        this.formLoading = false;

        if (response.success) this.successMessage = true;
        else this.errorMessage = true;
      });
    },
    error => console.error(error));
  }

  resetForm () {
    this.formSend = false;
    this.contactForm.reset();
  }

  retry () {
    this.formSend = false;
  }
}
