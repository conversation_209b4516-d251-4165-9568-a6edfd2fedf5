<div class="blog-list-cpn blog-list-3 -space-default">
	<div class="container">

		<div class="blog-list-3-head -page-head">
			<h1 class="page-title -center">{{ 'library.blog-list-3.title' | translate }}</h1>

			<ul class="sortable-list tabs -center">
				<input class="input-tab" id="tab1" type="radio" name="tabs" checked>
				<label (click)="onCategoryChange('')" for="tab1">{{ 'library.blog-list-3.all' | translate }}</label>
        
				<ng-container *ngFor="let category of blogCategories">
					<input class="input-tab" id="tab-{{category.slug}}" type="radio" name="tabs">
					<label (click)="onCategoryChange(category.slug)" for="tab-{{category.slug}}">{{category.name}}</label>
				</ng-container>
			</ul>
		</div>

		<div class="article-list-ctn grid -full" id="blog-list">
			<div itemscope itemtype="http://schema.org/Blog" *ngFor="let blogPost of blogPosts | orderBy: '-publication_date' | filterBy: ['category_slug']: currentCategory | paginate: { id: 'blog-pagination', itemsPerPage: limitPerPage, currentPage: cPage, totalItems:totalLength }" class="article col-t-lg-4 col-t-6 col-12">
				<lib-article-2 itemprop="blogPost" [blogPost]="blogPost"></lib-article-2>
			</div>
		</div>

		<pagination-controls id="blog-pagination" class="paginiation-controls"
			(pageChange)="onPageChange($event)"
			maxSize="5"
			directionLinks="true"
			autoHide="true"
			previousLabel=""
			nextLabel="">
		</pagination-controls>

	</div>
</div>
