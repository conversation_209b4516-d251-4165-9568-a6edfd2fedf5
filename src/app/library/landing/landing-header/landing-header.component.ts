import { Component, OnInit, EventEmitter, Output, Input } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'lib-landing-header',
  templateUrl: './landing-header.component.html'
})

export class LandingHeaderComponent implements OnInit {
  @Output() switchLang: EventEmitter<any> = new EventEmitter<any>();
  @Input() showSwitchLang; 
  @Input() translateUrl; 

  constructor (
    public translate: TranslateService
  ) {}

  ngOnInit () {
  }

  translateCampaign () {
    let url = this.switchLang.emit(true);
  }

}
