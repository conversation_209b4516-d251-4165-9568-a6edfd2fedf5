<div class="properties-slider-cpn" *ngIf="properties?.length">
	<div class="container">

		<h2>{{ 'library.properties-slider.title' | translate }}</h2>

		<div class="swiper-container properties-list-ctn">
			<div class="swiper-wrapper properties-list-inner">

				<div class="swiper-slide properties" *ngFor="let property of properties;">
					<lib-properties [property]="property"></lib-properties>
				</div>

			</div>
		</div>

    <a [routerLink]="'urls.search-properties' | translate" class="small-link right">
      {{ 'library.properties-slider.all' | translate }} <i class="icon-arrow-right"></i>
    </a>
		
		<div class="nav-ctn">
			<a class="main-button prev"><i class="icon-arrow-left"></i></a>
			<a class="main-button next"><i class="icon-arrow-right"></i></a>
		</div>

	</div>
</div>
