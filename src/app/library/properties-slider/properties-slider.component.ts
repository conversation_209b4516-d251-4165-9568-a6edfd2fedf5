import { Component, OnInit, Input } from '@angular/core';
import Swiper from 'swiper';
import { Navigation } from 'swiper/modules';

@Component({
  selector: 'lib-properties-slider',
  templateUrl: './properties-slider.component.html'
})

export class PropertiesSliderComponent implements OnInit {
  @Input() properties;
  propertiesSwiper: any;

  constructor () { }

  ngOnInit () {
    this.initSlider();
  }

  initSlider () {
    Swiper.use([Navigation]);

    setTimeout(() => {
      this.propertiesSwiper = new Swiper('.properties-slider-cpn .swiper-container', {
        speed: 1000,
        loop: true,
        observer: true,
        navigation: {
          nextEl: '.nav-ctn .next',
          prevEl: '.nav-ctn .prev'
        },
        slidesPerView: 1,
        spaceBetween: 0,
        breakpoints: {
          1024: {
            slidesPerView: 3,
            spaceBetween: 20
          },
          768: {
            slidesPerView: 2,
            spaceBetween: 10
          }
        }
      });
    }, 1000);
  }
}
