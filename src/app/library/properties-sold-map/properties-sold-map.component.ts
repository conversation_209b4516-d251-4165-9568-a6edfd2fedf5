import { Component, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { InscriptionsService } from '@/services/v3/inscriptions/inscriptions.service';
import { MapService } from '@/services/v3/map/map.service';

@Component({
  selector: 'lib-properties-sold-map',
  templateUrl: './properties-sold-map.component.html'
})

export class PropertiesSoldMapComponent implements OnInit {
  propertiesSold: any;
  map: any;
  mapColor: string;
  source: any;
  looped: boolean = false;
  areas: any;

  constructor (
    private inscriptionsService: InscriptionsService,
    private translate: TranslateService,
    private mapService: MapService
  ) {
    this.inscriptionsService.getInscriptionSold().subscribe(({ data }) => {
      this.propertiesSold = data;
      if (this.propertiesSold.length) setTimeout(() => this.initMap(), 200);
    });
  }

  async ngOnInit () {
    // Fetch map color from client infos
    const { color } = await this.translate.get('client.map').toPromise();
    this.mapColor = color;
  }

  initMap () {
    if (this.looped) return;

    // Create map object
    this.map = this.mapService.createMap();

    this.map.on('load', () => {
      const features = this.propertiesSold.map(p => ({ type: 'Feature', geometry: { type: 'Point', coordinates: [p.lng, p.lat] } }));
      this.map.addSource('properties', {
        type: 'geojson',
        data: { type: 'FeatureCollection', features },
        cluster: true
      });

      // Add clusters icons
      this.map.addLayer({
        id: 'clusters',
        type: 'circle',
        source: 'properties',
        filter: ['has', 'point_count'],
        paint: { 'circle-color': this.mapColor, 'circle-radius': 18, 'circle-stroke-width': 6, 'circle-stroke-color': this.mapColor, 'circle-stroke-opacity': 0.5 }
      });

      // Add clusters count
      this.map.addLayer({
        id: 'cluster-count',
        type: 'symbol',
        source: 'properties',
        filter: ['has', 'point_count'],
        layout: {
          'text-field': '{point_count_abbreviated}',
          'text-font': ['Arial Unicode MS Bold'],
          'text-size': 14
        },
        paint: { 'text-color': '#ffffff' }
      });

      // Add property points
      this.map.addLayer({
        id: 'p',
        type: 'circle',
        source: 'properties',
        filter: ['!', ['has', 'point_count']],
        paint: { 'circle-color': this.mapColor, 'circle-radius': 10, 'circle-stroke-width': 4, 'circle-stroke-color': this.mapColor, 'circle-stroke-opacity': 0.5 }
      });

      // Open cluster on click
      this.map.on('click', 'clusters', e => {
        const features = this.map.queryRenderedFeatures(e.point, { layers: ['clusters'] });
        const clusterId = features[0].properties.cluster_id;

        this.map.getSource('properties').getClusterExpansionZoom(clusterId, (err, zoom) => {
          if (!err) this.map.easeTo({ center: features[0].geometry.coordinates, zoom: zoom });
        });
      });

      // Cluster hover event
      this.map.on('mouseenter', 'clusters', () => { this.map.getCanvas().style.cursor = 'pointer'; });
      this.map.on('mouseleave', 'clusters', () => { this.map.getCanvas().style.cursor = ''; });

      // Resize map to include all properties
      const bounds = this.mapService.getBoundaries(features);
      this.map.fitBounds(bounds, { padding: 100, linear: true });

      this.looped = true;
    });
  }
}
