import { Component, OnInit, Input } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'lib-tool-evaluation',
  templateUrl: './tool-evaluation.component.html'
})

export class ToolEvaluationComponent implements OnInit {
  @Input() property;

  public currentLang: any;
  public chartOptions = { responsive: true };
  public chartLabels: string[] = [];
  public chartColors: any[] = [{ backgroundColor: ['#303030', '#008060'] }];

  public chartClicked (e: any): void {
    // console.log(e);
  }

  public chartHovered (e: any): void {
    // console.log(e);
  }

  constructor (
    public translateService: TranslateService
  ) {
    const userLang = window.location.pathname.split('/')[1];
    this.currentLang = userLang || 'fr';

    this.translateService.get('library.property-tools.tool-evaluation.chart-labels').subscribe(res => {
      this.chartLabels = [res.plot, res.building];
    });
  }

  ngOnInit () {
  }
}
