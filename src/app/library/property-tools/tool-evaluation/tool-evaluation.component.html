<div class="dual-wrap">
	<div class="stats-wrap">
		<div class="stats">
			<p class="name">{{ 'library.property-tools.tool-evaluation.year' | translate }}</p>
			<p class="value" *ngIf="property.evaluation_year">{{property.evaluation_year }}</p>
			<p class="value" *ngIf="!property.evaluation_year">N/D</p>
		</div>
		<div class="stats">
			<p class="name">{{ 'library.property-tools.tool-evaluation.plot' | translate }}</p>
			<p class="value" *ngIf="property.evaluation_plot">{{property.evaluation_plot | currency:'CAD':'symbol-narrow':'2.0-0':this.currentLang}}</p>
			<p class="value" *ngIf="!property.evaluation_plot">N/D</p>
		</div>
		<div class="stats">
			<p class="name">{{ 'library.property-tools.tool-evaluation.building' | translate }}</p>
			<p class="value" *ngIf="property.evaluation_building">{{property.evaluation_building | currency:'CAD':'symbol-narrow':'2.0-0':this.currentLang}}</p>
			<p class="value" *ngIf="!property.evaluation_building">N/D</p>
		</div>
		<div class="stats">
			<p class="name">Total</p>
			<p class="value" *ngIf="property.total_eval">{{property.total_eval | currency:'CAD':'symbol-narrow':'2.0-0':this.currentLang}}</p>
			<p class="value" *ngIf="!property.total_eval">N/D</p>
		</div>

		<div class="stats">
			<p class="name">{{ 'library.property-tools.tool-evaluation.tax' | translate }}</p>
			<p class="value" *ngIf="property.ext_mutation_tax">
				{{property.ext_mutation_tax | currency:'CAD':'symbol-narrow':'2.0-0':this.currentLang}}</p>
			<p class="value" *ngIf="!property.ext_mutation_tax">N/D</p>
		</div>

		<!-- <div class="stats">
			<p class="name">{{ 'library.property-tools.tool-evaluation.total' | translate }}</p>
			<p class="value" *ngIf="property.ext_total_price">
				{{property.ext_total_price | currency:'CAD':'symbol-narrow':'2.0-0':this.currentLang}}</p>
			<p class="value" *ngIf="!property.ext_total_price">N/D</p>
		</div> -->
	</div>
	<div class="graph-ctn">
		<ng-container *ngIf="property.evaluation_plot && property.evaluation_building">
			<!-- <canvas baseChart
			  	[chartType]="'doughnut'"
			    [data]="[property.evaluation_plot, property.evaluation_building]"
			    [labels]="chartLabels"
			    [options]="chartOptions"
			    [colors]="chartColors"
			    [legend]="true"
			    (chartHover)="chartHovered($event)"
			    (chartClick)="chartClicked($event)">
			</canvas> -->
			<!-- todo : but seems to not be used anymore -->
		</ng-container>    
	</div>
</div>
