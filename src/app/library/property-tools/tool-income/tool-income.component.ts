import { Component, OnInit, Input } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'lib-tool-income',
  templateUrl: './tool-income.component.html'
})

export class ToolIncomeComponent implements OnInit {
  @Input() property;

  public currentLang: any;
  public chartOptions = {
    responsive: true
  };

  public chartLabels: string[] = [];
  public chartColors: any[] = [{ backgroundColor: ['#008060', '#005741', '#003125', '#303030'] }];

  public chartClicked (e: any): void {
    // console.log(e);
  }

  public chartHovered (e: any): void {
    // console.log(e);
  }

  constructor (public translateService: TranslateService) {
    const userLang = window.location.pathname.split('/')[1];
    this.currentLang = userLang || 'fr';

    this.translateService.get('library.property-tools.tool-income.chart-labels').subscribe(res => {
      this.chartLabels = [
        res.residential,
        res.commercial,
        res['car-park'],
        res.other
      ];
    });
  }

  ngOnInit () {
  }
}
