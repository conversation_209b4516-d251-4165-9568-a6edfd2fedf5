import { Component, OnInit, Input } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

import { InscriptionsService } from '@/services/v3/inscriptions/inscriptions.service';

@Component({
  selector: 'lib-tool-expenses',
  templateUrl: './tool-expenses.component.html'
})

export class ToolExpensesComponent implements OnInit {
  @Input() property;

  public currentLang: any;
  expenses: any;
  salesprice: any;
  payments: any;
  interestRate: any;
  cashdown: any;
  mortgageLength: any;
  CAPITALISATION: number = 2;
  VERSEMENTS: number = 12;
  public mortgage: any[];
  public chartOptions = {
    responsive: true
  };

  chartLabels: any[] = [];
  chartData: any[] = [];
  chartColors: any[] = [{ backgroundColor: ['#008060', '#005741', '#003125'] }];

  public chartClicked (e: any): void {
    // console.log(e);
  }

  public chartHovered (e: any): void {
    // console.log(e);
  }

  constructor (
    public translateService: TranslateService,
    private inscriptionsService: InscriptionsService
  ) {
    const userLang = window.location.pathname.split('/')[1];
    this.currentLang = userLang || 'fr';

    this.translateService.get('library.property-tools.tool-expenses.mortgage-years').subscribe(res => {
      this.mortgage = res;
    });
  }

  ngOnInit () {
    this.salesprice = parseInt(this.property.price_sale);
  }

  ngAfterViewInit () {
    this.getExpenses();
  }

  getExpenses () {
    const scope = this;

    this.inscriptionsService.getExpenses(this.property.mls).subscribe(data => {
      this.expenses = data.data;

      for (const e in this.expenses) {
        const expense = this.expenses[e];
        scope.chartLabels.push(expense.expense_type);
        scope.chartData.push(expense.expense_amount);
      }
    });
  }

  calcPayment () {
    if (this.salesprice && this.cashdown && this.interestRate && this.mortgageLength) {
      const x = parseFloat(this.interestRate) / (100 * this.CAPITALISATION);
      const y = this.CAPITALISATION / this.VERSEMENTS;
      const interestPeriod = Math.pow((x + 1), y) - 1;
      const mount = parseInt(this.salesprice) - parseInt(this.cashdown);

      const result = (mount / ((1 - Math.pow((1 + interestPeriod), -(this.mortgageLength * this.VERSEMENTS))) / interestPeriod)).toFixed(2);

      if (parseInt(result) <= 0) {
        this.payments = '0';
      } else {
        this.payments = String(result);
      }
    } else {
      this.payments = undefined;
    }
  }
}
