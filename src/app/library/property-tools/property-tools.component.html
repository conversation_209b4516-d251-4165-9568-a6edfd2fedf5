<div class="property-tools-cpn" *ngIf="property.price_sale && property.status == 0">
	<div class="tabs-ctn" [ngClass]="{'-width-3': property.potential_income_total != null}">
		<input class="input-tab" id="tab-input1" type="radio" name="tabs" checked>
		<label class="tab-label" for="tab-input1">{{ 'library.property-tools.tab-label1' | translate }}</label>
		<input class="input-tab" id="tab-input2" type="radio" name="tabs">
		<label class="tab-label -no-print -no-border-2" for="tab-input2">{{ 'library.property-tools.tab-label2' | translate }}</label>
		<input class="input-tab" id="tab-input3" type="radio" name="tabs" *ngIf="property.potential_income_total">
		<label *ngIf="property.potential_income_total" class="tab-label -no-print -no-border" for="tab-input3"><span class="icon-round-dollar"></span>{{ 'library.property-tools.tab-label3' | translate }}</label>


		<div id="tab-tool1" class="tab-content">
			<lib-tool-evaluation [property]='property'></lib-tool-evaluation>
		</div>
		<div id="tab-tool2" class="tab-content -no-print">
			<lib-tool-expenses [property]=property></lib-tool-expenses>
		</div>

		<div id="tab-tool3" class="tab-content -no-print" *ngIf="property.potential_income_total">
			<lib-tool-income [property]=property></lib-tool-income>
		</div>
	</div>
</div>
