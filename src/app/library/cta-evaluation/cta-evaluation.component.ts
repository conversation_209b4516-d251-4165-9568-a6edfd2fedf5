import { Component, OnInit } from '@angular/core';
import { BlocksService } from '@/services/v3/contentblocks/contentblocks.service';

@Component({
  selector: 'lib-cta-evaluation',
  templateUrl: './cta-evaluation.component.html'
})

export class CtaEvaluationComponent implements OnInit {
  public blockTitle: string;
  public blockContent: string;
  constructor (
    private blocksService: BlocksService
  ) {
    this.blocksService.getBlock('bloc-evaluation-immobiliere').subscribe(data => {
      this.blockTitle = data.title;
      this.blockContent = data.text;
    });
  }

  ngOnInit () {
  }
}
