import { Component, OnInit, Input } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { MapService } from '@/services/v3/map/map.service';

@Component({
  selector: 'lib-neighborhood-map-2',
  templateUrl: './neighborhood-map-2.component.html'
})

export class NeighborhoodMap2Component implements OnInit {
  @Input() neighborhood;

  map: any;
  mapColor: string;
  hoveredFeatureId = null;

  constructor (
    private mapService: MapService,
    private translate: TranslateService
  ) {}

  async ngOnInit () {
    // Fetch map color from client infos
    const { color } = await this.translate.get('client.map').toPromise();
    this.mapColor = color;
  }

  setupHoverListeners() {
    const cards = document.querySelectorAll('.neighborhood-card');
    
    cards.forEach(card => {
      card.addEventListener('mouseenter', (e: any) => {
        const id = e.target.getAttribute('data-id');
        const feature = this.map?.querySourceFeatures('quartiers-data')
          .find(f => f.properties.id === id);

        if (feature) {
          if (this.hoveredFeatureId !== null) {
            this.map.setFeatureState(
              { source: 'quartiers-data', id: this.hoveredFeatureId },
              { hover: false }
            );
          }
          this.hoveredFeatureId = feature.id;
          this.map.setFeatureState(
            { source: 'quartiers-data', id: this.hoveredFeatureId },
            { hover: true }
          );
        }
      });

      card.addEventListener('mouseleave', () => {
        if (this.hoveredFeatureId !== null) {
          this.map.setFeatureState(
            { source: 'quartiers-data', id: this.hoveredFeatureId },
            { hover: false }
          );
          this.hoveredFeatureId = null;
        }
      });
    });
  }

  async initMap () {
    // Create map object
    this.map = this.mapService.createMap();

    // Render map and add events
    this.map.on('load', () => {
      // Attach map geojson data
      this.map.addSource('quartiers-data', { type: 'geojson', data: this.neighborhood.geoJSON, generateId: true });

      // Build polygons using conditionnal opacity with transition
      this.map.addLayer({
        id: 'quartiers',
        type: 'fill',
        source: 'quartiers-data',
        paint: {
          'fill-color': this.mapColor,
          'fill-opacity': [
            'case',
            ['boolean', ['feature-state', 'hover'], false], 0.8,
            ['boolean', ['feature-state', 'selected'], false], 0.6,
            0.3
          ],
          'fill-opacity-transition': {
            duration: 300,
            delay: 0
          }
        }
      });

      // Cursor styling
      this.map.on('mouseenter', 'quartiers', () => { this.map.getCanvas().style.cursor = 'pointer'; });
      this.map.on('mouseleave', 'quartiers', () => { this.map.getCanvas().style.cursor = ''; });

      // Center map on neighborhood
      const bounds = this.mapService.getBoundaries(this.neighborhood.geoJSON.features);
      this.map.fitBounds(bounds, { padding: 50 });

      this.setupHoverListeners();
    });
  }
}
