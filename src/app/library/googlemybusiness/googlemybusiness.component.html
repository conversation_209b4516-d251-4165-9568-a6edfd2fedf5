<div class="reviews-slider-cpn" *ngIf="reviews?.length">
    <div class="container">
        <h2 class="title -center -flex-center">{{ "library.reviews.title" | translate }}</h2>

        <div class="swiper-container reviews-list-ctn -space-default">
            <div class="swiper-wrapper reviews-list-inner">

                <div class="swiper-slide review" *ngFor="let review of reviews">
                    <div class="review-header">
                        <div class="review-header-left">
                            <img class="reviewer-image" src="{{review.reviewer.profilePhotoUrl}}" alt="Reviewer Image">
                        </div>
                        <div class="review-header-right">
                            <div class="reviewer-name">{{review.reviewer.displayName}}</div>
                            <div class="reviewer-rating">
                                <div class="rating-stars">
                                    <i *ngFor="let star of [1, 2, 3, 4, 5]" class="icon-star" [ngClass]="{'checked': star <= getNumericRating(review.starRating)}"></i>
                                </div>
                                <div class="rating-value">{{review.rating}}</div>
                            </div>
                            <div class="reviewer-date">{{ formatDate(review.createTime) }}</div>
                        </div>
                    </div>
                    <div class="review-body">
                        <div class="review-text">{{ filterText(review.comment) }}</div>
                    </div>
                </div>
            </div>

            <div class="swiper-button-prev swiper-btn"><i></i></div>
            <div class="swiper-button-next swiper-btn"><i></i></div>

            <div class="swiper-pagination"></div>
        </div>
    </div>
</div>

