import { Component, OnInit } from '@angular/core';
import { GooglemybusinessService } from '@/services/v3/googlemybusiness/googlemybusiness.service';
import Swiper from 'swiper';
import { Navigation } from 'swiper/modules';

@Component({
  selector: 'lib-googlemybusiness',
  templateUrl: './googlemybusiness.component.html'
})

export class GooglemybusinessComponent implements OnInit {
    reviews: any = {};
  private reviewsSwiper: Swiper;
  constructor (
    private googlemybusiness: GooglemybusinessService
  ) { }

  ngOnInit () {
    this.googlemybusiness.getGoogleMyBusiness().subscribe(({ data }) => {
      const allReviews = JSON.parse(data.google_my_business_reviews);
      this.reviews = allReviews.reviews;
    });
    this.initSlider();
  }

  initSlider () {
    Swiper.use([Navigation]);

    setTimeout(() => {
      this.reviewsSwiper = new Swiper('.reviews-slider-cpn .swiper-container', {
        speed: 1000,
        loop: true,
        observer: true,
        navigation: {
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev'
        },
        slidesPerView: 3,
        spaceBetween: 0,
        breakpoints: {
          1024: {
            slidesPerView: 3,
            spaceBetween: 20
          },
          768: {
            slidesPerView: 2,
            spaceBetween: 10
          }
        }
      });
    }, 1000);
  }

  getNumericRating(rating: string): number {
    const ratingMap: { [key: string]: number } = {
      'ONE': 1,
      'TWO': 2,
      'THREE': 3,
      'FOUR': 4,
      'FIVE': 5
    };
    return ratingMap[rating] || 0; // Retourne 0 par défaut si la valeur n'est pas trouvée
  }

  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-CA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }

  filterText (text: string): string {
    if (!text) {
      return '';
    }
    const index = text.indexOf('(Translated by Google)');
    return index !== -1 ? text.substring(0, index).trim() : text;
  }
}
