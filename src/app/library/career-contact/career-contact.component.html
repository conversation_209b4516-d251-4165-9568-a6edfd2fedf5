<div id="career-contact" class="career-contact-cpn">
	<div class="container -small">
		<div class="form-container" [ngClass]="{'send': formSend}">
			<form [formGroup]="careerForm" [ngClass]="{'loading-inner': formLoading}" id="careerForm" class="contact-form-ctn" (ngSubmit)="onSubmit()">

				<div class="form-head">
					<h1 class="-page-title">{{ blockTitle }}</h1>
					<div class="step">
						<p>{{ currentStep }} / 2</p>
					</div>
				</div>

				<div id="step1" class="form-step" [class.show]="currentStep == 1">
					<div class="-page-description" [innerHtml]="blockContent"></div>
					<p class="-page-required">{{ "library.career-contact.required" | translate }}</p>

					<h3 class="-emphase-title">{{ "library.career-contact.subtitle1" | translate }}</h3>

					<div class="form-row">
						<div class="input-ctn -dual datepicker-ctn">
							<label>{{ 'library.career-contact.step1.mydate-label' | translate }}</label>
							<dp-date-picker formControlName="mydate" [config]="datePickerConfig"></dp-date-picker>  

							 <div class="form-control-feedback" *ngIf="mydate.errors && (mydate.dirty || mydate.touched)">
								 <p *ngIf="mydate.errors.required">{{ "library.career-contact.errors.mydate_required" | translate }}</p>
							 </div>
						</div>

						<div class="input-ctn -dual">
							<label>{{ 'library.career-contact.step1.poste-type-label' | translate }}</label>
							<ng-select
							[searchable]="false"
							[items]="roles"
							bindLabel="name"
							bindValue="name"
							placeholder="{{ 'library.career-contact.step1.select' | translate }}" class="rooms-filter" formControlName="role">
							</ng-select>
							<div class="form-control-feedback" *ngIf="role.errors && (role.dirty || role.touched)">
								<p *ngIf="role.errors.required">{{ "library.career-contact.errors.role_required" | translate }}</p>
							</div>
						</div>
					</div>

					<div class="form-row">
						<div class="input-ctn -half">
							<label class="block">{{ 'library.career-contact.step1.cv-label' | translate }}</label>
							<label id="label-file-upload" for="file-upload" class="main-button -primary-small">{{ 'library.career-contact.step1.upload' | translate }}</label>
							<input class="-hide" id="file-upload" type="file" name="file" ng2FileSelect formControlName="fileInput" [uploader]="uploader" (change)="onFileChange($event)" accept=
								"application/pdf, application/msword, application/vnd.openxmlformats-officedocument.wordprocessingml.document,text/plain,image/*"/>
							<div class="form-control-feedback" *ngIf="fileInput.errors && (fileInput.dirty || fileInput.touched)">
								<p *ngIf="fileInput.errors.required">{{ "library.career-contact.errors.role_required" | translate }}</p>
							</div>
						</div>
					</div>

					<div class="form-row">
						<div class="input-ctn">
							<label>{{ 'library.career-contact.step1.message-label' | translate }}</label>
							<textarea name="message" formControlName="message"></textarea>
							<div class="form-control-feedback" *ngIf="message.errors && (message.dirty || message.touched)">
								<p *ngIf="message.errors.required">{{ "library.career-contact.errors.message_required" | translate }}</p>
							</div>
						</div>
					</div>

					<div class="button-ctn">
						<button class="main-button -primary" [disabled]="mydate.errors || role.errors || message.errors || fileInput.errors" (click)="onNextStep($event)">{{ 'library.career-contact.next' | translate }}</button>
					</div>
				</div>

				<div id="step2" class="form-step" [class.show]="currentStep == 2">
					<h3 _ngcontent-c22="" class="-emphase-title">{{ "library.career-contact.subtitle2" | translate }}</h3>

					<div class="form-row">
						<div class="input-ctn -dual">
							<label>{{ 'library.career-contact.step2.firstname' | translate }}</label>
							<input type="text" class="form-control" [ngClass]="{'-error': firstName.invalid && (firstName.dirty || firstName.touched)}" formControlName="firstName" name="first_name">
							<div class="form-control-feedback" *ngIf="firstName.errors && (firstName.dirty || firstName.touched)">
								<p>{{ "library.career-contact.errors.firstname_required" | translate }}</p>
							</div>
						</div>
						<div class="input-ctn -dual">
							<label>{{ 'library.career-contact.step2.lastname' | translate }}</label>
							<input type="text" class="form-control" [ngClass]="{'-error': lastName.invalid && (lastName.dirty || lastName.touched)}" formControlName="lastName" name="last_name">
							<div class="form-control-feedback"
							*ngIf="lastName.errors && (lastName.dirty || lastName.touched)">
								<p *ngIf="lastName.errors.required">{{ "library.career-contact.errors.lastname_required" | translate }}</p>
							</div>
						</div>
					</div>
					<div class="form-row">
						<div class="input-ctn -dual">
							<label>{{ 'library.career-contact.step2.address' | translate }}</label>
							<input type="text" name="address" formControlName="address">
						</div>
						<div class="input-ctn -dual">
							<label>{{ 'library.career-contact.step2.city' | translate }}</label>
							<input type="text" name="city" formControlName="city">
						</div>
					</div>
					<div class="form-row -bottom">
						<div class="input-ctn -dual">
							<label>{{ 'library.career-contact.step2.phone' | translate }}</label>
							<input type="text" class="form-control" mask="************" [ngClass]="{'-error': phone.invalid && (phone.dirty || phone.touched)}" formControlName="phone" name="phone">
						</div>
						<div class="input-ctn -dual -small">
							<label></label>
							<input type="text" name="ext" class="form-control" formControlName="phoneExt"  placeholder="Ext">
						</div>
						<div class="form-control-feedback" *ngIf="phone.errors && (phone.dirty || phone.touched)">
							<p *ngIf="phone.errors.required">{{ "library.career-contact.errors.phone_required" | translate }}</p>
							<p *ngIf="phone.errors.pattern">{{ "library.career-contact.errors.phone_invalid" | translate }}</p>
							<p *ngIf="phone.errors.minlength">{{ "library.career-contact.errors.phone_invalid" | translate }}</p>
						</div>
					</div>
					<div class="form-row">
						<div class="input-ctn">
							<label>{{ 'library.career-contact.step2.email' | translate }}</label>
							<input type="email" class="form-control" (focus)="isTyping = true" (blur)="isTyping = false" [ngClass]="{'-error': email.invalid && (email.dirty || email.touched) && !isTyping}" formControlName="email" name="email">
							<div class="form-control-feedback" *ngIf="email.errors && (email.dirty || email.touched) && !isTyping">
								<p *ngIf="email.errors.required">{{ "library.career-contact.errors.email_required" | translate }}</p>
								<p *ngIf="email.errors.pattern">{{ "library.career-contact.errors.email_invalid" | translate }}</p>
							</div>
						</div>
					</div>

					<div class="button-ctn">
						<a class="main-button -previous" (click)="onPreviousStep($event)">{{ 'library.career-contact.return' | translate }}</a>
						<!-- <a class="main-button -primary" (click)="onSubmit($event)">{{ 'library.career-contact.submit' | translate }}</a> -->
						<button class="main-button -primary" [disabled]="!careerForm.valid" type="submit"><span>{{ "library.career-contact.submit" | translate }}</span></button>
					</div>
					<p class="-page-required -page-required--right">{{ "library.career-contact.required" | translate }}</p>
				</div>

			</form>

			<div *ngIf="formLoading" class="form-loader">
				<div class="loading-circle"></div>
			</div>

			<div class="form-response" [ngClass]="{'show': formSend}">
				<h1 class="-page-title">{{ 'library.career-contact.title' | translate }}</h1>
				<ng-container *ngIf="successMessage">
					<p class="message">{{ "library.career-contact.successMessage" | translate }}</p>
					<div class="button-ctn">
						<a class="main-button -primary" (click)="resetForm()">{{ 'library.career-contact.back' | translate }}</a>
					</div>
				</ng-container>
				<ng-container *ngIf="errorMessage">
					<p class="message">{{ "library.career-contact.errorMessage" | translate }}</p>
					<div class="button-ctn">
						<a class="main-button -primary" (click)="retry()">{{ 'library.career-contact.back' | translate }}</a>
					</div>
				</ng-container>
			</div>
		</div>
	</div>
</div>
