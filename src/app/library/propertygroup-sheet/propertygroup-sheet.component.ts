import { Component, OnInit, Input, SimpleChanges } from '@angular/core';

@Component({
  selector: 'lib-propertygroup-sheet',
  templateUrl: './propertygroup-sheet.component.html'
})

export class PropertyGroupSheetComponent implements OnInit {
  @Input() propertygroup;
  public detectMobile: boolean;

  photos = [];
  tempPhotos = [];

  constructor () {
    const width = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;
    if (width <= 992) this.detectMobile = true;
  }

  ngOnInit () {
  }

  ngOnChanges (changes: SimpleChanges) {
    if (changes.propertygroup.currentValue) {
      this.tempPhotos = this.propertygroup.medias.reduce((photos, photo) => {
        photos.push(photo);
        return photos;
      }, []);
    }
  }

  // Calculate image height to split them between columns
  computeImgHeight ({ target }, { id }) {
    this.tempPhotos.find(p => p.id === id).height = target.height;

    // If all photos heights are processed
    if (this.tempPhotos.reduce((photos, photo) => photo.height ? [...photos, photo] : photos, []).length === this.tempPhotos.length) {
      // Distribute photos amongst columns based on height
      this.photos = this.tempPhotos.reduce((columns, photo) => {
        const heights = columns.map(c => c.reduce((sum, p) => sum + p.height, 0));
        columns[heights.indexOf(Math.min(...heights))].push(photo);
        return columns;
      }, [[], []]);
    }
  }
}
