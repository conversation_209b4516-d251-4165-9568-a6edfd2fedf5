<div class="property-favorite-cpn">
	<!-- 
		favoriteList is the currentArray fetched from the Local Storage

		filterBy is a pipe which return the object in json form
		Exemple: "[{'id: 324231'}]"

		search is an optional pipe, it make sure it only return the value/s which exactly fit the params passed

		Without Strict
		array = "[{'id: 11'}, {'id: 1321'}, {'id: 1411'}]"
		filterBy: ['id']: 11
		return: "[{'id: 11'}, {'id: 1411'}]"

		With Strict
		array = "[{'id: 11'}, {'id: 1321'}, {'id: 1411'}]"
		filterBy: ['id']: 11: search: strict 
		return: "[{'id: 11'}]"
	-->
    <a *ngIf="( favoriteList | filterBy: ['id']: mls | json ) == '[]'" class="main-button -favorite" href="" (click)="saveItem($event, mls)"><i class="icon-favorite"></i> {{ 'library.property-favorite.save-item' | translate }}</a>
    <a *ngIf="( favoriteList | filterBy: ['id']: mls | json ) != '[]'" class="main-button -favorite" href="" (click)="clearItem($event, mls)"><i class="icon-favorite"></i> {{ 'library.property-favorite.remove-item' | translate }}</a>

	<!-- 
    	Decorators are based upon accessors so the update trigger only on assignation. 
    	Consequence, if you change the value of a bound object's property the new model will not be updated properly.

    	In another words, ngIf is aplicated on load when filtered in ngX as it's been depriciated since they used to perform poorly.

    	So here we create 2 copies of button always hided and shown on saveItem or clearItem callback
    -->

    <a class="add-favorite-button main-button -favorite -hide -disabled" href="" (click)="saveItem($event, mls)"><i class="icon-favorite"></i> {{ 'library.property-favorite.save-item' | translate }}</a>
    <a class="remove-favorite-button main-button -favorite -hide -disabled" href="" (click)="clearItem($event, mls)"><i class="icon-favorite"></i> {{ 'library.property-favorite.remove-item' | translate }}</a>
</div>