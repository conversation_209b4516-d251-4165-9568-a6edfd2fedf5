import { Component, OnInit, Input } from '@angular/core';
import { LocalStorageService } from 'ngx-webstorage';

import { FavoritesService } from '@/services/v3/favorites/favorites.service';

@Component({
  selector: 'lib-property-favorite',
  templateUrl: './property-favorite.component.html'
})

export class PropertyFavoriteComponent implements OnInit {
  @Input() mls;

  public favoriteList: any[] = [];
  public firstTime: boolean = true

  constructor (
    private storage: LocalStorageService,
    private favoritesService: FavoritesService
  ) { }

  ngOnInit () {
    // Retrive the favoriteList from localStorage return empty array if null.
    this.retrieveItem();

    // This is for debuging console log every add/remove in the localStorage
    // this.storage.observe('propertiesFavorite').subscribe((value) => console.log('new item', value));
  }

  saveItem ($event, propertyId) {
    $event.preventDefault();

    const addButton = $event.currentTarget.parentNode.querySelectorAll('.add-favorite-button')[0];

    if (addButton.classList.contains('-disabled') && !addButton.classList.contains('-hide')) return;

    // If never had favorite just add value in array
    if (this.favoriteList.length === 0) {
      this.favoriteList.push({ id: propertyId });
    }

    // Push in array if not already in the array
    // Fallback if button didn't correctly change by corrupted LocalStorage
    if (this.favoriteList.some(e => e.id !== propertyId)) {
      this.favoriteList.push({ id: propertyId });
    }

    const numberOfFavorite = this.favoriteList.length;
    const favoriteNumberElement = document.getElementById('favorite-number');
    if (numberOfFavorite <= 0) favoriteNumberElement.textContent = String(numberOfFavorite);

    // Add/Overwrite to localStorage the new array
    this.storage.store('propertiesFavorite', this.favoriteList);

    $event.currentTarget.classList.add('-hide');

    const removeButton = $event.currentTarget.parentNode.querySelectorAll('.remove-favorite-button')[0];
    removeButton.classList.remove('-hide');

    this.favoritesService.addFavorite();
    this.firstTime = false;
  }

  retrieveItem () {
    // Return the localStorage array
    if (this.storage.retrieve('propertiesFavorite') === null) {
      this.favoriteList = [];
      this.storage.store('propertiesFavorite', this.favoriteList);
    } else {
      this.favoriteList = this.storage.retrieve('propertiesFavorite');
    }
  }

  clearItem ($event, propertyId) {
    $event.preventDefault();

    const removeButton = $event.currentTarget.parentNode.querySelectorAll('.remove-favorite-button')[0];
    if (removeButton.classList.contains('-disabled') && !removeButton.classList.contains('-hide')) return;

    // Return the array without the propertyId object
    const removedItem = this.favoriteList.some(e => e.id === propertyId) ? this.favoriteList.filter(el => el.id !== propertyId) : null;
    // Add/Overwrite to localStorage the new array
    this.storage.store('propertiesFavorite', removedItem);

    $event.currentTarget.classList.add('-hide');
    const addButton = $event.currentTarget.parentNode.querySelectorAll('.add-favorite-button')[0];
    addButton.classList.remove('-hide');
    this.favoritesService.addFavorite();
  }

  clearStorage () {
    // If needed to clear all the favorites
    this.storage.clear('propertiesFavorite');
  }
}
