import { Component, OnInit, Input } from '@angular/core';
import { CurrencyPipe } from '@angular/common';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'lib-neighborhood-avgcosts',
  templateUrl: './neighborhood-avgcosts.component.html'
})

export class NeighborhoodAvgcostsComponent implements OnInit {
  @Input() neighborhood;
  @Input() formatTitle: Function;

  public lineChartOptions: any;

  constructor (
    private translate: TranslateService
  ) { }

  ngOnInit () {
    const scope = this;
    const currencyPipe = new CurrencyPipe(scope.translate.currentLang);

    // Colors can be changed here : src/app/services/v3/neighborhoods/neighborhoods.service.ts
    // Line 34

    this.lineChartOptions = {
      responsive: true,
      plugins: {
        tooltip: {
          enabled: true,
          callbacks: {
            label: (context) => {
              return currencyPipe.transform(context.raw, 'CAD', 'symbol-narrow', '2.0-0');
            }
          }
        },
        legend: {
          display: true
        }
      },
      scales: {
        y: {
          beginAtZero: false,
          ticks: {
            callback: (value) => {
              return currencyPipe.transform(value, 'CAD', 'symbol-narrow', '2.0-0');
            }
          }
        }
      }
    };
  }

  public chartHovered (e: any): void {
    // console.log(e);
  }
}
