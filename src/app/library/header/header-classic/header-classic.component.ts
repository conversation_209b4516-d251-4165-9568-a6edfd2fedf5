import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'lib-header-classic',
  templateUrl: './header-classic.component.html'
})

export class HeaderClassicComponent implements OnInit {
  @Input() transparent: string;
  @Input() specialColor: string;
  @Output() openPanel = new EventEmitter<Event>();
  @Output() switchLanguage = new EventEmitter<string>();

  constructor (
    public translate: TranslateService
  ) {

  }

  ngOnInit () {

  }

  onOpenPanel ($event: Event) {
    this.openPanel.emit($event);
  }

  switchLang (lang: string) {
    this.switchLanguage.emit(lang);
  }
}
