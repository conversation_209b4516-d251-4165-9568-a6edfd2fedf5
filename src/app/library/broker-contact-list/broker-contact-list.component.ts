import { Component, OnInit } from '@angular/core';
import { TeamsMembersService } from '@/services/v3/teammembers/teammembers.service';

@Component({
  selector: 'lib-broker-contact-list',
  templateUrl: './broker-contact-list.component.html'
})

export class BrokerContactListComponent implements OnInit {
  brokers = [];
  administrators = [];

  constructor (
    private teamService: TeamsMembersService
  ) {}

  async ngOnInit () {
    const { data } = await this.teamService.getTeams().toPromise();
    this.brokers = data.filter(m => m.teammembers_category === 'broker');
    this.administrators = data.filter(m => m.teammembers_category !== 'broker');
  }
}
