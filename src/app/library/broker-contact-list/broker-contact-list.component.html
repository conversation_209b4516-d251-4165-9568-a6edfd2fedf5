<div class="broker-contact-list-cpn">
  <div class="container">

    <h3>{{ 'library.broker-contact-list.title-brokers' | translate }}</h3>
    <div class="list-ctn">
      <lib-broker-card class="broker-card-ctn" *ngFor="let member of brokers" [member]="member" itemscope itemtype="http://schema.org/Person"></lib-broker-card>
    </div>

    <h3>{{ 'library.broker-contact-list.title-administrators' | translate }}</h3>
    <div class="list-ctn">
      <lib-broker-card class="broker-card-ctn" *ngFor="let member of administrators" [member]="member" itemscope itemtype="http://schema.org/Person"></lib-broker-card>
    </div>

    <a class="main-button -primary" [routerLink]="['urls.real-estate-agents' | translate]">
      {{ 'library.broker-contact-list.button' | translate }}
    </a>

  </div>
</div>