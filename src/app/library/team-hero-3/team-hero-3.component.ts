import { Component, OnInit } from '@angular/core';
import { BlocksService } from '@/services/v3/contentblocks/contentblocks.service';

@Component({
  selector: 'lib-team-hero-3',
  templateUrl: './team-hero-3.component.html'
})

export class TeamHero3Component implements OnInit {
  public blockTitle: string;
  public blockContent: string;

  constructor (private blocksService: BlocksService) {
    this.blocksService.getBlock('bloc-equipe').subscribe(data => {
      this.blockTitle = data.title;
      this.blockContent = data.text;
    });
  }

  ngOnInit () {
  }
}
