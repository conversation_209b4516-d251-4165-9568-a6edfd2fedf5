import { Component, OnInit } from '@angular/core';
import { UntypedFormBuilder, UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';

import { TeamsMembersService } from '@/services/v3/teammembers/teammembers.service';
import { BlocksService } from '@/services/v3/contentblocks/contentblocks.service';
import { ContactService } from '@/services/v3/contact/contact.service';

import { Subscription } from 'rxjs';
import { ReCaptchaV3Service } from 'ng-recaptcha';

@Component({
  selector: 'lib-broker-contact-header-2',
  templateUrl: './broker-contact-header-2.component.html'
})

export class BrokerContactHeader2Component implements OnInit {
  public isMobile: boolean;
  teamMembers: any = [];

  // Form
  contactForm: UntypedFormGroup;
  firstName: UntypedFormControl;
  lastName: UntypedFormControl;
  phoneNumber: UntypedFormControl;
  phoneExt: UntypedFormControl;
  email: UntypedFormControl;
  message: UntypedFormControl;
  subject: any;
  isTyping = false;
  successMessage: boolean = false;
  errorMessage: boolean = false;
  formSend: boolean = false;
  formLoading: boolean = false;

  public blockTitle: string;
  public blockContent: string;

  // Recaptcha v3 Subscription
  private submitExecutionSubscription: Subscription

  constructor (
    private recaptchaV3Service: ReCaptchaV3Service,
    private blocksService: BlocksService,
    private teamService: TeamsMembersService,
    private formBuilder: UntypedFormBuilder,
    private contactService: ContactService,
    private translate: TranslateService
  ) {
    const width = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;
    if (width <= 992) this.isMobile = true;

    this.teamService.getTeams().subscribe(({ data }) => {
      this.teamMembers = data;
    });

    this.blocksService.getBlock('bloc-contact').subscribe(data => {
      this.blockTitle = data.title;
      this.blockContent = data.text;
    });
  }

  ngOnInit () {
    this.createFormControls();
    this.createForm();
    this.delayRCDisplay();
  }

  delayRCDisplay () {
    setTimeout(() => {
      const result = document.getElementsByClassName('grecaptcha-badge');
      const recaptcha = result.item(0) as HTMLElement;
      recaptcha.style.visibility = 'visible';
    }, 2000);
  }

  ngOnDestroy () {
    const result = document.getElementsByClassName('grecaptcha-badge');
    const recaptcha = result.item(0) as HTMLElement;
    recaptcha.style.visibility = 'hidden';

    if (this.submitExecutionSubscription) {
      this.submitExecutionSubscription.unsubscribe();
    }
  }

  private createFormControls () {
    this.subject = new UntypedFormControl('');
    this.firstName = new UntypedFormControl('', Validators.required);
    this.lastName = new UntypedFormControl('', Validators.required);
    this.phoneNumber = new UntypedFormControl('', [
      Validators.required,
      Validators.pattern('[0-9]+'),
      Validators.minLength(10)
    ]);
    this.email = new UntypedFormControl('', [
      Validators.required,
      Validators.pattern('^\\w+([\\.-]?\\w+)*@\\w+([\\.-]?\\w+)*(\\.\\w{2,3})+$')
    ]);
    this.phoneExt = new UntypedFormControl('');
    this.message = new UntypedFormControl('', Validators.required);
  }

  private createForm () {
    this.translate.get('library.broker-contact-3.subject').subscribe(res => {
      this.subject = res;
    });

    this.contactForm = this.formBuilder.group({
      name: new UntypedFormGroup({
        firstName: this.firstName,
        lastName: this.lastName
      }),
      phone: new UntypedFormGroup({
        phoneNumber: this.phoneNumber,
        phoneExt: this.phoneExt
      }),
      email: this.email,
      message: this.message,
      subject: this.subject
    });
  }

  onSubmit () {
    if (!this.contactForm.valid) return false;
    this.formLoading = true;

    this.submitExecutionSubscription = this.recaptchaV3Service.execute('submit').subscribe(token => {
      this.contactForm.value.subject = this.subject;
      this.contactForm.value.token_captcha = token;

      this.contactService.postContact(this.contactForm.value).subscribe(response => {
        this.formSend = true;
        this.formLoading = false;

        if (response.success) this.successMessage = true;
        else this.errorMessage = true;
      });
    },
    error => console.error(error));
  }

  resetForm () {
    this.formSend = false;
    this.contactForm.reset();
  }

  retry () {
    this.formSend = false;
  }
}
