import { Component, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { MapService } from '@/services/v3/map/map.service';

@Component({
  selector: 'lib-broker-contact-map',
  templateUrl: './broker-contact-map.component.html'
})

export class BrokerContactMapComponent implements OnInit {
  map: any;

  constructor (
    private translate: TranslateService,
    private mapService: MapService
  ) {}

  ngOnInit () {
    this.initMap();
  }

  async initMap () {
    // Load client info
    const { url, coords: [lat, lng, zoom] } = await this.translate.get('client.map').toPromise();

    // Create map object
    this.map = this.mapService.createMap({ lat, lng, zoom });

    // Add pin icon
    this.map.on('load', () => {
      this.map.loadImage('/assets/images/pin.png', (error, image) => {
        if (error) return;

        this.map.addImage('pin', image);

        this.map.addSource('pin', {
          type: 'geojson',
          data: {
            type: 'FeatureCollection',
            features: [{ type: 'Feature', geometry: { type: 'Point', coordinates: [lng, lat] } }]
          }
        });

        this.map.addLayer({
          id: 'pin',
          type: 'symbol',
          source: 'pin',
          layout: { 'icon-image': 'pin', 'icon-size': 1 }
        });
      });
    });

    // Add click/hover event on pin
    this.map.on('click', 'pin', () => window.open(url));
    this.map.on('mouseenter', 'pin', () => { this.map.getCanvas().style.cursor = 'pointer'; });
    this.map.on('mouseleave', 'pin', () => { this.map.getCanvas().style.cursor = ''; });
  }
}
