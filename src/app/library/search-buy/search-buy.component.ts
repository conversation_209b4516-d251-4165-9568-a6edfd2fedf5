import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';

import { InscriptionsService } from '@/services/v3/inscriptions/inscriptions.service';

@Component({
  selector: 'lib-search-buy',
  templateUrl: './search-buy.component.html'
})

export class SearchBuyComponent implements OnInit {
  municipalites: any;
  searchModel: any = { city: undefined }
  search: any = Object.assign({}, this.searchModel);

  constructor (
    private inscriptionsService: InscriptionsService,
    private router: Router,
    private translate: TranslateService
  ) {}

  ngOnInit () {
    this.getMunicipalites();
  }

  getMunicipalites () {
    this.inscriptionsService.getMunicipalitiesWithInscriptions().subscribe(({ data }) => {
      this.municipalites = data;
    });
  }

  onSubmitBuy () {
    const url = this.translate.instant('urls.search-properties');
    this.router.navigate([url, this.search.city]);
  }
}
