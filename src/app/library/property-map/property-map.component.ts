import { Component, OnInit, Input } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { MapService } from '@/services/v3/map/map.service';

@Component({
  selector: 'lib-property-map',
  templateUrl: './property-map.component.html'
})

export class PropertyMapComponent implements OnInit {
  @Input() property;
  @Input() neighborhood;

  public isMobile: boolean = false;
  map: any;
  mapColor: string;
  looped = false;
  settings: any = { propertiesPaneIsOpen: true };

  constructor (
    private mapService: MapService,
    private translate: TranslateService
  ) {
    const width = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;
    if (width <= 767) this.isMobile = true;
  }

  async ngOnInit () {
    // Fetch map color from client infos
    const { color } = await this.translate.get('client.map').toPromise();
    this.mapColor = color;
  }

  async initMap () {
    if (this.looped) return;

    // Create map object
    const { geo_latitude: lat, geo_longitude: lng } = this.neighborhood;
    this.map = this.mapService.createMap({ lat, lng, zoom: 13 });

    // Set padding based on side panel
    this.adjustSizeMap();

    // Set map events and features
    this.map.on('load', () => {
      // Attach neighborhood geojson data if available
      if (this.neighborhood.geo_json) {
        this.map.addSource('quartiers-data', { type: 'geojson', data: this.neighborhood.geo_json, generateId: true });
        this.map.addLayer({
          id: 'quartiers',
          type: 'fill',
          source: 'quartiers-data',
          paint: { 'fill-color': this.mapColor, 'fill-opacity': 0.3 }
        });
      }

      // Add pin icon
      this.map.loadImage('/assets/images/pin.png', (error, icon) => {
        if (!error) {
          const features = [{ type: 'Feature', geometry: { type: 'Point', coordinates: [lng, lat] } }];
          this.map.addImage('pin-icon', icon);
          this.map.addSource('pin', { type: 'geojson', data: { type: 'FeatureCollection', features } });
          this.map.addLayer({ id: 'pins', type: 'symbol', source: 'pin', layout: { 'icon-image': 'pin-icon' } });
        }
      });

      this.looped = true;
    });
  }

  // Adjust map padding based on pane status
  adjustSizeMap () {
    this.map.easeTo({
      padding: { left: this.settings.propertiesPaneIsOpen && !this.isMobile ? 400 : 30 },
      duration: 300
    });
  }
}
