import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';

@Component({
  selector: 'lib-location-pane',
  templateUrl: './location-pane.component.html'
})

export class LocationPaneComponent implements OnInit {
  @Output() sizeChange: EventEmitter<any> = new EventEmitter<any>();

  @Input() settings;
  @Input() neighborhood;

  constructor () { }

  ngOnInit () {}

  togglePanel () {
    this.settings.propertiesPaneIsOpen = !this.settings.propertiesPaneIsOpen;
    document.getElementById('map').classList.toggle('open');
    this.sizeChange.emit();
  }

  sanitizeDescription () {
    if (this.neighborhood.sites_neighborhood_descrtiption) {
      const text = this.neighborhood.sites_neighborhood_descrtiption.replace(/<h[1-6].*?>.*?<\/h[1-6]>/g, '');
      const firstP = text?.match(/<p.*?>(.*?)<\/p>/)?.[1] || text;
      const desc = firstP?.substring(0, 250);
      return desc.length === 250 ? desc + '...' : desc;
    }
    return '';
  }

  getGoogleMapsLink (address: string): string {
    const baseUrl = 'https://www.google.ca/maps/place/';
    const formattedAddress = address.replace(/\s+/g, '+').replace(/#/g, '%23');
    return `${baseUrl}${formattedAddress}`;
  }
}
