import { Component, OnInit, Input } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'lib-openhouse-card-large',
  templateUrl: './openhouse-card-large.component.html'
})

export class OpenhouseCardLargeComponent implements OnInit {
  @Input() property;
  propertyUrl: any;

  constructor (public translate: TranslateService) { }

  ngOnInit () {
    if (this.property.neighborhood_slug) {
      this.propertyUrl = this.translate.instant('urls.property-single') +
        '/' + this.property.municipality_slug +
        '/' + this.property.neighborhood_slug +
        '/' + this.property.ext_address_slug +
        '/' + this.property.mls;
    } else {
      this.propertyUrl = this.translate.instant('urls.property-single') +
        '/' + this.property.municipality_slug +
        '/' + this.property.ext_address_slug +
        '/' + this.property.mls;
    }
  }
}
