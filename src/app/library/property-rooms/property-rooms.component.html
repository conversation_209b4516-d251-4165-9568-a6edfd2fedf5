<div class="property-rooms-cpn" *ngIf='rooms'>
	<ng-container *ngIf="!rooms.total == 0">
		<h3 class="-emphase-title">{{ 'library.property-rooms.title' | translate }}</h3>

		<div class="room-ctn table-ctn -has-button">
			<div class="table-wrap">
				<div class="room table-row -head grid">
					<p class="col-t-lg-3 level"><span>{{ 'library.property-rooms.level' | translate }}</span></p>
					<p class="col-t-lg-3 piece"><span>{{ 'library.property-rooms.rooms' | translate }}</span></p>
					<p class="col-t-lg-3 floor"><span>{{ 'library.property-rooms.floors' | translate }}</span></p>
					<p class="col-t-lg-3 details"><span>{{ 'library.property-rooms.details' | translate }}</span></p>
				</div>

				<div *ngFor="let room of rooms.data;" class="room table-row grid">
					<p class="col-t-lg-3 level">{{room.floor}}</p>
					<p class="col-t-lg-3 piece">{{room.code}}</p>
					<p class="col-t-lg-3 floor">{{room.dimensions}}</p>
					<p class="col-t-lg-3 details">{{room.floor_code}}</p>
				</div>
			</div>
			
			<div class="gradient"></div>
			<a class="small-link" href="" (click)="onOpenTable($event)"><i class="icon-map-plus"></i> {{ 'library.property-rooms.see-all-rooms' | translate }}</a>
		</div>
	</ng-container>
</div>
