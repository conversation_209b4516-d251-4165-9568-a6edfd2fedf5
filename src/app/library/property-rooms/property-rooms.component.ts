import { Component, OnInit, Input } from '@angular/core';

@Component({
  selector: 'lib-property-rooms',
  templateUrl: './property-rooms.component.html'
})

export class PropertyRoomsComponent implements OnInit {
  @Input() rooms;

  constructor () { }

  ngOnInit () {
    if (this.rooms && this.rooms.data) {
      this.rooms.data.sort((a, b) => a.order - b.order);
    }
  }

  onOpenTable ($event) {
    $event.preventDefault();
    $event.currentTarget.parentNode.classList.add('-opened');
  }
}
