import { Component, OnInit, Input } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'lib-statistics-evaluation',
  templateUrl: './statistics-evaluation.component.html'
})

export class StatisticsEvaluationComponent implements OnInit {
  @Input() property;

  public currentLang: any;

  expenses: any;
  salesprice: any;
  loanCost: any;
  payments: any;
  interestRate: any;
  cashdown: any = 0;
  mortgageLength: any;
  frequencyChoosen: any;

  CAPITALISATION: number = 2;

  public mortgage: any[];
  public frequencyPaiements: any[];

  constructor (
    public translateService: TranslateService
  ) {
    this.translateService.get('library.property-tools.tool-expenses.mortgage-years').subscribe(res => {
      this.mortgage = res;
      this.mortgageLength = 25;
    });

    this.translateService.get('library.property-tools.tool-expenses.frequency-paiements').subscribe(res => {
      this.frequencyPaiements = res;
      this.frequencyChoosen = this.frequencyPaiements[0].value;
    });

    const userLang = window.location.pathname.split('/')[1];
    this.currentLang = userLang || 'fr';
  }

  ngOnInit () {
    this.salesprice = parseInt(this.property.price_sale);
    this.interestRate = 3;
    this.calcCashdown();
    this.calcPayment();
  }

  calcCashdown () {
    const { salesprice } = this;
    this.cashdown = salesprice >= 1000000
      // 20% cashdown if over 1M$
      ? salesprice * 0.2
      : salesprice <= 500000
        // 5% under 500k
        ? salesprice * 0.05
        // 5% for the first 500k, then 10% for the rest
        : (500000 * 0.05) + ((salesprice - 500000) % 500000 * 0.1);
  }

  calcPayment () {
    this.loanCost = this.salesprice - this.cashdown < 0 ? 0 : this.salesprice - this.cashdown;
    if (this.salesprice && this.cashdown && this.interestRate && this.mortgageLength && this.frequencyChoosen) {
      const x = parseFloat(this.interestRate) / (100 * this.CAPITALISATION);
      const y = this.CAPITALISATION / this.frequencyChoosen;
      const interestPeriod = Math.pow((x + 1), y) - 1;
      const mount = parseInt(this.salesprice) - parseInt(this.cashdown);

      const result = (mount / ((1 - Math.pow((1 + interestPeriod), -(this.mortgageLength * this.frequencyChoosen))) / interestPeriod)).toFixed(2);

      if (parseInt(result) <= 0) {
        this.payments = '0';
      } else {
        this.payments = String(result);
      }
    } else {
      this.payments = undefined;
    }
  }
}
