<h3 class="title-bg -small">{{ 'library.statistics-evaluation.main-title' | translate }}</h3>
<div class="form-container">
    <form class="form-payments">
        <div class="form-row">
            <div class="input-ctn -dual">
                <label>{{ 'library.property-tools.tool-expenses.property-price' | translate }}</label>
                <input type="text" name="salesprice" [ngModel]="salesprice | currency:'CAD':'symbol-narrow':'2.0-0':this.currentLang" [ngModelOptions]="{standalone: true}" disabled>
            </div>
            <div class="input-ctn -dual">
                <label>{{ 'library.property-tools.tool-expenses.downpayment' | translate}}</label>
                <input type="text" name="cashdown" [(ngModel)]=cashdown (ngModelChange)="calcPayment()"  [ngModelOptions]="{standalone: true}">
            </div>
        </div>
        <div class="form-row">
            <div class="input-ctn -dual">
                <label>{{ 'library.property-tools.tool-expenses.loan-cost' | translate }}</label>
                <input type="text" name="loancost" [ngModel]="loanCost | currency:'CAD':'symbol-narrow':'2.0-0':this.currentLang" [ngModelOptions]="{standalone: true}" disabled>
            </div>
            <div class="input-ctn -dual">
                <label>{{ 'library.property-tools.tool-expenses.interest-rate' | translate }} *</label>
                <input type="text" name="interestRate" dropSpecialCharacters="false" [(ngModel)]=interestRate (ngModelChange)="calcPayment()" [ngModelOptions]="{standalone: true}">
            </div>
        </div>
        <div class="form-row">
            <div class="input-ctn -dual">
                <label>{{ 'library.property-tools.tool-expenses.mortgage' | translate }} *</label>
                <ng-select
                    [searchable]="false"
                    [items]="mortgage"
                    bindLabel="name"
                    bindValue="value"
                    [(ngModel)]="mortgageLength"
                    [ngModelOptions]="{standalone: true}"
                    (change)="calcPayment()"
                    placeholder="{{ 'library.property-tools.tool-expenses.mortgage' | translate }}" class="hypotheque-filter align-center">
                </ng-select>
            </div>
            <div class="input-ctn -dual">
                <label>{{ 'library.property-tools.tool-expenses.frequency' | translate }} *</label>
                <ng-select
                    [searchable]="false"
                    [items]="frequencyPaiements"
                    bindLabel="name"
                    bindValue="value"
                    [(ngModel)]="frequencyChoosen"
                    [ngModelOptions]="{standalone: true}"
                    (change)="calcPayment()"
                    placeholder="{{ 'library.property-tools.tool-expenses.frequency' | translate }}" class="hypotheque-filter align-center">
                </ng-select>
            </div>
        </div>
        <div class="value-ctn">
            <p *ngIf="payments">{{ 'library.property-tools.tool-expenses.payments' | translate }}</p>
            <p class="value" *ngIf="payments">{{ payments | currency:'CAD':'symbol-narrow':'2.0-0':this.currentLang}}</p>
        </div>
    </form>
</div>

