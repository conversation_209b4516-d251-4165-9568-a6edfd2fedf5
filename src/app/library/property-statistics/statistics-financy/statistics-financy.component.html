<h3 class="title-bg -small">{{ 'library.statistics-financy.main-title' | translate }}</h3>
<div *ngIf="property.total_eval" class="table-ctn">
    <div class="table-wrap">
        <div class="table-row -head">
            <p>{{ 'library.statistics-financy.evaluation.title' | translate }} ( {{ property.evaluation_year }} )</p>
        </div>
        <div *ngIf="property.evaluation_plot" class="table-row">
            <p>{{ 'library.statistics-financy.evaluation.field' | translate }}</p>
            <p><span>{{property.evaluation_plot | currency:'CAD':'symbol-narrow':'2.0-0':this.currentLang}}</span></p>
        </div>
        <div *ngIf="property.evaluation_building" class="table-row">
            <p>{{ 'library.statistics-financy.evaluation.building' | translate }}</p>
            <p><span>{{property.evaluation_building | currency:'CAD':'symbol-narrow':'2.0-0':this.currentLang}}</span></p>
        </div>
        <div *ngIf="property.total_eval" class="table-row">
            <p><span>{{ 'library.statistics-financy.evaluation.total' | translate }}</span></p>
            <p><span>{{property.total_eval | currency:'CAD':'symbol-narrow':'2.0-0':this.currentLang}}</span></p>
        </div>
    </div>
</div>

<div *ngIf="property.ext_mutation_tax || expenses?.total_taxes > 0" class="table-ctn">
    <div class="table-wrap">
        <div class="table-row -head">
            <p>{{ 'library.statistics-financy.taxes.title' | translate }}</p>
        </div>
        <div *ngFor="let expense of expenses?.taxes" class="table-row">
            <p>{{expense.expense_type}}</p>
            <p><span>{{expense.expense_amount | currency:'CAD':'symbol-narrow':'2.0-0':this.currentLang}}</span></p>
        </div>
        <div *ngIf="expenses?.total_taxes > 0" class="table-row">
            <p><span>{{ 'library.statistics-financy.taxes.total' | translate }}</span></p>
            <p><span>{{expenses.total_taxes | currency:'CAD':'symbol-narrow':'2.0-0':this.currentLang}}</span></p>
        </div>
        <div *ngIf="property.ext_mutation_tax" class="table-row -mutation">
            <p>{{ 'library.statistics-financy.taxes.mutation' | translate }}</p>
            <p><span>{{ property.ext_mutation_tax | currency:'CAD':'symbol-narrow':'2.0-0':this.currentLang }}</span></p>
            <div><p [innerHTML]="'library.statistics-financy.taxes.mutation-text'| translate"></p></div>
        </div>
    </div>
</div>
<div *ngIf="property.potential_income_total" class="table-ctn">
    <div class="table-wrap">
        <div class="table-row -head">
            <p>{{ 'library.statistics-financy.income.title' | translate }}</p>
        </div>
        <div *ngIf="property.potential_income_res" class="table-row">
            <p>{{ 'library.statistics-financy.income.residential' | translate }}</p>
            <p><span>{{property.potential_income_res | currency:'CAD':'symbol-narrow':'2.0-0':this.currentLang}}</span></p>
        </div>
        <div *ngIf="property.potential_income_comm" class="table-row">
            <p>{{ 'library.statistics-financy.income.commercial' | translate }}</p>
            <p><span>{{property.potential_income_comm | currency:'CAD':'symbol-narrow':'2.0-0':this.currentLang}}</span></p>
        </div>
        <div *ngIf="property.potential_income_stat" class="table-row">
            <p>{{ 'library.statistics-financy.income.car-park' | translate }}</p>
            <p><span>{{property.potential_income_stat | currency:'CAD':'symbol-narrow':'2.0-0':this.currentLang}}</span></p>
        </div>
        <div *ngIf="property.potential_income_au" class="table-row">
            <p>{{ 'library.statistics-financy.income.other' | translate }}</p>
            <p><span>{{property.potential_income_au | currency:'CAD':'symbol-narrow':'2.0-0':this.currentLang}}</span></p>
        </div>
        <div *ngIf="property.potential_income_total" class="table-row">
            <p><span>{{ 'library.statistics-financy.income.total' | translate }}</span></p>
            <p><span>{{property.potential_income_total | currency:'CAD':'symbol-narrow':'2.0-0':this.currentLang}}</span></p>
        </div>
    </div>
</div>

<div *ngIf="expenses?.total_other > 0" class="table-ctn">
    <div class="table-wrap">
        <div class="table-row -head">
            <p>{{ 'library.statistics-financy.other.title' | translate }}</p>
        </div>
        <div *ngFor="let expense of expenses.other" class="table-row">
            <p>{{expense.expense_type}}</p>
            <p><span>{{expense.expense_amount | currency:'CAD':'symbol-narrow':'2.0-0':this.currentLang}}</span></p>
        </div>
    </div>
</div>