import { Component, OnInit, Input } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

import { InscriptionsService } from '@/services/v3/inscriptions/inscriptions.service';

@Component({
  selector: 'lib-statistics-financy',
  templateUrl: './statistics-financy.component.html'
})

export class StatisticsFinancyComponent implements OnInit {
  @Input() property;

  public currentLang: any;
  public expenses: any;
  public totalTax = 0;

  constructor (
    public translateService: TranslateService,
    private inscriptionsService: InscriptionsService
  ) {
    const userLang = window.location.pathname.split('/')[1];
    this.currentLang = userLang || 'fr';
  }

  ngOnInit () {
    this.getExpenses();
  }

  getExpenses () {
    this.inscriptionsService.getExpenses(this.property.mls, 'array').subscribe(data => {
      this.expenses = data.data;
    });
  }
}
