<div class="testimonials-slider-cpn" *ngIf="testimonials?.length > 0">
	<div class="swiper swiper-testimonials -space-default container -narrow">

		<h3 class="-white -no-space">{{ 'library.testimonials-slider.title' | translate }}</h3>
		<div class="swiper-container">

			<div class="swiper-wrapper">
				<div class="swiper-slide" itemscope itemtype="http://schema.org/Review" *ngFor="let testimony of testimonials">
				<div class="container" itemscope itemtype="http://schema.org/Quotation">
					<p itemprop="reviewBody" class="description" [innerHtml]="testimony.description | shorten: 300: '...'"></p>
					<p itemprop="spokenByCharacter" class="name"><span class="line"></span>{{ testimony.author }}</p>
				</div>
				</div>
			</div>

			<div class="swiper-button-prev swiper-btn"><i></i></div>
			<div class="swiper-button-next swiper-btn"><i></i></div>
		</div>

		<div class="swiper-pagination"></div>
	</div>
</div>
