import { Component, OnInit } from '@angular/core';
import { TestimonialsService } from '@/services/v3/testimonials/testimonials.service';
import Swiper from 'swiper';
import { Navigation, Pagination, Autoplay } from 'swiper/modules';
@Component({
  selector: 'lib-testimonials-slider',
  templateUrl: './testimonials-slider.component.html'
})

export class TestimonialsSliderComponent implements OnInit {
  testimonialSwiper: any;
  testimonials = [];

  constructor (
    private testimonialsService: TestimonialsService
  ) { }

  ngOnInit () {}

  ngAfterViewInit () {
    this.testimonialsService.getTestimonials(3, 'rdm').subscribe(({ data }) => {
      this.testimonials = data;
      this.initSlider();
    });
  }

  initSlider () {
    Swiper.use([Navigation, Pagination, Autoplay]);

    setTimeout(() => {
      this.testimonialSwiper = new Swiper('.swiper-testimonials .swiper-container', {
        speed: 1000,
        observer: true,
        loop: true,
        autoplay: {
          delay: 6000
        },
        runCallbacksOnInit: false,
        pagination: {
          el: '.swiper-pagination',
          type: 'bullets'
        },
        navigation: {
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev'
        }
      });
    }, 1000);
  }
}
