<div itemscope itemtype="http://schema.org/House" class="jsProperty" [routerLink]="['urls.property-single' | translate] + propertyUrl ">
	<div class="containerBandeau">
		<span href="" *ngIf="!rentalSearch && property.price_sale && property.price_rental"
			class="properties-label -label-rental-possible">{{ "library.properties.rental-possible" | translate }}</span>
		<span href="" *ngIf="property.status && !priceChanged.changed"
			class="properties-label -label-sold">{{ "library.properties.sold" | translate }}</span>
		<span href="" *ngIf="property.is_new && !priceChanged.changed"
			class="properties-label -label-new">{{ "library.properties.new" | translate }}</span>
		<span href="" *ngIf="property.openhouses?.length > 0 && !priceChanged.changed"
			class="properties-label -label-view">{{ "library.properties.visit" | translate }}
			{{property.openhouses[0].start_date | localizedDate:'longDate'}}</span>
		<ng-container *ngIf="priceChanged?.changed">
			<div
				class="properties-label -label-price-changed"
				[ngClass]="{
					'-down': priceChanged?.direction === 'down',
					'-up': priceChanged?.direction === 'up'
				}">
				<ng-container [ngSwitch]="priceChanged?.direction">
					<ng-container *ngSwitchCase="'down'">
						{{ "library.properties.price_changed.down" | translate }}
					</ng-container>
					<ng-container *ngSwitchCase="'up'">
						{{ "library.properties.price_changed.up" | translate }}
					</ng-container>
					<ng-container *ngSwitchDefault>
						{{ "library.properties.price_changed" | translate }}
					</ng-container>
				</ng-container>
			</div>
		</ng-container>
	</div>

	<!-- {{property.visit_360}} -->

	<div class="icon-tab">
		<div class="play-btn" *ngIf="property.video">
			<span class="icon-play"></span>
		</div>
		<div class="player360" *ngIf="property.visit_360">
			<span class="icon-360"></span>
		</div>
	</div>

	<div class="img-ctn">
		<img itemprop="image" src="{{property.ext_coverphoto}}"
		(load)="setImageSize($event)" 
		alt="{{ ['library.properties.alt-image' | translate] + property?.ext_address || '' }}" onerror="this.src='assets/images/placeholder/propriete-nb.jpg';">
	</div>
	
	<div class="gradiant"></div>
	<div class="filter"></div>

	<div class="properties-info">
		<div class="bloc-head">
			<ng-container *ngIf="!property.status">
				<p class="price" *ngIf="property.price_sale && !rentalSearch; else showRental">
					<ng-container *ngIf="property.price_sale_unit">
						{{ property.price_sale | currency:'CAD':'symbol-narrow':'1.2-2':this.currentLang }} / 
						{{ property.price_sale_unit | lowercase }}
					</ng-container>

					<ng-container *ngIf="!property.price_sale_unit" class="s -inner">
						{{ property.price_sale | currency:'CAD':'symbol-narrow':'2.0-0':this.currentLang }}
						<span *ngIf="property.taxes_sale_not_included" class="small-tps">+tps/tvq</span>
					</ng-container>
				</p>

				<ng-template #showRental>
					<p class="price">
						<span class="c -inner" *ngIf="property.property_category !== 'Commercial'">
							{{ property.price_rental | currency:'CAD':'symbol-narrow':'2.0-0':this.currentLang }}
							{{ 'library.property-hero.per-month' | translate }}
						</span>

						<span class="c a -inner" *ngIf="property.property_category === 'Commercial' && property.rental_period === 'A'">
							{{ property.price_rental | currency:'CAD':'symbol-narrow':'2.0-0':this.currentLang }}
							{{ 'library.property-hero.commercial-year' | translate }}
							<span *ngIf="property.taxes_rental_not_included" class="small-tps">+tps/tvq</span>
						</span>

						<span class="c m -inner" *ngIf="property.property_category === 'Commercial' && property.rental_period !== 'A'">
							{{ property.price_rental | currency:'CAD':'symbol-narrow':'2.0-0':this.currentLang }}
							{{ 'library.property-hero.commercial-month' | translate }}
							<span *ngIf="property.taxes_rental_not_included" class="small-tps">+tps/tvq</span>
						</span>
					</p>
				</ng-template>
			</ng-container>

			<p class="type">{{property.property_type}}</p>
		</div>
		
		<div class="more-info" itemscope itemtype="http://schema.org/PostalAddress">
			<a [routerLink]="['urls.property-single' | translate] + propertyUrl " class="address" itemprop="address">
				{{ property.displayAddress }}
			</a>

			<div class="align">
				<p class="location" itemprop="addressLocality">
					{{property.municipality.description | shorten: 25: '...'}}</p>
				<div class="numbers">
					<div class="icon-ctn" *ngIf="property.rooms_bedroom_total_number">
						<i class="icon-bed"></i>
						<p itemprop="numberOfRooms">{{property.rooms_bedroom_total_number}}</p>
					</div>
					<div class="icon-ctn" *ngIf="property.rooms_bathroom_number">
						<i class="icon-shower"></i>
						<p itemprop="numberOfRooms">{{property.rooms_bathroom_number}}</p>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>