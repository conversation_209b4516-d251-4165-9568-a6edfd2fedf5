import { Component, OnInit, Input } from '@angular/core';

@Component({
  selector: 'lib-property-characteristics',
  templateUrl: './property-characteristics.component.html'
})

export class PropertyCharacteristicsComponent implements OnInit {
  @Input() characteristics;

  constructor () { }

  ngOnInit () {
  }

  onOpenTable ($event) {
    $event.preventDefault();
    $event.currentTarget.parentNode.classList.add('-opened');
  }
}
