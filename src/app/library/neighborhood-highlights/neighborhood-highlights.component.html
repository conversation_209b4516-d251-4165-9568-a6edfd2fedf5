<div class="neighborhood-highlights-cpn container -space-large grid" *ngIf="neighborhood" itemscope itemtype="http://schema.org/Place">
	<div class="neighborhood-description" [ngClass]="neighborhood.editableblocks ? 'col-t-lg-8' : 'col-12'" >
		<h2 itemprop="name" class="title -no-top-space" *ngIf="neighborhood.name">{{neighborhood.name}}</h2>
		<div itemprop="description" class="main-inscription laraberg" [innerHtml]="contentSanitized"></div>
		<!-- btn voir la vidéo -->
		<p class="open-video" (click)="onOpenFullScreen($event)" *ngIf="neighborhood.video_url">
			{{"global.btn-video" | translate}}	
			<span class="icon-fill-play"> </span>
		</p>
		
		<div *ngIf="neighborhood.points_of_interest" class="interest-ctn">
			<h3 class="-emphase-title">
				{{ 'library.neighborhood-interest.title' | translate}}
			</h3>
			<ul class="small-list">
				<ng-container *ngFor="let interest of neighborhood.points_of_interest">
					<li>{{interest.name}}</li>
				</ng-container>
			</ul>
		</div>
	</div>
	<div *ngIf="neighborhood.editableblocks" class="col-t-lg-4">
		<div class="blocks-ctn">
			<div class="block" *ngFor="let block of neighborhood.editableblocks">
				<div class="icon-ctn" *ngIf="block.icon">
					<i class="{{block.icon}}"></i>
				</div>
				<div class="text-ctn">
					<h4 class="-no-space -small title">{{block.title}}</h4>
					<div class="description" [innerHtml]="block.text"></div>
				</div>
			</div>
		</div>
	</div>
</div>