import { Component, OnInit, Input, OnChanges, SimpleChanges } from '@angular/core';

import { DomSanitizer } from '@angular/platform-browser';

@Component({
  selector: 'lib-neighborhood-highlights',
  templateUrl: './neighborhood-highlights.component.html'
})

export class NeighborhoodHighlightsComponent implements OnInit, OnChanges {
  @Input() neighborhood;

  public loading: boolean = true;
  public videoUrl: any;
  public contentSanitized: any;
  constructor (
    private sanitizer: DomSanitizer
  ) {}

  ngOnInit () {
    // Retiré le code de sanitization d'ici
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['neighborhood'] && this.neighborhood?.description) {
      this.contentSanitized = this.sanitizer.bypassSecurityTrustHtml(this.neighborhood.description);
    }
  }
  initVideo (value) {
    this.videoUrl = this.sanitizer.bypassSecurityTrustResourceUrl(value.video_embed);
    setTimeout(() => {
      this.loading = false;
    }, 1200);

    
  }

  onOpenFullScreen (event) {
    event.stopPropagation();
    document.querySelectorAll('#fullSlider')[0].classList.add('-opened');
    // this.videoUrl = "https://www.youtube.com/embed/" + this.neighborhood.video + "?rel=0&amp;controls=0&amp;showinfo=0";
    // (<HTMLImageElement>document.getElementById("video-yt")).src =  this.videoUrl + "&autoplay=1";
    document.documentElement.classList.toggle('-no-scroll');
  }

  onCloseFullScreen () {
    document.querySelectorAll('.full-screen-cpn')[0].classList.remove('-opened');
    document.documentElement.classList.toggle('-no-scroll');
    // (<HTMLImageElement>document.getElementById("video-yt")).src =  this.videoUrl;
  }
}
