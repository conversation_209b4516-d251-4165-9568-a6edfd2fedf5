<a class="item" href="" (click)="onOpenPreview($event)" itemscope itemtype="http://schema.org/Person">
	<div class="img-ctn">
		<div class="filter"></div>
		<ng-template [ngIf]="teamMember.photo" [ngIfElse]="defaultImage">
			<img itemprop="image" src="{{teamMember.photo}}" alt="{{teamMember.firstname}} {{teamMember.lastname}} - {{teamMember.job}}">
		</ng-template>

		<ng-template #defaultImage>
			<img itemprop="image" src="/assets/images/placeholder/default_image.jpeg" alt="{{teamMember.firstname}} {{teamMember.lastname}} - {{teamMember.job}}">
		</ng-template>
	</div>
	<div class="grid-content">
		<p itemprop="name" class="name" *ngIf="teamMember.firstname && teamMember.lastname">{{teamMember.firstname}} {{teamMember.lastname}}</p>
		<p itemprop="jobTitle" class="role" *ngIf="teamMember.job">{{teamMember.job}}</p>
	</div>
</a>
<div class="og-expander">
	<div class="og-expander-inner">
		<span class="og-close" (click)="onClosePreview($event)"></span>
		<div class="container grid">
			<div class="og-fullimg col-12 col-t-lg-6">
				<!-- Show photo -->
				<ng-container *ngIf="teamMember.photo && !teamMember.video_formatted; else showVideoOrDefault">
					<img itemprop="image" [src]="teamMember.photo" [alt]="teamMember.firstname + ' ' + teamMember.lastname">
				</ng-container>

				<!-- Video -->
				<ng-template #showVideoOrDefault>
					<ng-container *ngIf="teamMember.video_formatted; else defaultImage">
						<iframe class="video inlinevideo" [src]="cleanVideo" title="YouTube video player" frameborder="0" allow="autoplay" allowfullscreen></iframe>
					</ng-container>
				</ng-template>

				<!-- placeholder-->
				<ng-template #defaultImage>
					<img itemprop="image" src="/assets/images/placeholder/default_image.jpeg" [alt]="teamMember.firstname + ' ' + teamMember.lastname">
				</ng-template>
			</div>
			
			<div class="og-details col-12 col-t-lg-6">
				<h2 itemprop="name" class="name"*ngIf="teamMember.firstname && teamMember.lastname">{{teamMember.firstname}} {{teamMember.lastname}}</h2>
				<p itemprop="jobTitle" class="role"*ngIf="teamMember.job">{{teamMember.job}}</p>
				<div class="social-ctn">
					<a itemprop="telephone" *ngIf="teamMember.phone" href="tel:{{ teamMember.phone | phone}}" class="-phone"><i class="icon-mobile"></i> {{ teamMember.phone | phone}}</a>
					<a itemprop="email" *ngIf="teamMember.email" href="mailto:{{teamMember.email}}" class="-phone -mail"><i class="icon-mail"></i> {{teamMember.email}}</a>
					<div class="block">
						<a *ngIf="teamMember.facebook" target="_blank" href="{{teamMember.facebook}}" class="social-icon icon-logo-facebook"></a>
			        	<a *ngIf="teamMember.linkedin" target="_blank" href="{{teamMember.linkedin}}" class="social-icon icon-logo-linkedin"></a>
			        	<a *ngIf="teamMember.instagram" target="_blank" href="{{teamMember.instagram}}" class="social-icon icon-logo-instagram"></a>
			        	<a *ngIf="teamMember.youtube" target="_blank" href="{{teamMember.youtube}}" class="social-icon icon-logo-youtube"></a>
			        	<a *ngIf="teamMember.twitter" target="_blank" href="{{teamMember.twitter}}" class="social-icon icon-x"></a>
					</div>
				</div>
				<p itemprop="knowsAbout" [innerHtml]="teamMember.biography"></p>
				<p itemprop="knowsLanguage" *ngIf="teamMember.languages_value" class="lang">{{ 'library.team-card-1.spoken' | translate }} {{teamMember.languages_value}}</p>

			</div>
		</div>
	</div>
</div>

