<div class="property-navigation-cpn">
	<div class="previous-ctn">
		<a class="property-nav-ctn" [routerLink]="['urls.neighborhoods' | translate , previousNav.slug ]" *ngIf='previousNav'>
			<div class="arrow-ctn -left-arrow">
				<i class="icon-arrow-left"></i>
			</div>
			<div class="nav-info -left-nav">
				<div class="img-ctn">
					<img src="{{previousNav.header_image}}" alt="{{previousNav.name}}">
				</div>
				<div class="text-ctn">
					<p class="title">{{ 'library.neighborhood-navigation.prevnav' | translate }}</p>
					<p class="location" *ngIf='previousNav.name'>{{previousNav.name}}</p>
				</div>
			</div>
		</a>
	</div>
	<div class="next-ctn">
		<a class="property-nav-ctn" [routerLink]="['urls.neighborhoods' | translate , nextNav.slug ]" *ngIf='nextNav'>
			<div class="nav-info -right-nav">
				<div class="text-ctn">
					<p class="title">{{ 'library.neighborhood-navigation.nextnav' | translate }}</p>
					<p class="location" *ngIf='nextNav.name'>{{nextNav.name}}</p>
				</div>
				<div class="img-ctn">
					<img src="{{nextNav.header_image}}" alt="{{nextNav.name}}">
				</div>
			</div>
			<div class="arrow-ctn -right-arrow">
				<i class="icon-arrow-right"></i>
			</div>
		</a>
	</div>
</div>
