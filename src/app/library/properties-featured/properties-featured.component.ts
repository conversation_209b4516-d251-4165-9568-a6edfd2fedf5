import {Component, OnInit, Input, ViewChild, ElementRef, AfterViewInit} from '@angular/core';
import Swiper from 'swiper';

@Component({
  selector: 'lib-properties-featured',
  templateUrl: './properties-featured.component.html'
})

export class PropertiesFeaturedComponent implements OnInit, AfterViewInit {
  @Input() properties;

  @ViewChild('swiperContainer', { static: false }) swiperContainer: ElementRef;
  @ViewChild('nextButton', { static: false }) nextButton: ElementRef;
  @ViewChild('prevButton', { static: false }) prevButton: ElementRef;

  constructor () { }

  ngOnInit () {}

  ngAfterViewInit () {
    this.initSlider();
  }

  initSlider () {
    setTimeout(() => {
      const swiper = new Swiper(this.swiperContainer.nativeElement, {
        speed: 1000,
        observer: true,
        simulateTouch: false,
        autoplay: true,
        runCallbacksOnInit: false,
        navigation: {
          nextEl: this.nextButton.nativeElement,
          prevEl: this.prevButton.nativeElement
        }
      });
    }, 1000);
  }
}
