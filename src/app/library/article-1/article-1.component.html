<a *ngIf='blogPost.coverphoto' class="img-ctn" [routerLink]="['urls.real-estate-blog' | translate, blogPost.slug ]">
	<div class="filter"></div>
	<img src="{{ blogPost.coverphoto }}" alt="{{'library.article.alt-image' | translate}}{{ blogPost.title }}">
</a>
<div class="article-info">
	<h3 class="-no-space -small"><a [routerLink]="['urls.real-estate-blog' | translate, blogPost.slug ]">{{ blogPost.title }}</a></h3>
	<p class="date">{{ blogPost.publication_date | localizedDate:'longDate' }}</p>
	<p class="description">{{ blogPost.abstract | shorten: 600: '...' }}</p>
</div>
