import { Component, OnInit, HostListener } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { NeighborhoodsService } from '@/services/v3/neighborhoods/neighborhoods.service';
import { MapService } from '@/services/v3/map/map.service';

@Component({
  selector: 'lib-neighborhood-map',
  templateUrl: './neighborhood-map.component.html'
})

export class NeighborhoodMapComponent implements OnInit {
  settings: any = { propertiesPaneIsOpen: true };
  searchModel: any = { feature: undefined }
  search: any = Object.assign({}, this.searchModel);

  public isTablet: boolean = false;
  resizeTimeout: any; 
  locationJSON: any;
  map: any;
  selected = null;
  looped = false;
  hoveredFeatureId = null;

  mapBounds;
  mapPadding = 40;
  mapTabletPadding = 50;
  mapColor: string;
  showMapOnly = false;

  constructor (
    private neighborhoodsService: NeighborhoodsService,
    private translate: TranslateService,
    private mapService: MapService
  ) {
    const width = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;
    if (width <= 992) this.isTablet = true;
  }

  async ngOnInit () {
    // Fetch map color from client infos
    const { color } = await this.translate.get('client.map').toPromise();
    this.mapColor = color;

    this.neighborhoodsService.getNeighborhoods().subscribe(({ data }) => {
      this.locationJSON = data;

      // Set map boundaries based on features
      this.mapBounds = this.mapService.getBoundaries(this.locationJSON.features);

      this.initMap();
      this.setupHoverListeners();
    });
  }

  setupHoverListeners() {
    const cards = document.querySelectorAll('.neighborhood-card');
    
    cards.forEach(card => {
      card.addEventListener('mouseenter', (e: any) => {
        const id = e.target.getAttribute('data-id');
        const feature = this.map?.querySourceFeatures('quartiers-data')
          .find(f => f.properties.id === id);

        if (feature) {
          if (this.hoveredFeatureId !== null) {
            this.map.setFeatureState(
              { source: 'quartiers-data', id: this.hoveredFeatureId },
              { hover: false }
            );
          }
          this.hoveredFeatureId = feature.id;
          this.map.setFeatureState(
            { source: 'quartiers-data', id: this.hoveredFeatureId },
            { hover: true }
          );
        }
      });

      card.addEventListener('mouseleave', () => {
        if (this.hoveredFeatureId !== null) {
          this.map.setFeatureState(
            { source: 'quartiers-data', id: this.hoveredFeatureId },
            { hover: false }
          );
          this.hoveredFeatureId = null;
        }
      });
    });
  }

  async initMap () {
    if (this.looped) return;

    // Create map object
    this.map = this.mapService.createMap();
    this.adjustSizeMap();

    // Render map and add events
    this.map.on('load', () => {
      this.looped = true;

      // Attach map geojson data
      this.map.addSource('quartiers-data', { type: 'geojson', data: this.locationJSON, generateId: true });

      // Build polygons using conditionnal opacity with transition
      this.map.addLayer({
        id: 'quartiers',
        type: 'fill',
        source: 'quartiers-data',
        paint: {
          'fill-color': this.mapColor,
          'fill-opacity': [
            'case',
            ['boolean', ['feature-state', 'hover'], false], 0.8,
            ['boolean', ['feature-state', 'selected'], false], 0.6,
            0.3
          ],
          'fill-opacity-transition': {
            duration: 300,
            delay: 0
          }
        }
      });

      // Cursor styling
      this.map.on('mouseenter', 'quartiers', () => { this.map.getCanvas().style.cursor = 'pointer'; });
      this.map.on('mouseleave', 'quartiers', () => { this.map.getCanvas().style.cursor = ''; });

      // Add click event to neighborhoods
      this.map.on('click', 'quartiers', e => {
        this.clearSelection();
        this.select(e.features[0]);
      });

      this.centerMap(); 
      this.setupHoverListeners();
    });
  }

  @HostListener('window:resize', ['$event'])
  onResize(event: Event) {
    clearTimeout(this.resizeTimeout);
    this.resizeTimeout = setTimeout(() => {
      this.centerMap(); 
      this.map.resize();
    }, 300);
  }

  // Select a neighborhood
  select (e) {
    if (!e) return;

    // Fetch JSON data corresponding to selected neighborhood
    this.selected = {
      ...this.locationJSON.features.find(f => f.properties.id === e.properties.id),
      id: e.id // Add polygon id for future reference
    };

    // Set selected status on map polygon
    this.map.setFeatureState({ source: 'quartiers-data', id: e.id }, { selected: true });

    // Center map on neighborhood
    const bounds = this.mapService.getBoundaries([this.selected]);
    const { mapPadding } = this;
    this.map.fitBounds(bounds, { padding: { top: mapPadding, bottom: mapPadding, right: this.isTablet ? this.mapPadding : 400 } });
  }

  // Clear neighborhood selection and reset map viewport
  clearSelection () {
    if (this.selected) this.map.setFeatureState({ source: 'quartiers-data', id: this.selected.id }, { selected: false });
    this.selected = null;
    this.centerMap();
  }

  // Adjust map padding based on pane status
  adjustSizeMap (side = 'left') {
    this.map.easeTo({
      padding: { [side]: this.settings.propertiesPaneIsOpen && !this.isTablet ? 400 : this.mapPadding },
      duration: 300
    });
  }

  // Neighborhood panel drodown selection
  selectWithDropdown () {
    this.clearSelection();
    const neighborhood = this.map.querySourceFeatures('quartiers-data', { sourceLayer: 'quartiers' })
      .find(f => f.properties.id === this.search.feature);

    this.select(neighborhood);
    if (this.isTablet) this.settings.propertiesPaneIsOpen = false;
  }

  toggleMapPane(show = null) {
    this.showMapOnly = show;

    if(show) {
      this.centerMap();
    }
  }

  centerMap() {
    const dynamicPadding = this.calculatePadding();     
    this.map.fitBounds(this.mapBounds, { padding: dynamicPadding, linear: true, maxZoom: 18 });
  }
  
  calculatePadding(): any {
     if (window.innerWidth > 992) {
       return { 
         top: 150, 
         left: 150,
         bottom: this.mapPadding,
         right: this.mapPadding 
       };
     } 
     else {
       return { 
         top: this.mapTabletPadding,
         bottom: this.mapTabletPadding, 
         left: this.mapTabletPadding, 
         right: this.mapTabletPadding 
       };
     }
  }
}