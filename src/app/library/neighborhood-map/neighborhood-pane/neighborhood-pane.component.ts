import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';

@Component({
  selector: 'lib-neighborhood-pane',
  templateUrl: './neighborhood-pane.component.html'
})

export class NeighborhoodPaneComponent implements OnInit {
  @Input() features;
  @Input() settings;
  @Input() search;
  @Input() showMapOnly;

  @Output() sizeChange: EventEmitter<any> = new EventEmitter<any>();
  @Output() change: EventEmitter<any> = new EventEmitter<any>();

  constructor () { }

  ngOnInit () {
  }

  togglePanel () {
    this.settings.propertiesPaneIsOpen = !this.settings.propertiesPaneIsOpen;
    document.getElementById('map').classList.toggle('open');
    this.sizeChange.emit();
  }

  onChangeFeatures ($event) {
    this.search.feature = $event?.properties.id;
    this.change.emit(true);
  }
}
