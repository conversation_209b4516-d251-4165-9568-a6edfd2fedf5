<div class="neighborhood-map-cpn style-1" [class.show]="showMapOnly">
  <div class="neighborhoods-filters-toggle-ctn">
    <div class="view-toggle-ctn">
      <a
        class="btn-toggle-list"
        (click)="toggleMapPane(false)"
        [class.active]="!showMapOnly"
        >{{ "library.toggler.list" | translate }}</a
      >
      <a
        class="btn-toggle-map"
        (click)="toggleMapPane(true)"
        [class.active]="showMapOnly"
        >{{ "library.toggler.map" | translate }}</a
      >
    </div>
  </div>

  <lib-neighborhood-pane *ngIf="locationJSON" 
    [search]='search' 
    [features]="locationJSON.features" 
    [settings]='settings' 
    [showMapOnly]="!showMapOnly"
    (sizeChange)=adjustSizeMap() 
    (change)="selectWithDropdown()"
    >
  </lib-neighborhood-pane>

  <div>
    <div class="map" id="map" [ngStyle]=""></div>

    <div id="currentPopup-ctn" *ngIf="selected?.properties"> 
      <div class="background"></div>

      <div id="currentPopup" class="pop-up-content"> 
        <div id="close-popup" (click)="clearSelection()">X</div> 
        <div class="img-ctn"><img [src]="selected.properties.header_image" alt=""></div> 

        <div class="text-ctn">
          <p class="location -no-space">{{ selected.properties.name }}</p> 
          <p [innerHTML]="selected.properties.description | shorten: 400: '...'"></p> 
          <a class="main-button -primary" [routerLink]="['urls.neighborhoods' | translate, selected.properties.id]">
            {{ 'library.neighborhood-map.btn-discover' | translate }}
          </a>
        </div>
      </div>

    </div>
  </div>
</div>