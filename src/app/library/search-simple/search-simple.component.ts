import { Component, OnInit, Renderer2, Inject } from '@angular/core';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { DOCUMENT } from '@angular/common';

import { InscriptionsService } from '@/services/v3/inscriptions/inscriptions.service';

import { environment } from '@/../environments/environment';

@Component({
  selector: 'lib-search-simple',
  templateUrl: './search-simple.component.html'
})

export class SearchSimpleComponent implements OnInit {
  municipalites: any;
  mls: any;
  GoogleMaps;
  addressText;
  searchboxProperties: any[] = [];
  maxDropdownProperties = 5;
  searchModified = false;

  searchModel: any = {
    city: undefined,
    address: '',
    mls: undefined,
    keyword: ''
  }

  search: any = Object.assign({}, this.searchModel);

  constructor (
    @Inject(DOCUMENT) private document: Document,
    private inscriptionsService: InscriptionsService,
    private router: Router,
    private translate: TranslateService,
    private renderer2: Renderer2
  ) {}

  async ngOnInit () {
    this.getMunicipalites();
    this.getAllMls();

    // Dynamically inject GoogleMaps script inside DOM - see Evaluation-form component for details
    await this.loadScript('https://maps.googleapis.com/maps/api/js?key=' + environment.gmapToken + '&libraries=places');
    this.GoogleMaps = window.google.maps;

    // Initialize address field with auto-complete
    const addressElement = document.getElementById('address-input-2');
    const addressOptions = { componentRestrictions: { country: 'ca' } };
    const addressField = new this.GoogleMaps.places.Autocomplete(addressElement, addressOptions);

    addressField.addListener('place_changed', () => {
      const address = addressField.getPlace();
      if (address) this.search.address = address;
    });
  }

  // Inject script element into DOM
  private loadScript (url) {
    // Ignore if script already exists
    if (document.querySelector('script[data-name="gmap-script"]')) return;

    return new Promise((resolve, reject) => {
      const script = this.renderer2.createElement('script');
      script.setAttribute('data-name', 'gmap-script');
      script.type = 'text/javascript';
      script.src = url;
      script.text = '';
      script.async = true;
      script.defer = true;
      script.onload = resolve;
      script.onerror = reject;
      this.renderer2.appendChild(this.document.body, script);
    });
  }

  getMunicipalites () {
    this.inscriptionsService.getMunicipalitiesWithInscriptions().subscribe(data => {
      this.municipalites = data.data;
    });
  }

  getAllMls () {
    this.inscriptionsService.getAllMls().subscribe(data => {
      this.mls = data.data;
    });
  }

  onSearch(event: any) {
    // Prevents opening search dropdown on load
    if (!this.searchModified) this.searchModified = true;

    if (event && event.target.value) {
      this.search.keyword = event.target.value.toLowerCase();
      
      // Filter municipalities based on search keyword
      if (this.municipalites) {
        const keywords = this.search.keyword.split(" ").filter(k => k.length > 0);
        this.searchboxProperties = this.municipalites
          .filter(m => {
            return keywords.every(keyword => 
              m.label.toLowerCase().includes(keyword)
            );
          })
          .slice(0, this.maxDropdownProperties)
          .map(m => {
            let fullAddress = m.label;
            
            // Highlight matching keywords
            keywords.forEach(keyword => {
              if (keyword.length > 0) {
                const regex = new RegExp(`(${keyword})`, "ig");
                fullAddress = fullAddress.replace(regex, "<b>$1</b>");
              }
            });

            return { ...m, fullAddress };
          });
      }
    } else {
      delete this.search.keyword;
      this.searchboxProperties = [];
    }
  }

  clearSearch() {
    this.search.keyword = '';
    this.search.city = undefined;
    this.searchboxProperties = [];
    this.searchModified = false;
  }

  onSubmitSell () {
    const url = this.translate.instant('urls.real-estate-online-evaluation');
    this.router.navigate([url, this.search.address.formatted_address || this.addressText]);
  }

  onSubmitBuy () {
    const url = this.translate.instant('urls.search-properties');
    this.router.navigate([url, this.search.city]);
  }

  onSubmitCentris () {
    const url = this.translate.instant('urls.search-properties');
    this.router.navigate([url, this.search.mls]);
  }
}
