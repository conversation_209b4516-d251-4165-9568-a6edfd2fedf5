<div class="neighborhood-demographics-cpn -space-default" *ngIf="neighborhood">
	<div class="container">
		<h3 class="-center">
		 	{{ formatTitle(neighborhood.name, 'library.neighborhood-demographics.title' | translate) }}
		</h3>

		<div class="chart-wrap">
			<div class="chart-ctn block">
				<div>
					<ng-container>
						<canvas baseChart
							[type]="'doughnut'"
							[data]="ageData"
							(chartHover)="chartHovered($event)">
							</canvas>
					</ng-container>

					<p class="label">{{'library.neighborhood-demographics.age.title' | translate}}</p>
				</div>
			</div>
			<div class="chart-ctn block">
				<div>
					<ng-container>
						<canvas baseChart
							[type]="'doughnut'"
							[data]="householdData"
							(chartHover)="chartHovered($event)">
						</canvas>
					</ng-container>
					<p class="label">{{'library.neighborhood-demographics.groups.title' | translate}}</p>
				</div>
			
			</div>
			<div class="chart-ctn block">
				<div>
					<ng-container>
						<canvas baseChart
							[type]="'doughnut'"
							[data]="languageData"
							(chartHover)="chartHovered($event)">
						</canvas>
					</ng-container>
					<p class="label">{{'library.neighborhood-demographics.lang.title' | translate}}</p>
				</div>
			</div>
			<div class="stats-ctn block">
				<div class="stats-wrap">
					<div class="stats" *ngIf="neighborhood.area">
						<p class="name">{{'library.neighborhood-demographics.area' | translate}}</p>
						<p class="value">{{neighborhood.area}} <span>km<sup>2</sup></span></p>
					</div>
					<div class="stats" *ngIf="neighborhood.population">
						<p class="name">{{'library.neighborhood-demographics.number' | translate}}</p>
						<p class="value">{{neighborhood.population}}</p>
					</div>
					<div class="stats" *ngIf="neighborhood.density">
						<p class="name">{{'library.neighborhood-demographics.density' | translate}}</p>
						<p class="value">{{neighborhood.density}} <span>hab./km<sup>2</sup></span></p>
					</div>
				</div>
			</div>
		</div>
	</div>	
</div>
