import { Component, OnInit, Input } from '@angular/core';

@Component({
  selector: 'lib-property-sheet',
  templateUrl: './property-sheet.component.html'
})

export class PropertySheetComponent implements OnInit {
  @Input() property;
  @Input() brokers;
  @Input() openHouses;
  @Input() rooms;
  @Input() addenda;
  @Input() characteristics;
  @Input() expenses;
  @Input() documentsList;

  public detectMobile: boolean;

  constructor () {
    const width = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;
    if (width <= 992) this.detectMobile = true;
  }

  ngOnInit () {
  }
}
