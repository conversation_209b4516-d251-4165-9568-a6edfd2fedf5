import { Component, OnInit, Input } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';

import Swiper from 'swiper';
import { Controller, Navigation, Pagination, Autoplay } from 'swiper/modules';

@Component({
  selector: 'lib-property-hero',
  templateUrl: './property-hero.component.html'
})

export class PropertyHeroComponent implements OnInit {
  @Input() property;
  @Input() photos;

  public loading: boolean = true;
  public isMobile: boolean;

  // public videoUrl: any;

  public propertySwiper: any;
  public propertySwiperGalleryTop: any;
  public propertySwiperGalleryThumbs: any;
  public currentLang: any;
  public currentUrl: string;
  private dropdownOpen = false;

  constructor (
    private sanitizer: DomSanitizer
  ) {
    const userLang = window.location.pathname.split('/')[1];
    this.currentLang = userLang || 'fr';
    this.isMobile = (window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth) <= 992;
    this.currentUrl = window.location.href;
  }

  ngOnInit() {}

  initVideo (value) {
    Swiper.use([Navigation, Pagination, Controller, Autoplay]);
    // this.videoUrl = this.sanitizer.bypassSecurityTrustResourceUrl(value.video);

    this.propertySwiper = new Swiper('.js-swiper .swiper-container', {
      speed: 1000,
      // autoplayDisableOnInteraction: false,
      runCallbacksOnInit: false,
      loop: true,
      simulateTouch: false,
      navigation: {
        nextEl: '.swiper-button-next',
        prevEl: '.swiper-button-prev'
      },
      autoplay: {
        delay: 5000,
      },
      // pagination: {
      //   el: '.swiper-pagination',
      //   type: 'fraction'
      // }
    });

    this.propertySwiperGalleryTop = new Swiper('.gallery-top', {
      spaceBetween: 10,
      navigation: {
        nextEl: '.swiper-button-next',
        prevEl: '.swiper-button-prev'
      },
      pagination: {
        el: '.swiper-pagination',
        type: 'fraction'
      }
    });

    this.propertySwiperGalleryThumbs = new Swiper('.gallery-thumbs', {
      spaceBetween: 10,
      centeredSlides: true,
      slidesPerView: 'auto',
      touchRatio: 0.2,
      slideToClickedSlide: true
    });

    this.propertySwiperGalleryTop.controller.control = this.propertySwiperGalleryThumbs;
    this.propertySwiperGalleryThumbs.controller.control = this.propertySwiperGalleryTop;

    this.loading = false;
  }

  onOpenFullScreen (event) {
    event.stopPropagation();

    this.propertySwiperGalleryTop.slideTo(this.propertySwiper.realIndex, 0);
    document.querySelectorAll('#fullSlider')[0].classList.add('-opened');
    document.querySelectorAll('#fullSlider')[0].classList.add('-gallery');
    document.querySelectorAll('#fullSlider')[0].classList.remove('-video');
    document.documentElement.classList.add('-no-scroll');

    // fix IE Swiper offset
    // Doesn't change anything on Chrome (Only launch resize event once which recalculate the slides)
    setTimeout(function () {
      window.dispatchEvent(new Event('resize'));
    }, 750);
  }

  onOpenFullScreenVideo (event) {
    if (this.isMobile) return;

    event.preventDefault();
    event.stopPropagation();

    document.querySelectorAll('#fullSlider')[0].classList.add('-opened');
    document.querySelectorAll('#fullSlider')[0].classList.remove('-gallery');
    document.querySelectorAll('#fullSlider')[0].classList.add('-video');
    document.documentElement.classList.add('-no-scroll');

    // fix IE Swiper offset
    // Doesn't change anything on Chrome (Only launch resize event once which recalculate the slides)
    setTimeout(() => { window.dispatchEvent(new Event('resize')); }, 750);
  }

  onCloseFullScreen () {
    document.querySelectorAll('.full-screen-cpn')[0].classList.remove('-opened');
    document.documentElement.classList.remove('-no-scroll');
  }

  stopEvent (event) {
    event.stopPropagation();
  }

  setImageSize ({ target }) {
    const img = target as HTMLImageElement;
    img.width = img.naturalWidth;
    img.height = img.naturalHeight;
  }

  hasBothSaleAndRentalPrices () {
    const { price_sale: sale, price_rental: rental } = this.property;
    return sale && rental;
  }

  toggleDropdown() {
      this.dropdownOpen = !this.dropdownOpen;
  }
}
