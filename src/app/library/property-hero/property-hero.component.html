<div *ngIf="loading" class="hero-loader">
  <div class="loading-circle"></div>
</div>

<div class="property-hero-cpn" [class.loading]="loading">
  <!-- Slider main container -->
  <div class="swiper js-swiper">
    <div class="swiper-container">

      <div class="swiper-wrapper" *ngIf="photos">
        <div class="swiper-slide" *ngFor="let photo of photos.data" (click)="onOpenFullScreen($event)">
          <a href="{{ currentUrl }}" (click)="$event.preventDefault()">
            <img src="{{ photo.url.fullsize }}" alt="{{ property?.ext_address || '' }}" data-unlazy="true"
              onerror="this.src='assets/images/placeholder/propriete-nb-wide.jpg';" (load)="setImageSize($event)">
          </a>
        </div>
      </div>

      <div class="container" *ngIf="property">
        <div class="absolute-ctn grid">

          <div class="button-ctn col-t-lg-8 col-12">
            <div class="see-more-list" (click)="onOpenFullScreen($event)">
              <i class="icon-camera"></i>
              <div class="swiper-pagination">
                <span class="total-photos" *ngIf="photos">{{ photos.data.length }}</span>
                <!-- otherwise reactive swiper pagination in .ts -->
              </div>
              <p>{{ 'library.property-hero.see-photos' | translate }}</p>
            </div>

            <div *ngIf="property.links.length > 0" class="dropdown" (click)="toggleDropdown()">
              <div class="visit-list" [class.dropdown-open]="dropdownOpen">
                <i class="icon-house_360"></i>
                <p>{{ 'library.property-hero.virtual-visit' | translate }}</p>
                <i class="icon-fill-play"></i>
              </div>
              <ul class="options" *ngIf="dropdownOpen">
                  <ng-container *ngFor="let link of property.links">
                      <li>
                          <a *ngIf="link.link_type === 'VIS3D'" target="_blank" [href]="currentLang === 'en' ? link.link_url_en : link.link_url">{{ 'library.property-hero.see-3d' | translate }}</a>
                          <a *ngIf="link.link_type === 'VISVID'" target="_blank" [href]="currentLang === 'en' ? link.link_url_en : link.link_url">{{ 'library.property-hero.see-video' | translate }}</a>
                          <a *ngIf="link.link_type === 'VISAER'" target="_blank" [href]="currentLang === 'en' ? link.link_url_en : link.link_url">{{ 'library.property-hero.see-air' | translate }}</a>
                          <a *ngIf="link.link_type === 'VISCOM' || link.link_type === 'VISAUT'" target="_blank" [href]="currentLang === 'en' ? link.link_url_en : link.link_url">{{ 'library.property-hero.see-other' | translate }}</a>
                      </li>
                  </ng-container>
              </ul>
            </div>

            <!-- <div class="play-btn" *ngIf="property.video" (click)="onOpenFullScreenVideo($event)"> -->
            <!-- <div class="play-btn" *ngIf="property.video">
              <a href="{{ property.video }}" target="_blank" class="icon-play"></a>
            </div> -->
            
            <!-- <div class="player360" *ngIf="property.visit_360">
              <a href="{{ property.visit_360 }}" target="_blank" class="icon-360"></a>
            </div> -->
          </div>

          <ng-container *ngIf="!property.status">
            <div class="price-tag">
              <p *ngIf="property.price_sale">
                <span *ngIf="property.price_sale_unit" class="s -inner">
                  {{ property.price_sale | currency:'CAD':'symbol-narrow':'1.2-2':this.currentLang }} / {{ property.price_sale_unit | lowercase }}
                </span>

                <span *ngIf="!property.price_sale_unit" class="s -inner">
                  {{ property.price_sale | currency:'CAD':'symbol-narrow':'2.0-0':this.currentLang }}
                  <span *ngIf="property.taxes_sale_not_included" class="small-tps">+tps/tvq</span>
                </span>

                <span class="under-tag" *ngIf="hasBothSaleAndRentalPrices()">
                  {{ 'global.sale' | translate }}
                </span>
              </p>

              <p *ngIf="property.price_rental">
                <span class="c -inner" *ngIf="property.property_category !== 'Commercial'">
                  {{ property.price_rental | currency:'CAD':'symbol-narrow':'2.0-0':this.currentLang }} 
                  <span class="small-tps">{{ 'library.property-hero.per-month' | translate }}</span>
                </span>
  
                <span class="c a -inner" *ngIf="property.property_category == 'Commercial' && property.rental_period == 'A'">
                  {{ property.price_rental | currency:'CAD':'symbol-narrow':'2.0-0':this.currentLang }} 
                  <span class="small-tps">{{ 'library.property-hero.commercial-year' | translate }}</span>
                </span>
  
                <span class="c m -inner" *ngIf="property.property_category == 'Commercial' && property.rental_period != 'A'">
                  {{ property.price_rental | currency:'CAD':'symbol-narrow':'2.0-0':this.currentLang }} 
                  <span class="small-tps">{{ 'library.property-hero.commercial-month' | translate }}</span>
                </span>

                <span class="under-tag" *ngIf="hasBothSaleAndRentalPrices()">
                  {{ 'global.rental' | translate }}
                </span>
              </p>
            </div>
          </ng-container>

          <ng-container *ngIf="property.status">
            <p class="price-tag -sold">
              <span class="-inner">{{ 'library.property-hero.sold' | translate }}</span>
            </p>
          </ng-container>
        </div>
      </div>

      <div class="swiper-button-prev swiper-btn" (click)="stopEvent($event)"><i></i></div>
      <div class="swiper-button-next swiper-btn" (click)="stopEvent($event)"><i></i></div>

    </div>
  </div>
</div>

<div class="full-screen-cpn" id="fullSlider">
  <div class="close" (click)="onCloseFullScreen()">
    <span class="icon-close"></span>
  </div>
  
  <!-- Swiper -->
  <div class="background" (click)="onCloseFullScreen()"></div>

  <div class="swiper-container gallery-top">
    <div class="swiper-wrapper" *ngIf="photos">
      <ng-container *ngFor="let photo of photos.data ; let i = index;">
        <div class="swiper-slide">
          <img src="{{ photo.url.fullsize }}" alt="{{ property?.ext_address || '' }}" onerror="this.src='assets/images/placeholder/propriete-nb-wide.jpg';">
          <div class="info-ctn">{{ i + 1 }} / {{ photos.total }} - {{ photo.code }}</div>
        </div>
      </ng-container>
    </div>

    <!-- Add Arrows -->
    <div class="swiper-button-next swiper-button-white"></div>
    <div class="swiper-button-prev swiper-button-white"></div>
    <div class="info-ctn hide">
      <div class="swiper-pagination"></div>
    </div>
  </div>

  <div class="swiper-container gallery-thumbs">
    <div class="swiper-wrapper" *ngIf="photos">
      <ng-container *ngFor="let photo of photos.data">
        <div class="swiper-slide" [ngStyle]="{'background-image': 'url(' + photo.url.fullsize + ')'}"></div>
      </ng-container>
    </div>
  </div>
  
  <!-- <div *ngIf="videoUrl" class="video-container">
    <div class="video-wrapper">
      <iframe class="e2e-iframe-trusted-src" [src]="videoUrl" frameborder="0"
        allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
    </div>
  </div> -->
</div>