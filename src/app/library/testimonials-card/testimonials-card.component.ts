import { Component, OnInit, Input } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';

@Component({
  selector: 'lib-testimonials-card',
  templateUrl: './testimonials-card.component.html'
})

export class TestimonialsCardComponent implements OnInit {
  @Input() testimonial;

  public videoUrl: any;

  constructor (
    private sanitizer: DomSanitizer
  ) { }

  ngOnInit () {
    this.videoUrl = this.sanitizer.bypassSecurityTrustResourceUrl(this.testimonial.video_embed);
  }
}
