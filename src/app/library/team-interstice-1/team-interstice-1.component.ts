import { Component, OnInit } from '@angular/core';
import { TeamsMembersService } from '@/services/v3/teammembers/teammembers.service';

@Component({
  selector: 'lib-team-interstice-1',
  templateUrl: './team-interstice-1.component.html'
})

export class TeamInterstice1Component implements OnInit {
  public teamMembers = [];

  constructor (private teamService: TeamsMembersService) {
    this.teamService.getTeams().subscribe(({ data }) => {
      this.teamMembers = data;
    });
  }

  ngOnInit () {
  }
}
