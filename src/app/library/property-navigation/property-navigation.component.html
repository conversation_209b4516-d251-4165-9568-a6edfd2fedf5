<div class="property-navigation-cpn">
	<div class="previous-ctn">
		<div class="property-nav-ctn" [routerLink]="previousLink" *ngIf='previousNav'>
			<div class="arrow-ctn -left-arrow">
				<i class="icon-arrow-left"></i>
			</div>
			<div class="nav-info -left-nav">
				<div *ngIf="previousNav.ext_coverphoto" class="img-ctn">
					<img src="{{previousNav.ext_coverphoto}}" alt="">
				</div>
				<div class="text-ctn">
					<p class="title">{{ 'library.property-navigation.previous-property' | translate }}</p>
					<a class="location" href="{{ previousLink }}">{{ previousNav.ext_address || previousNav.address_street || previousNav.municipality_label || previousNav.mls }}</a>
				</div>
			</div>
		</div>
	</div>
	<div class="next-ctn">
		<div class="property-nav-ctn" [routerLink]="nextLink" *ngIf='nextNav'>
			<div class="nav-info -right-nav">
				<div class="text-ctn">
					<p class="title">{{ 'library.property-navigation.next-property' | translate }}</p>
					<a class="location" href="{{ nextLink }}">{{ nextNav.ext_address || nextNav.address_street || nextNav.municipality_label || nextNav.mls }}</a>
				</div>
				<div *ngIf="nextNav.ext_coverphoto" class="img-ctn">
					<img src="{{nextNav.ext_coverphoto}}" alt="">
				</div>
			</div>
			<div class="arrow-ctn -right-arrow">
				<i class="icon-arrow-right"></i>
			</div>
		</div>
	</div>
</div>