import { Component, OnInit, Input, SimpleChang<PERSON>, OnChanges } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'lib-property-navigation',
  templateUrl: './property-navigation.component.html'
})

export class PropertyNavigationComponent implements OnInit, OnChanges {
  @Input() previousNav;
  @Input() nextNav;

  nextLink;
  previousLink;

  constructor (
    public translate: TranslateService
  ) { }

  ngOnInit () {
  }

  ngOnChanges (changes: SimpleChanges) {
    if (this.nextNav) { this.setupNext(); }
    if (this.previousNav) { this.setupPrevious(); }
  }

  setupNext () {
    if (this.nextNav.neighborhood_slug) {
      this.nextLink = this.translate.instant('urls.property-single') +
        '/' + this.nextNav.municipality_slug +
        '/' + this.nextNav.neighborhood_slug +
        '/' + this.nextNav.ext_address_slug +
        '/' + this.nextNav.mls
      ;
    } else {
      this.nextLink = this.translate.instant('urls.property-single') +
        '/' + this.nextNav.municipality_slug +
        '/' + this.nextNav.ext_address_slug +
        '/' + this.nextNav.mls
      ;
    }
  }

  setupPrevious () {
    if (this.previousNav.neighborhood_slug) {
      this.previousLink = this.translate.instant('urls.property-single') +
        '/' + this.previousNav.municipality_slug +
        '/' + this.previousNav.neighborhood_slug +
        '/' + this.previousNav.ext_address_slug +
        '/' + this.previousNav.mls
      ;
    } else {
      this.previousLink = this.translate.instant('urls.property-single') +
        '/' + this.previousNav.municipality_slug +
        '/' + this.previousNav.ext_address_slug +
        '/' + this.previousNav.mls
      ;
    }
  }
}
