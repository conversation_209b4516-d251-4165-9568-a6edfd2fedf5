import { Component, OnInit } from '@angular/core';
import { BlocksService } from '@/services/v3/contentblocks/contentblocks.service';

@Component({
  selector: 'lib-sell-sheet',
  templateUrl: './sell-sheet.component.html'
})

export class SellSheetComponent implements OnInit {
  public blockTitle: string;
  public blockContent: string;

  constructor (private blocksService: BlocksService) {
    this.blocksService.getBlock('bloc-vendre').subscribe(data => {
      this.blockTitle = data.title;
      this.blockContent = data.text;
      document.querySelector('.sell-sheet-page').classList.add('show');
    });
  }

  ngOnInit () {
  }
}
