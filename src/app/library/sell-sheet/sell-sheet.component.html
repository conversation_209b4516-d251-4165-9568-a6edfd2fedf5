<div class="sell-sheet-page container grid -space-default">

	<div class="page-list-ctn col-12 col-t-lg-8">
		<h1 *ngIf="blockTitle" class="page-title">{{ blockTitle }}</h1>
		<div class="page-description" [innerHtml]="blockContent"></div>
	</div>

	<div class="team-info-wrap col-12 col-t-lg-4">
		<div class="team-info-box">
			<div class="team-info-box-content">
				<p class="title">{{ 'library.sell-sheet.infobox.title' | translate }}</p>
				<p class="description">{{ 'library.sell-sheet.infobox.description' | translate }}</p>
				<a [routerLink]="['urls.contact' | translate ]" class="main-button -primary">{{ 'library.sell-sheet.infobox.button' | translate }}</a>
			</div>
		</div>
	</div>
	
</div>
