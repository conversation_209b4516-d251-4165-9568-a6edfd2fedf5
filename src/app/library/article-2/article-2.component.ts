import { Component, OnInit, Input } from '@angular/core';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'lib-article-2',
  templateUrl: './article-2.component.html',
  host: {
    '(click)': 'onClick()'
  }
})

export class Article2Component implements OnInit {
  @Input() blogPost;

  constructor(
    private router: Router,
    private translate: TranslateService
  ) { }

  ngOnInit() {
  }

  onClick() {
    const blogUrl = this.translate.instant('urls.real-estate-blog');
    this.router.navigate([blogUrl, this.blogPost.slug]);
  }
}
