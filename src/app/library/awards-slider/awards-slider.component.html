<div id="awards-slider-cpn" class="awards-slider-cpn -invisible -space-default" *ngIf="awards?.length">
	<div class="container">
		<div class="inner">
			<h2>{{ 'library.awards-slider.title' | translate }}</h2>

			<div class="swiper-container">
				<div class="swiper-wrapper">
					<div class="swiper-slide" *ngFor="let award of awards;">
						<lib-award-card [award]="award"></lib-award-card>
					</div>
				</div>
			</div>

			<div *ngIf="awards?.length > 2" class="nav-ctn">
				<a class="swiper-button-prev awards-button-prev swiper-btn"></a>
				<a class="swiper-button-next awards-button-next swiper-btn"></a>
			</div>
		</div>
	</div>
</div>
