import { Component, Input, OnInit } from '@angular/core';
import { BlogService } from '@/services/v3/blog/blog.service';

@Component({
  selector: 'lib-blog-list-1',
  templateUrl: './blog-list-1.component.html'
})

export class BlogList1Component implements OnInit {
  @Input() category = undefined;
  public blogPosts = [];

  constructor (
    private blog: BlogService
  ) {}

  ngOnInit () {
    this.blog.getPosts(3, this.category).subscribe(({ data }) => {
      this.blogPosts = data;
    });
  }
}
