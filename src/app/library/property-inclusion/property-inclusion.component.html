<div class="dual-wrap">
	<div class="property-inclusion-cpn dual-div" *ngIf="included">
		<h3 class="-emphase-title">{{ 'library.property-inclusion.inclusion' | translate }}</h3>
		<div class="text-wrap">
			<p class="--small">{{included}}</p>
		</div>
	</div>
	<div class="property-exclusion-cpn dual-div" *ngIf="excluded">
		<h3 class="-emphase-title">{{ 'library.property-inclusion.exclusion' | translate }}</h3>
		<div class="text-wrap">
			<p class="--smal">{{excluded}}</p>
		</div>
	</div>
</div>
