import { Component, OnInit } from '@angular/core';
import { ProgramsService } from '@/services/v3/programs/programs.service';

@Component({
  selector: 'lib-programs-list',
  templateUrl: './programs-list.component.html'
})

export class ProgramsListComponent implements OnInit {
  programs = [];

  constructor (
    private programsService: ProgramsService
  ) {}

  ngOnInit () {
  }

  ngAfterViewInit () {
    this.programsService.getPrograms().subscribe(({ data }) => {
      this.programs = data;
    });
  }
}
