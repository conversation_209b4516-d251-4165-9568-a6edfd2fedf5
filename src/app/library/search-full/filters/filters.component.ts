import { Component, OnInit, EventEmitter, Input, Output } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

import noUiSlider from 'nouislider';
import numeral from 'numeral';
import { UtilsService } from '@/services/v3/utils/utils.service';

@Component({
  selector: 'lib-filters',
  templateUrl: './filters.component.html'
})

export class FiltersComponent implements OnInit {
  @Input() search;
  @Input() properties;
  @Input() propertyTypes;
  @Input() showMapOnly;
  @Output() changeFilter: EventEmitter<any> = new EventEmitter<any>();
  @Output() changeCategory: EventEmitter<any> = new EventEmitter<any>();

  @Input() settings;
  @Output() toggleMap: EventEmitter<any> = new EventEmitter<any>();

  isMobile;

  // Properties categories and types
  propertyCategories;
  inscriptionTypes;

  searchPlaceholder = '';
  searchString = '';
  searchModified = false;

  // List of filtered properties in seachbox dropdown
  searchboxProperties = [];

  // Values and properties for filter fields
  // TODO: Make dynamic
  filterLabels = {
    rooms_bedroom_total_number: [1, 2, 3, 4, 5],
    parking: [1, 2, 3, 4, 5],
    rooms_bathroom_number: [1, 2, 3, 4, 5],
    garage: [1, 2, 3, 4, 5]
  }

  // Currently applied filters
  activeFilters = {
    types: {},

    priceMin: null,
    priceMax: null,
    rentalPriceMin: null,
    rentalPriceMax: null,

    rooms_bedroom_total_number: null,
    rooms_bathroom_number: null,
    openhouses: null,

    surfaceMin: null,
    surfaceMax: null,
    parking: null,
    garage: null,
    rf: null
  }

  filtersVisible = false;
  maxPriceValue: any;
  maxDropdownProperties = 5;

  // Slider HTML objects
  priceSlider: any;
  rentalPriceSlider: any;
  surfaceSlider: any;

  // Switching this to true enables the residential/commercial selector in search bar
  commercialEnabled = true;

  constructor (
    private translate: TranslateService,
    private utils: UtilsService
  ) {}

  ngOnInit () {
    // Set category to residential by default if commercial properties are disabled
    if (!this.commercialEnabled) this.search.searchType = 'residential';
    
    // Get translation of selector Residential / Commercial
    this.translate.get('library.search-full.filters.property-categories').subscribe(res => {
      this.propertyCategories = res;
    });

    // Get translation of selector Sell / Locate
    this.translate.get('library.search-full.filters.property-types').subscribe(res => {
      this.inscriptionTypes = res;
    });

    // Fetch search field placeholder text if switched mobile/desktop
    this.isMobile = (window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth) <= 992;
    const ph = 'library.search-full.filters.property-search' + (this.isMobile ? '-mobile' : '');
    this.translate.get(ph).subscribe(res => {
      this.searchPlaceholder = res;
    });

    this.searchString = this.search.keyword;
    
    this.getDataFromLocalStorage();

    this.submitFilters();
  }

  ngOnChanges (changes) {
    // Update search field dropdown properties
    if (this.search.keyword && this.search.keyword.length > 1 && changes.properties && changes.properties.currentValue) {
      const keywords = this.search.keyword.toLowerCase().split("-");
      this.searchboxProperties = changes.properties.currentValue
        .slice(0, this.maxDropdownProperties)
        .map(p => {
          // Add full address for use in search dropdown
          const { ext_address: address, municipality_label: municipality, neighborhood_label: neighborhood, mls } = p;
          let fullAddress = address + (municipality ? ', ' + municipality : '') + (neighborhood ? ', ' + neighborhood : '') + ' - #' + mls;

        // Highlight matching keywords in fullAddress
        keywords.forEach(keyword => {
          const regex = new RegExp(keyword, "ig");
          fullAddress = fullAddress.replace(regex, match => `<b>${match}</b>`);
        });

          return { ...p, fullAddress };
        });
    } else {
      this.searchboxProperties = null;
    }
  }

  propertyTypesKeys () {
    return Object.keys(this.propertyTypes);
    
  }

  //
  toggleMapPane (show = null) {
    this.showMapOnly = show;
    this.toggleMap.emit(show);
  }

  // Search handlers
  onSearch (event: any) {
    // Prevents opening search dropdown on load in case of url param
    if (!this.searchModified) this.searchModified = true;

    if (event && this.slugify(event.target.value)) {
      this.search.keyword = this.slugify(event.target.value);
    } else {
      delete this.search.keyword;
    }
    this.changeFilter.emit({ type: 'keyword' });
  }

  clearSearch () {
    this.searchString = undefined;
    this.onSearch(null);
    this.searchboxProperties = [];
  }

  // Filters handlers
  submitFilters (toggle = false) {
    this.search.filters = { ...this.activeFilters };
    // Save filters and selections to localStorage
    localStorage.setItem('searchFilters', JSON.stringify(this.activeFilters));
    if (this.search.searchType) {
      localStorage.setItem('searchType', this.search.searchType);
    }
    if (this.search.selectedInscriptionType) {
      localStorage.setItem('selectedInscriptionType', this.search.selectedInscriptionType.toString());
    }
    
    this.changeFilter.emit({ type: 'filters' });
    if (toggle) this.toggleFilters();
  }

  // Category change
  onChangeCategory () {
    this.changeCategory.emit();
    this.properties = [];
    this.filtersVisible = false;
    this.resetFilters(true);
    this.toggleFilters();
  }

  toggleFilters () {
    this.filtersVisible = !this.filtersVisible;
  }

  scrollToResult () {
    setTimeout(() => {
      const yOffset = -83;
      const el = document.getElementById('properties-list');
      if (!el) return;
      const y = el.getBoundingClientRect().top + window.pageYOffset + yOffset;
      window.scrollTo({ top: y, behavior: 'smooth' });
    }, 100);
  }

  resultFilters () {
    this.toggleFilters();
    this.scrollToResult();
  }

  resetFilters (category: boolean = false) {
    this.priceSlider.reset();
    this.rentalPriceSlider.reset();

    this.activeFilters.types = {};

    if (!category) {
      this.search.searchType = 'anyCategory';
    }
    this.search.selectedInscriptionType = 0;

    this.activeFilters.priceMin = null;
    this.activeFilters.priceMax = null;
    this.activeFilters.rentalPriceMin = null;
    this.activeFilters.rentalPriceMax = null;

    this.activeFilters.rooms_bedroom_total_number = null;
    this.activeFilters.rooms_bathroom_number = null;
    this.activeFilters.openhouses = null;

    // TODO: Add to API model?
    this.activeFilters.surfaceMin = null;
    this.activeFilters.surfaceMax = null;
    this.activeFilters.parking = null;
    this.activeFilters.garage = null;
    this.activeFilters.rf = null;

    // Clear saved filters and selections from localStorage
    localStorage.removeItem('searchFilters');
    localStorage.removeItem('searchType');
    localStorage.removeItem('selectedInscriptionType');

    this.submitFilters(true);

    this.scrollToResult();
  }

  // Sliders
  initPriceSlider (maxPrice = 100) {
    this.maxPriceValue = maxPrice;

    this.priceSlider = noUiSlider.create(document.querySelector('#price-picker'), {
      start: [this.activeFilters.priceMin || 0, this.activeFilters.priceMax || maxPrice],
      connect: true,
      step: 10000,
      tooltips: [true, true],
      range: { min: 0, max: maxPrice },
      format: {
        to: value => value >= 1000000 ? numeral(value).format('0.0a$') : numeral(value).format('0a$'),
        from: value => parseInt(value)
      }
    });

    this.priceSlider.on('change', e => {
      this.activeFilters.priceMin = numeral(e[0]).value();
      this.activeFilters.priceMax = numeral(e[1]).value();
      this.submitFilters();
    });
  }

  initRentalPriceSlider (maxPrice = 100) {
    this.maxPriceValue = maxPrice;

    this.rentalPriceSlider = noUiSlider.create(document.querySelector('#rental-price-picker'), {
      start: [this.activeFilters.rentalPriceMin || 0, this.activeFilters.rentalPriceMax || maxPrice],
      connect: true,
      step: 100,
      tooltips: [true, true],
      range: { min: 0, max: maxPrice },
      format: {
        to: value => numeral(value).format('0a$'),
        from: value => parseInt(value)
      }
    });

    this.rentalPriceSlider.on('change', e => {
      this.activeFilters.rentalPriceMin = numeral(e[0]).value();
      this.activeFilters.rentalPriceMax = numeral(e[1]).value();
      this.submitFilters();
    });
  }

  // Tools
  getPropertyUrl (property) {
    if (property.neighborhood_slug) {
      return this.translate.instant('urls.property-single') +
        '/' + property.municipality_slug +
        '/' + property.neighborhood_slug +
        '/' + property.ext_address_slug +
        '/' + property.mls;
    } else {
      return this.translate.instant('urls.property-single') +
        '/' + property.municipality_slug +
        '/' + property.ext_address_slug +
        '/' + property.mls;
    }
  }

  slugify (str) {
    return this.utils.slugify(str);
  }

  delayDropdownClose () {
    setTimeout(() => {
      this.searchboxProperties = [];
    }, 100);
  }

  getDataFromLocalStorage () {
    const savedFilters = localStorage.getItem('searchFilters');
    if (savedFilters) {
      this.activeFilters = JSON.parse(savedFilters);
    }

    // Load saved searchType after propertyCategories is populated
    const savedSearchType = localStorage.getItem('searchType');
    if (savedSearchType) {
      this.search.searchType = savedSearchType;
    }
    
    // Load saved inscriptionType after inscriptionTypes is populated
    const savedInscriptionType = localStorage.getItem('selectedInscriptionType');
    if (savedInscriptionType) {
      this.search.selectedInscriptionType = parseInt(savedInscriptionType);
    }
  }
}
