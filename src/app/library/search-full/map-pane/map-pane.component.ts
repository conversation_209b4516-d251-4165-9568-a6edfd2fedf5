import { Component, OnInit, Input } from '@angular/core';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';

import { Observable } from 'rxjs';
import { MapService } from '@/services/v3/map/map.service';
import { Popup } from 'mapbox-gl';

@Component({
  selector: 'lib-map-pane',
  templateUrl: './map-pane.component.html'
})

export class MapPaneComponent implements OnInit {
  @Input() properties;
  @Input() panelStatus: Observable<any>;
  @Input() showMapOnly: Observable<any>;

  map: any;
  geoJSON = [];

  lat: number;
  lng: number;
  zoom: number;
  mapColor: string;
  mapColorSold: string;

  constructor (
    private router: Router,
    private translate: TranslateService,
    private mapService: MapService
  ) {}

  async ngOnInit () {
    // Fetch color and coordinates from client infos
    const { color, soldColor, neighborhood: { lat, lng, zoom } } = await this.translate.get('client.map').toPromise();
    this.mapColor = color;
    this.mapColorSold = soldColor;
    this.lat = lat;
    this.lng = lng;
    this.zoom = zoom;

    window.onresize = () => this.resizeMap();
  }

  async initMap () {
    // Create map object
    this.map = this.mapService.createMap();

    // Add layers and events
    this.map.on('load', async () => {
      this.map.addSource('properties', {
        type: 'geojson',
        data: { type: 'FeatureCollection', features: this.geoJSON },
        cluster: true
      });

      // Add clusters icons
      this.map.addLayer({
        id: 'clusters',
        type: 'circle',
        source: 'properties',
        filter: ['has', 'point_count'],
        paint: { 'circle-color': this.mapColor, 'circle-radius': 18, 'circle-stroke-width': 6, 'circle-stroke-color': this.mapColor, 'circle-stroke-opacity': 0.5 }
      });

      // Add clusters count
      this.map.addLayer({
        id: 'cluster-count',
        type: 'symbol',
        source: 'properties',
        filter: ['has', 'point_count'],
        layout: {
          'text-field': '{point_count_abbreviated}',
          'text-font': ['Arial Unicode MS Bold'],
          'text-size': 14
        },
        paint: { 'text-color': '#ffffff' }
      });

      // Add property points
      this.map.addLayer({
        id: 'points',
        type: 'circle',
        source: 'properties',
        filter: ['!', ['has', 'point_count']],
        paint: {
          'circle-radius': 10,
          'circle-color': ['match', ['get', 'isSold'], 'true', this.mapColorSold, this.mapColor],
          'circle-stroke-color': ['match', ['get', 'isSold'], 'true', this.mapColorSold, this.mapColor],
          'circle-stroke-width': 4,
          'circle-stroke-opacity': 0.5
        }
      });

      // Open cluster / recenter map on click
      this.map.on('click', 'clusters', async e => {
        const features = this.map.queryRenderedFeatures(e.point, { layers: ['clusters'] });
        const { cluster_id: id, point_count: nb } = features[0].properties;
        this.map.getSource('properties').getClusterLeaves(id, nb, 0, (err, properties) => {
          if (!err) this.resizeMap(properties);
        });
      });

      // Show popup on pin click
      this.map.on('click', 'points', e => {
        const property = e.features[0].properties;
        const { coordinates: center } = e.features[0].geometry;

        // Center map to selected point
        const offset = [!this.isMobile() && this.panelStatus ? (window.innerWidth / 4) : 0, 0];
        this.map.flyTo({ center, offset });

        // Generate html popup and attach to map
        const html = this.propertyPopup(property);
        const popup = new Popup()
          .setLngLat(center)
          .setHTML(html)
          .addTo(this.map);

        // Add popup click event
        const url = this.translate.instant('urls.property-single') +
          '/' + property.municipality_slug +
          (property.neighborhood_slug ? '/' + property.neighborhood_slug : '') +
          '/' + property.ext_address_slug +
          '/' + property.mls;
        popup.getElement().querySelector('.inner-popup').addEventListener('click', () => this.router.navigateByUrl(url));
      });

      // Cluster hover event
      this.map.on('mouseenter', ['clusters', 'points'], () => { this.map.getCanvas().style.cursor = 'pointer'; });
      this.map.on('mouseleave', ['clusters', 'points'], () => { this.map.getCanvas().style.cursor = ''; });
    });
  }

  // Replace property list, then update the map
  updateProperties (properties) {
    this.properties = properties;
    this.geoJSON = this.properties.map(property => ({
      type: 'Feature',
      properties: { ...property, isSold: property.status ? 'true' : '' },
      geometry: { type: 'Point', coordinates: [property.geo_longitude, property.geo_latitude] }
    }));

    if (this.map && this.map.loaded()) this.updateMap();
    else {
      // Delay map init until it is loaded
      const timeout = setInterval(() => {
        if (this.map && this.map.loaded()) {
          this.updateMap();
          clearInterval(timeout);
        }
      }, 200);
    }
  }

  // Set map source to the current geoJSON object, then resize (called from search-full parent component)
  updateMap () {
    this.map.getSource('properties').setData({ type: 'FeatureCollection', features: this.geoJSON });
    this.resizeMap();
  }

  // Reset map viewport to include all properties
  resizeMap (features = this.geoJSON) {
    if (features.length) {
      const bounds = this.mapService.getBoundaries(features);
      const padding = !this.isMobile() ? this.calculateMapPadding() : 70;
      this.map.fitBounds(bounds, { padding, linear: true, maxZoom: 18 });
    } else {
      this.map.flyTo({ center: [this.lng, this.lat], zoom: this.zoom });
    }
  }

  // Check if window is mobile size
  isMobile () {
    return (window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth) <= 992;
  }

  // Set padding-left to 50% if the side panel is open
  calculateMapPadding () {
    const basePaddingLR = 30;
    const basePaddingTB = 250;
    return {
      left: ~~(this.panelStatus ? window.innerWidth / 2 : 0) + basePaddingLR,
      right: basePaddingLR,
      top: basePaddingTB,
      bottom: basePaddingTB
    };
  }

  // Return a html string of a property popup
  propertyPopup (property) {
    // Price tag
    console.log(property);

    const priceTag = property.status
      ? property.property_type
      : (property.price_sale || property.price_rental + this.translate.instant('library.map-pane.per-month')) + ' $';

    // Generate html popup
    return `<div class="inner-popup" id="popup-${property.mls}">` +
      `<img src="${property.ext_coverphoto}" />` +
      '<div class="content">' +
      `<p class="price">${priceTag}</p>` +
      `<p class="location">${property.ext_address}</p>` +
      '</div></div>';
  }
}
