import { Component, OnInit, EventEmitter, Input, Output } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'lib-sortable',
  templateUrl: './sortable.component.html'
})
export class SortableComponent implements OnInit {
  public isMobile: boolean;

  @Input() settings;
  @Output() viewChange: EventEmitter<any> = new EventEmitter<any>();

  orders: any = [];

  constructor (private translate: TranslateService) {
    const width = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;

    if (this.translate.currentLang === 'fr') {
      this.orders = [
        { value: '-date_origin_created', name: 'Plus récentes' },
        { value: 'date_origin_created', name: 'Plus anciennes' },
        { value: '-price_sale', name: 'Plus chères' },
        { value: 'price_sale', name: 'Moins chères' }
      ];
    } else {
      this.orders = [
        { value: '-date_origin_created', name: 'Newest' },
        { value: 'date_origin_created', name: 'Oldest' },
        { value: '-price_sale', name: 'Higher priced' },
        { value: 'price_sale', name: 'Lower priced' }
      ];
    }

    if (width <= 992) {
      this.isMobile = true;
    }
  }

  ngOnInit () { }

  onChangeOrder () {
    this.viewChange.emit(true);
    const input = <HTMLElement>document.querySelector('.order-filter .ng-input input');
    input.blur();
  }

  onClickView (boolean) {
    this.settings.showMapOnly = boolean;
    this.viewChange.emit(true);
  }

  onSortableChange (value) {
    this.settings.order = value;
    this.viewChange.emit(true);
  }
}
