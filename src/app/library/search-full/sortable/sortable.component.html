<div *ngIf="!isMobile" class="search-sortable-cpn">
	<h3>{{ 'library.search-full.sortable.title' | translate }}</h3>

	<div class="right">
		<ul class="sortable-list tabs">
			<input class="input-tab" id="tab1" type="radio" name="tabs" checked>
			<label for="tab1" (click)="onSortableChange('-date_origin_created')">{{ 'library.search-full.sortable.newest' | translate }}</label>
			<input class="input-tab" id="tab2" type="radio" name="tabs">
			<label for="tab2" (click)="onSortableChange('date_origin_created')">{{ 'library.search-full.sortable.oldest' | translate }}</label>
			<input class="input-tab" id="tab3" type="radio" name="tabs">
			<label for="tab3" (click)="onSortableChange('-price_sale')">{{ 'library.search-full.sortable.higher-priced' | translate }}</label>
			<input class="input-tab" id="tab4" type="radio" name="tabs">
			<label for="tab4" (click)="onSortableChange('price_sale')">{{ 'library.search-full.sortable.lower-priced' | translate }}</label>
		</ul>
	</div>
</div>

<div *ngIf="isMobile" class="search-sortable-cpn">
	<div class="left">
		<div class="icons-ctn sortable-list tabs">
			<input class="input-tab" id="tab-house" type="radio" name="view" checked>
			<label for="tab-house" (click)="onClickView(false)">{{ 'library.search-full.sortable.house' | translate }}</label>
			<input class="input-tab" id="tab-map" type="radio" name="view">
			<label for="tab-map" (click)="onClickView(true)">{{ 'library.search-full.sortable.map' | translate }}</label>
		</div>
	</div>
	<div class="right">
		<!-- Orders  -->
		<ng-select
			[searchable]="false"
			[items]="orders"
			bindLabel="name"
			bindValue="value"
			[(ngModel)]="settings.order"
			[ngModelOptions]="{standalone: true}"
			(change)="onChangeOrder()"
			placeholder="Ordre" class="order-filter align-center">
		</ng-select>
	</div>
</div>
