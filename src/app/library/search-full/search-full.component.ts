import { Component, OnInit, ViewChild } from "@angular/core";
import { ActivatedRoute } from "@angular/router";
import { SessionStorageService } from "ngx-webstorage";

import { FiltersComponent } from "./filters/filters.component";
import { MapPaneComponent } from "./map-pane/map-pane.component";

import { InscriptionsService } from "@/services/v3/inscriptions/inscriptions.service";

@Component({
  selector: "lib-search-full",
  templateUrl: "./search-full.component.html"
})
export class SearchFullComponent implements OnInit {
  public firstLoad = true;

  @ViewChild(MapPaneComponent)
  private mapPaneComponent: MapPaneComponent;

  @ViewChild(FiltersComponent)
  private filtersComponent: FiltersComponent;

  searchModel = {
    keyword: "",
    searchType: "anyCategory",
    selectedInscriptionType: 0,
    filters: {}
  };

  search: any = { ...this.searchModel };

  properties: any;
  allProperties: any;
  propertyTypes: any = {};
  allPropertyTypes: any = {};

  sortable = "more_recent";
  currentPage = 1;
  loading = true;
  mobileMapVisible = false;
  panelStatus = "open"; // null || open || grid
  showMapOnly = true;

  constructor(
    private session: SessionStorageService,
    private inscriptionsService: InscriptionsService,
    private route: ActivatedRoute
  ) {}

  ngOnInit() {
    const cityParam = this.route.snapshot.paramMap.get("municipality");
    if (cityParam) {
      this.search.keyword = cityParam;
    }
  }

  async ngAfterViewInit() {
    this.getMaxPrice();
    this.getMaxRental();
    this.getMaxSurface();

    // Fetch types and sub-types for all of current broker's properties
    await this.getPropertyTypes();

    // If a keyword is included in url, preset initial search params
    const { keyword } = this.search;
    if (keyword) {
      // TODO: There should be an API endpoint to fetch a property by address slug, to avoid having to
      //  fetch all properties in order to figure out initial dropdown settings when a property is passed in url
      const { data } = await this.inscriptionsService
        .getInscriptions(-1, {})
        .toPromise();
      const {
        price_rental: rental,
        price_sale: sale,
        is_commercial: commercial,
        is_residential: residential
      } = data.filter((p) =>
        [p.ext_address_slug, p.ext_address].includes(keyword)
      )[0] || {};

      this.search.searchType = commercial
        ? "commercial"
        : residential
        ? "residential"
        : "anyCategory";
      this.search.selectedInscriptionType = rental ? 2 : sale ? 1 : 0;
    }

    this.getProperties();
  }

  // Fetch all property categories types
  async getPropertyTypes() {
    const { data } = await this.inscriptionsService.getTypes().toPromise();
    this.allPropertyTypes = [...data];
  }

  // Change current property categories depending on the selected category
  updatePropertyTypes() {
    if (!this.allPropertyTypes || !this.allPropertyTypes.length) {
      this.propertyTypes = {};
      return;
    }
    this.propertyTypes = this.allPropertyTypes.reduce((all, current) => {
      const list = [...(all[current.property_category] || [])];
      if (
        current["is_" + this.search.searchType] ||
        this.search.searchType === "anyCategory"
      ) {
        list.push(current);
      }
      return { ...all, [current.property_category]: list };
    });
  }

  // Fetch properties
  async getProperties() {
    this.loading = true;

    this.inscriptionsService
      .getInscriptions(-1, { ...this.search })
      .subscribe(({ data }) => {
        this.properties = data;
        this.allProperties = [...this.properties];
        // console.log(this.properties.map(p => p.property_type_key));
        this.updatePropertyTypes();

        // Init map if needed
        if (this.firstLoad) {
          setTimeout(() => {
            this.mapPaneComponent.initMap();
            this.firstLoad = false;
          }, 400);
        }

        this.loading = false;

        // Add/Overwrite to localStorage the new array
        this.addToLocalStorage();
        this.filterProperties();
      });
  }

  filterProperties() {
    const { keyword, filters } = this.search;
    let properties = [...this.allProperties];

    // Filter by keyword with pertinance 
    if (keyword) {
      const keywords = keyword.toLowerCase().split("-");
      properties = properties.map(p => {
        // set a relavance matching score
        let relevanceScore = 0;
        keywords.forEach(word => {
          if (
            p.ext_address.toLowerCase().includes(word) ||
            p.municipality_label.toLowerCase().includes(word) ||
            p.ext_address_slug.includes(word) ||
            p.municipality_slug.includes(word) ||
            p.neighborhood_slug.includes(word) ||
            String(p.mls).includes(word)
          ) {
            // If one of a keyword match with one rule, update score
            relevanceScore++;
          }
        });
        return { property: p, relevanceScore };
      });
    
      // remove properties with score 0
      properties = properties.filter(p => p.relevanceScore > 0);
      // Order with the best score in firt
      properties.sort((a, b) => b.relevanceScore - a.relevanceScore);
      // And remove score props
      properties = properties.map(p => p.property);
    }

    const { selectedInscriptionType, searchType } = this.search;
    const isResidential = searchType === "residential";
    const isCommercial = searchType === "commercial";

    // Filter by sale/rental
    properties =
      selectedInscriptionType === 1
        ? properties.filter((p) => p.price_sale)
        : selectedInscriptionType === 2
        ? properties.filter((p) => p.price_rental)
        : properties;

    // Other filters
    if (filters) {
      // Skip if null or empty
      for (const filter of Object.keys(filters)) {
        const f = filters[filter];
        if (!f || (Array.isArray(f) && !f.length)) {
          delete filters[filter];
        }
      }

      for (const filter of Object.keys(filters)) {
        if (filter === "types") {
          // Transform types into an array ["CB", "RE", ...], excluding false values
          filters.types = Object.keys(filters.types)
            .filter((t) => filters.types[t]) // && validTypes.includes(t))
            .map((t) => t);

          // TODO: Filter propertyTypes to match only the current search category
          if (filters.types.length) {
            properties = properties.filter((p) =>
              filters.types.includes(p.property_type_key)
            );
          } else {
            delete filters.type;
          }
        }

        if (filter === "priceMin") {
          properties = properties.filter(
            (p) => p.price_sale >= filters.priceMin
          );
        }
        if (filter === "priceMax") {
          properties = properties.filter(
            (p) => p.price_sale <= filters.priceMax
          );
        }

        if (filter === "rentalPriceMin") {
          properties = properties.filter(
            (p) => p.price_rental >= filters.rentalPriceMin
          );
        }
        if (filter === "rentalPriceMax") {
          properties = properties.filter(
            (p) => p.price_rental <= filters.rentalPriceMax
          );
        }

        if (!isCommercial && filter === "rooms_bathroom_number") {
          properties = properties.filter(
            (p) => p.rooms_bathroom_number >= filters.rooms_bathroom_number
          );
        }
        if (!isCommercial && filter === "rooms_bedroom_total_number") {
          properties = properties.filter(
            (p) =>
              p.rooms_bedroom_total_number >= filters.rooms_bedroom_total_number
          );
        }
        if (!isCommercial && filter === "openhouses") {
          properties = properties.filter(
            (p) => p.openhouses && p.openhouses.length
          );
        }
      }
      // this.scrollToResult();
    }

    this.properties = properties;
    this.mapPaneComponent.updateProperties(this.properties);
    this.currentPage = 1;
    this.addToLocalStorage();
  }

  updateMapSize(size) {
    this.panelStatus = size;
    setTimeout(() => this.mapPaneComponent.updateMap(), 400);
  }

  toggleMap(show) {
    this.showMapOnly = show;
    this.scrollToResult();
  }

  onPageChange(page) {
    this.currentPage = page;
    this.scrollToResult();
  }

  resetFilters() {
    this.properties = this.allProperties;
    this.currentPage = 1;
    this.search.filters = {};
  }

  scrollToResult() {
    setTimeout(() => {
      const yOffset = -83;
      const y =
        document.getElementById("properties-list").getBoundingClientRect().top +
        window.pageYOffset +
        yOffset;
      window.scrollTo({ top: y, behavior: "smooth" });
    }, 100);
  }

  getMaxPrice() {
    this.inscriptionsService.getMaxPrice().subscribe((data) => {
      this.filtersComponent.initPriceSlider(data.data);
    });
  }

  getMaxRental() {
    this.inscriptionsService.getMaxPriceRental().subscribe(({ data = 50000 }) => {
      this.filtersComponent.initRentalPriceSlider(data);
    });
  }

  getMaxSurface() {
    // this.inscriptionsService.getMaxPrice().subscribe(data => {
    // this.filtersComponent.initSurfaceSlider(data);
    // });
  }

  addToLocalStorage() {
    const navigation = Object.keys(this.properties)
      .map((key) => String(this.properties[key].mls))
      .reverse();
    this.session.store("navigationproperties", navigation);
  }
}
