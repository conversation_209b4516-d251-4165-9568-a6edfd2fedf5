import { Component, OnInit, Input, EventEmitter, Output, SimpleChanges } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'lib-properties-pane',
  templateUrl: './properties-pane.component.html'
})

export class PropertiesPaneComponent implements OnInit {
  @Input() loading;
  @Input() search;
  @Input() properties;
  @Input() currentPage;
  @Input() panelStatus;
  @Input() showMapOnly;

  @Output() sizeChange: EventEmitter<any> = new EventEmitter<any>();
  @Output() pageChange: EventEmitter<any> = new EventEmitter<any>();

  sortableItems: any = []
  titleStr = '';
  order;

  constructor (
    private translate: TranslateService
  ) {
    if (this.translate.currentLang === 'fr') {
      this.sortableItems = [
        { value: '-date_origin_created', name: 'Plus récentes' },
        { value: 'date_origin_created', name: 'Plus anciennes' },
        { value: '-price_sale', name: 'Plus chères' },
        { value: 'price_sale', name: 'Moins chères' }
      ];
    } else {
      this.sortableItems = [
        { value: '-date_origin_created', name: 'Newest' },
        { value: 'date_origin_created', name: 'Oldest' },
        { value: '-price_sale', name: 'Higher priced' },
        { value: 'price_sale', name: 'Lower priced' }
      ];
    }

    // Set default order value to "Most recent"
    this.order = this.sortableItems[0].value;
  }

  ngOnInit () {
  }

  ngOnChanges (changes: SimpleChanges) {
    if (changes.properties && this.properties) {
      this.titleStr = '';
      if (this.properties.length) {
        const nb = this.properties.length;
        const found = this.translate.instant('library.search-full.sortable.' + (nb > 1 ? 'properties-found' : 'property-found'));
        this.titleStr = `${nb} ${found}`;
      }
    }
  }

  onPageChange (number: number) {
    this.pageChange.emit(number);
  }

  togglePane (side = 'left') {
    if (side === 'left') {
      this.panelStatus = this.panelStatus === '-grid' ? 'open' : null;
    } else if (side === 'right') {
      this.panelStatus = !this.panelStatus ? 'open' : '-grid';
    }
    this.sizeChange.emit(this.panelStatus);
  }
}
