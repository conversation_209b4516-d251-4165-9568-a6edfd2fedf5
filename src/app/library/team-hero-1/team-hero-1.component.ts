import { Component, OnInit } from '@angular/core';
import { BlocksService } from '@/services/v3/contentblocks/contentblocks.service';

@Component({
  selector: 'lib-team-hero-1',
  templateUrl: './team-hero-1.component.html'
})

export class TeamHero1Component implements OnInit {
  public blockTitle: string;
  public blockContent: string;

  constructor (private blocksService: BlocksService) {
    this.blocksService.getBlock('bloc-equipe').subscribe(data => {
      this.blockTitle = data.title;
      this.blockContent = data.text;
    });
  }

  ngOnInit () {
  }

  onReadMore ($event) {
    $event.preventDefault();
    $event.currentTarget.parentNode.classList.add('-opened');
  }
}
