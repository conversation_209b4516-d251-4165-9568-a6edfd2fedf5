<div class="property-details-cpn">
	<h3 class="-emphase-title">{{ "library.property-details.title" | translate }}</h3>
	
	<div class="table-ctn-dual">
		<div class="details-ctn table-ctn -dual -between">
			<div class="detail table-row">
				<p>{{ "library.property-details.type" | translate }}</p>
				<p *ngIf=property.building_type><span>{{ property.building_type }}</span></p>
				<p *ngIf=!property.building_type><span>--</span></p>
			</div>
			<div class="detail table-row">
				<p>{{ "library.property-details.living-area" | translate }}</p>
				<p *ngIf=property.building_dimensions_living_area><span>{{ property.building_dimensions_living_area | numberFormat }} {{ property.building_dimensions_living_area_units }}</span></p>
				<p *ngIf=!property.building_dimensions_living_area><span>N/D</span></p>
			</div>
			<div class="detail table-row">
				<p>{{ "library.property-details.dimensions-land" | translate }}</p>
				<p *ngIf="property.plot_dimensions_front > 0 && property.plot_dimensions_depth > 0 "><span>{{property.plot_dimensions_front | numberFormat}}x{{property.plot_dimensions_depth | numberFormat}} {{property.plot_dimensions_units}} <span *ngIf="property.plot_dimensions_irr">irr.</span></span></p>
				<p *ngIf="!property.plot_dimensions_front || !property.plot_dimensions_depth"><span>--</span></p>
			</div>
			<div class="detail table-row">
				<p>{{ "library.property-details.surface-land" | translate }}</p>
				<p *ngIf="property.plot_dimensions_area > 0 "><span>{{ property.plot_dimensions_area | numberFormat }} {{ property.plot_dimensions_area_units }}</span></p>
				<p *ngIf="!property.plot_dimensions_area"><span>--</span></p>
			</div>
		</div>

		<div class="details-ctn table-ctn -dual -between">
			<div class="detail table-row">
				<p>{{ "library.property-details.building-area" | translate }}</p>
				<p *ngIf="property.building_dimensions_front > 0 && property.building_dimensions_depth > 0 "><span>{{property.building_dimensions_front | numberFormat}}x{{property.building_dimensions_depth | numberFormat}} {{property.building_size_unit}} <span *ngIf="property.building_dimensions_irr">irr.</span></span></p>
				<p *ngIf="!property.building_dimensions_front || !property.building_dimensions_depth"><span>--</span></p>
			</div>
			<div class="detail table-row">
				<p>{{ "library.property-details.zonage" | translate }}</p>
				<p *ngIf="property.property_category"><span>{{property.property_category}}</span></p>
				<p *ngIf="!property.property_category"><span>--</span></p>
			</div>
			<div class="detail table-row">
				<p>{{ "library.property-details.construct-year" | translate }}</p>
				<p *ngIf="property.building_construction_year"><span>{{ property.building_construction_year }}</span></p>
				<p *ngIf="!property.building_construction_year"><span>--</span></p>
			</div>
			<div class="detail table-row">
				<p>{{ "library.property-details.financial-recovery" | translate }}</p>
				<p>
					<span *ngIf="property.foreclosure">{{ "library.property-details.label-yes" | translate }}</span>
					<span *ngIf="!property.foreclosure">{{ "library.property-details.label-no" | translate }}</span>
				</p>
			</div>
			<!--<div class="detail table-row">
				<p>{{ "library.property-details.location-certificate" | translate }}</p>
				<p>
					<span *ngIf="property.certificate_of_location">{{ "library.property-details.label-yes" | translate }}</span>
					<span *ngIf="!property.certificate_of_location">{{ "library.property-details.label-no" | translate }}</span>
				</p>
			</div>-->
		</div>
	</div>

</div>
