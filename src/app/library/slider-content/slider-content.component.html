<div class="slider-content-cpn">
    <div class="container">
        <h2 class="title">{{ 'library.slider-content.title' | translate }}</h2>
        <div class="swipers-ctn grid">
            <div class="swiper swiper-content-img col-12 col-t-lg-8">
                <div class="swiper-container">
                    <div class="swiper-wrapper">
                        <!-- Slides -->
                        <div class="swiper-slide">
                            <img src="assets/images/placeholder/slide-01.png">
                        </div>
                        <div class="swiper-slide">
                            <img src="assets/images/placeholder/slide-02.png">
                        </div>
                        <div class="swiper-slide">
                            <img src="assets/images/placeholder/slide-03.png">
                        </div>
                    </div>
                </div>
            </div>
            <div class="text col-12 col-t-lg-6">
                <div class="">
                    <div class="swiper swiper-content">
                        <div class="swiper-container">
                            <div class="swiper-wrapper">
                                <!-- Slides -->
                                <div class="swiper-slide" *ngFor="let slide of slidesContent; let i = index">
                                    <div class="head">
                                        <p class="title">{{slide.title}}</p>
                                        <div class="step">
                                            <p>{{i + 1}} / {{slidesContent.length}}</p>
                                        </div>
                                    </div>
                                    <p class="content">
                                        {{slide.text}}
                                    </p>
                                </div>
                            </div>
                            <div class="swiper-nav">
                                <div class="swiper-button-prev swiper-btn"></div>
                                <div class="swiper-button-next swiper-btn"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>