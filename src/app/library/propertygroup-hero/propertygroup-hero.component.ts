import { Component, OnInit, Input } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'lib-propertygroup-hero',
  templateUrl: './propertygroup-hero.component.html'
})

export class PropertyGroupHeroComponent implements OnInit {
  public currentLang: any;
  public loading: boolean = true;

  @Input() propertygroup;

  constructor (
    public translateService: TranslateService
  ) {}

  ngOnInit () {
    this.currentLang = this.translateService.currentLang;
  }

  ngAfterViewInit () {
    setTimeout(() => { this.loading = false; }, 2200);
  }

  onOpenFullScreen () {
    document.querySelectorAll('.full-screen-cpn')[0].classList.add('-opened');
    document.documentElement.classList.toggle('-no-scroll');

    // fix IE Swiper offset
    // Doesn't change anything on Chrome (Only launch resize event once which recalculate the slides)
    setTimeout(() => { window.dispatchEvent(new Event('resize')); }, 750);
  }

  onCloseFullScreen () {
    document.querySelectorAll('.full-screen-cpn')[0].classList.remove('-opened');
    document.documentElement.classList.toggle('-no-scroll');
  }
}
