import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { faCoffee } from '@fortawesome/free-solid-svg-icons';

@Component({
  selector: 'lib-property-share',
  templateUrl: './property-share.component.html'
})

export class PropertyShareComponent implements OnInit {
  faCoffee = faCoffee;
  public currentUrl = '';
  constructor (
    private _router: Router
  ) {
    this.currentUrl = window.location.href;
  }

  ngOnInit () {
  }

  onPrint () {
    window.print();
  }
}
