<div id="header-panel">
	<div class="panel-backdrop"></div>

	<div class="panel-cpn">
		<a href="#" class="close-button" (click)="onClosePanel($event)"><span></span> <span></span> <span></span></a>
		
    <ul class="panel-menu -no-list-style">
			<li class="main-item"><a [routerLink]="['urls.home' | translate ]">{{ 'library.header.home' | translate }}</a></li>

			<li class="main-item -dropdown" routerLinkActive="active-child" [routerLinkActiveOptions]="{exact: true}">
				<input type="checkbox" />
				<span data-toggle="dropdown">{{ 'library.panel.menu.properties' | translate }}</span>
				<ul class="dropdown-menu">
					<li><a [routerLink]="['urls.search-properties' | translate ]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ 'library.panel.menu.search-properties' | translate }}</a></li>
					<li><a [routerLink]="['urls.property-group' | translate ]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ 'library.panel.menu.property-groups' | translate }}</a></li>
					<li><a [routerLink]="['urls.neighborhoods' | translate ]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ 'library.panel.menu.neighborhoods' | translate }}</a></li>
				</ul>
			</li>

			<li class="main-item -dropdown" routerLinkActive="active-child" [routerLinkActiveOptions]="{exact: true}">
				<input type="checkbox" />
				<span data-toggle="dropdown">{{ 'library.panel.menu.buy' | translate }}</span>
				<ul class="dropdown-menu">
					<li><a [routerLink]="['urls.real-estate-alert' | translate ]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ 'library.panel.menu.real-estate-alert' | translate }}</a></li>
					<li><a [routerLink]="['urls.buy-house' | translate ]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ 'library.panel.menu.buying-tips' | translate }}</a></li>
					<li><a [routerLink]="['urls.open-house' | translate ]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ 'library.panel.menu.open-house' | translate }}</a></li>
				</ul>
			</li>

			<li class="main-item -dropdown" routerLinkActive="active-child" [routerLinkActiveOptions]="{exact: true}">
				<input type="checkbox" />
				<span data-toggle="dropdown">{{ 'library.panel.menu.sell' | translate }}</span>
				<ul class="dropdown-menu">
					<li><a [routerLink]="['urls.real-estate-online-evaluation' | translate ]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ 'library.panel.menu.evaluate-online' | translate }}</a></li>
					<li><a [routerLink]="['urls.sell-house' | translate ]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ 'library.panel.menu.selling-tips' | translate }}</a></li>
					<li><a [routerLink]="['urls.home-staging' | translate ]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ 'library.panel.menu.home-staging' | translate }}</a></li>
				</ul>
			</li>

			<li class="main-item -dropdown" routerLinkActive="active-child" [routerLinkActiveOptions]="{exact: true}">
				<input type="checkbox" />
				<span data-toggle="dropdown">{{ 'library.panel.menu.team' | translate }}</span>
				<ul class="dropdown-menu">
					<li><a [routerLink]="['urls.real-estate-agents' | translate]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ 'library.panel.menu.our-team' | translate }}</a></li>
					<li><a [routerLink]="['urls.real-estate-agents-2' | translate]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ "library.header.our-team" | translate }} (2)</a></li>
					<li><a [routerLink]="['urls.real-estate-agents-3' | translate]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ "library.header.our-team" | translate }} (3)</a></li>
					<li><a [routerLink]="['urls.real-estate-agents-4' | translate]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ "library.header.our-team" | translate }} (4)</a></li>
					<li><a [routerLink]="['urls.real-estate-agents-5' | translate]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ "library.header.our-team" | translate }} (5)</a></li>
					<li><a [routerLink]="['urls.real-estate-agents-6' | translate]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ "library.header.our-team" | translate }} (6)</a></li>
					<li><a [routerLink]="['urls.specialists' | translate]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ "library.header.specialists" | translate }}</a></li>
					<li><a [routerLink]="['urls.testimonials' | translate]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ "library.header.testimonials" | translate }}</a></li>
					<li><a [routerLink]="['urls.career' | translate]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ "library.header.career" | translate }}</a></li>
				</ul>
			</li>
      
			<li class="main-item "><a [routerLink]="['urls.real-estate-blog' | translate ]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ 'library.panel.menu.blog' | translate }}</a></li>
			<li class="main-item"><a [routerLink]="['urls.contact' | translate ]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ "library.header.contact" | translate }}</a></li>
		</ul>

		<a class="switch" (click)="switchLang(translate.currentLang == 'fr' ? 'en' : 'fr')">{{ "global.switchlang" | translate }}</a>
		
		<div class="share-ctn">
			<ul class="-no-list-style">
				<li><a href="{{ 'client.facebook' | translate }}" class="icon-logo-facebook" target="_blank"></a></li>
				<li><a href="{{ 'client.twitter' | translate }}" class="icon-x" target="_blank"></a></li>
				<li><a href="{{ 'client.instagram' | translate }}" class="icon-logo-instagram" target="_blank"></a></li>
				<li><a href="{{ 'client.linkedin' | translate }}" class="icon-logo-linkedin" target="_blank"></a></li>
				<li><a href="{{ 'client.youtube' | translate }}" class="icon-logo-youtube" target="_blank"></a></li>
				<li><a href="mailto:{{ 'client.email' | translate }}" class="icon-logo-mail"></a></li>
			</ul>
		</div>
	</div>
</div>