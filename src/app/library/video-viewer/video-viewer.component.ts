import { Component, OnInit, Input } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
@Component({
  selector: 'lib-video-viewer',
  templateUrl: './video-viewer.component.html'
})
export class VideoViewerComponent implements OnInit {

	@Input() customContent; 

	cleanVideo:any

	constructor(public translate: TranslateService, private sanitizer: DomSanitizer) {
		
	}

	ngOnInit() {
		this.cleanVideo	= this.sanitizer.bypassSecurityTrustResourceUrl(this.customContent.section_video_formatted) 
	}

}
