<div class="broker-contact-header-1-cpn -space-lg">
	<div class="container grid">

		<div class="contact-form-ctn no-padding col-12 col-t-lg-7">

			<h1 class="-no-top-space">{{ blockTitle }}</h1>
			<div class="-page-description" [innerHtml]="blockContent"></div>
			<p class="-page-required">{{ "library.broker-contact-header-1.required" | translate }}</p>

			<lib-broker-contact-form [narrow]="true"></lib-broker-contact-form>
		</div>

		<div class="info-box-ctn col-12 col-t-lg-5">
			<div class="info-box" itemscope itemtype="http://schema.org/Organization">
				<img itemprop="image" src="/assets/images/placeholder/equipe-contact.jpg" alt="{{ 'library.broker-contact-header-1.title' | translate }} {{ 'library.broker-contact-header-1.alt-image' | translate }}">
				<div class="info-text-ctn">
					<h2 itemprop="name" class="title">{{ 'library.broker-contact-header-1.title' | translate }}</h2>
					<div class="location-ctn block">
						<i class="icon-pin"></i>
						<div itemprop="address" itemscope itemtype="http://schema.org/PostalAddress" class="text">
							<p itemprop="name">{{ 'client.name' | translate }}</p>
							<p itemprop="streetAddress">{{ 'client.address' | translate }}</p>
							<p itemprop="addressLocality">{{ 'client.city' | translate }}</p>
							<div class="links">
								<a itemprop="location" href="" (click)="showMap($event)">{{ 'library.broker-contact-header-1.see-maps' | translate }}</a>
								<a itemprop="url" href="{{ 'client.map.url' | translate }}" target="_blank">{{ 'library.broker-contact-header-1.see-direction' | translate }}</a>
							</div>
						</div>
					</div>
					<div class="email-ctn block">
						<i class="icon-mail"></i>
						<div class="text">
							 <a itemprop="email" href="mailto:{{ 'client.email' | translate }}">{{ 'client.email' | translate }}</a>
						</div>
					</div>
					<div class="phone-ctn block">
						<i class="icon-phone"></i>
						<div class="text">
							<a itemprop="telephone" href="tel:{{ 'client.phone' | translate | phone }}">{{ 'client.phone' | translate | phone }}</a>
						</div>
					</div>
				</div>
			</div>
		</div>
    
	</div>
</div>
