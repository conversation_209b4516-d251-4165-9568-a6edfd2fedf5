import { Component, OnInit } from '@angular/core';
import { BlocksService } from '@/services/v3/contentblocks/contentblocks.service';

@Component({
  selector: 'lib-broker-contact-header-1',
  templateUrl: './broker-contact-header-1.component.html'
})

export class BrokerContactHeader1Component implements OnInit {
  public blockTitle: string;
  public blockContent: string;

  constructor (
    private blocksService: BlocksService
  ) {
    this.blocksService.getBlock('bloc-contact').subscribe(({ title, text }) => {
      this.blockTitle = title;
      this.blockContent = text;
    });
  }

  ngOnInit () {}

  showMap (event) {
    event.preventDefault();
    setTimeout(() => document.getElementById('map').scrollIntoView({ behavior: 'smooth' }), 100);
  }
}
