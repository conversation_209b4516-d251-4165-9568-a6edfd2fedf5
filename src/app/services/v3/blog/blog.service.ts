import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { UtilsService } from '@/services/v3/utils/utils.service';

@Injectable()
export class BlogService {
  constructor (
    private utils: UtilsService
  ) { }

  /*
    Get a list of categories
  */
  public getCategories (): Observable<any> {
    const endpoint = 'blog/categories';
    return this.utils.apiGet(endpoint);
  }

  /*
    Get a list of posts
  */
  public getPosts (limit, category = undefined): Observable<any> {
    // const endpoint = 'blog/posts?' +  // Deprecated v1 blog posts
    const endpoint = 'blog/posts-v2?' +
      (limit ? 'limit=' + limit + '&' : '') +
      (category ? 'category=' + category : '');
    return this.utils.apiGet(endpoint);
  }

  /*
    Get a single post by slug
  */
  public getPost (slug, preview = undefined): Observable<any> {
    // const endpoint = 'blog/posts/' + slug;  // Deprecated v1 blog posts
    const endpoint = 'blog/posts-v2/' +
      slug +
      (preview ? '?preview=' + preview : '');
    return this.utils.apiGet(endpoint);
  }
}
