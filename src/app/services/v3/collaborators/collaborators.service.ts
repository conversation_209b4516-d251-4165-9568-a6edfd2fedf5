import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { UtilsService } from '@/services/v3/utils/utils.service';

@Injectable()
export class CollaboratorsService {
  constructor (
    private utils: UtilsService
  ) { }

  /*
    Get a list of categories
  */
  public getCategories (): Observable<any> {
    const endpoint = 'collaborators/categories';
    return this.utils.apiGet(endpoint);
  }

  /*
    Get a list of specialists
  */
  public getCollaborators (): Observable<any> {
    const endpoint = 'collaborators';
    return this.utils.apiGet(endpoint);
  }
}
