import { Injectable } from '@angular/core';
import { HttpEvent, HttpInterceptor, HttpHandler, HttpRequest } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '@/../environments/environment';

@Injectable()
export class VarnishInterceptor implements HttpInterceptor {
  intercept (req: HttpRequest<any>, next: <PERSON>ttpHandler): Observable<HttpEvent<any>> {
    let targetUrl = req.url;
    if (targetUrl.match(/\?/)) {
      targetUrl += '&referer=' + environment.siteDomain;
    } else {
      targetUrl += '?referer=' + environment.siteDomain;
    }

    const varnishReq = req.clone({ url: targetUrl });
    return next.handle(varnishReq);
  }
}
