import { Injectable } from '@angular/core';
import { Map, NavigationControl } from 'mapbox-gl';
import { environment } from '@/../environments/environment';

@Injectable({ providedIn: 'root' })

export class MapService {
  // Centered on Montreal by default
  mapOrigin = { lat: 45.537264, lng: -73.614031, zoom: 10.5 };

  constructor () { }

  // Create a Mapbox object with basic settings
  public createMap ({ lat, lng, zoom } = this.mapOrigin, id = 'map') {
    const map = new Map({
      accessToken: environment.mapBoxToken,
      container: document.getElementById(id),
      style: 'mapbox://styles/eclosion/cjfybdpo8022s2rnyzehovfkl',
      zoom,
      center: { lat, lng }
    });

    // Disable interactions
    map.scrollZoom.disable();
    map.dragRotate.disable();
    map.touchZoomRotate.disableRotation();

    // Add controls
    map.addControl(new NavigationControl());

    return map;
  }

  // Build an flattened array of coordinates from features
  private getAllFeaturesCoords (features) {
    // Extract coordinates from each feature
    return features.map(f => f.geometry.coordinates)
      // Flatten all multipolygons
      .flat(10)
      // Create coords array [...x, ...y]
      .reduce((final, current, index) => {
        final[index % 2].push(current);
        return final;
      }, [[], []]);
  }

  // Get a coordinates set that includes all features
  public getBoundaries (features) {
    const [x, y] = this.getAllFeaturesCoords(features);
    return [[Math.min(...x), Math.min(...y)], [Math.max(...x), Math.max(...y)]];
  }
}
