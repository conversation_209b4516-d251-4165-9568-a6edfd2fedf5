import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { UtilsService } from '@/services/v3/utils/utils.service';

@Injectable()
export class InscriptionsService {
  constructor (
    private utils: UtilsService
  ) { }

  /*
    Gets list of inscriptions from the API. Can be filtered with different parameters.
  */
  public getInscriptions (limit, searchObj): Observable<any> {
    const { city, rooms, type, flags, cover, featured, sold, neighborhood, mls, pricemin, pricemax, searchType, sort } = searchObj;
    const { openhouse, revenues, prestige } = flags || {};

    // Sanitize and format search object values
    const paramsObj: any = {
      limit: limit && limit !== -1 ? limit : null,
      price: (pricemin || '') + ',' + (pricemax || ''),
      sort: sort ? sort : null,
      mls: mls?.length ? mls.toString() : null,
      type: type?.length > 0 ? type.toString() : null,
      cover: cover ? '1' : null,
      featured: featured ? '1' : null,
      bedrooms: rooms,
      openhouse: !!openhouse || null,
      revenues: +!!revenues || null,
      collection: !!prestige || null,
      'inscription-groups': (flags && !!flags['inscription-groups']) || null,
      // carac: [openhouse, revenues, prestige, 'inscription-groups']  // Is this still used?
      city,
      sold,
      neighborhood,
      searchType
    };

    // Build endpoint string from search params
    const invalidValues = [null, undefined, '', 'null,null', ','];
    const params = Object.keys(paramsObj)
      .filter(p => !invalidValues.includes(paramsObj[p]))
      .map(p => p + '=' + paramsObj[p])
      .join('&');

    // Execute request
    const endpoint = 'inscriptions' + (params.length ? '?' : '') + params;
    // console.log(endpoint);
    return this.utils.apiGet(endpoint);
  }

  /*
    Get a collection of all addresses with inscriptions
  */
  public getMunicipalitiesWithInscriptions (): Observable<any> {
    const endpoint = 'municipalities?with=inscriptions';
    return this.utils.apiGet(endpoint,
      response => {
        const data = response.data.map(municipality => {
          const { description: label, code: value, inscriptions } = municipality;
          // eslint-disable-next-line array-callback-return
          const mInscriptions = inscriptions.map(i => {
            const { ext_address: label, mls: value, rentalPrice } = i;
            // Tag out properties for rent
            if (!rentalPrice) return { label, value };
          });
          return [{ label, value }, ...mInscriptions];
        })
          .flat()
          // Remove undefined properties ( it's rental properties )
          .filter(item => item)
          .sort((a, b) => a.label >= b.label);

        return { ...response, data };
      });
  }

  /*
    Get a collection of all addresses with inscriptions
  */
  public getAllMls (): Observable<any> {
    const endpoint = 'inscriptions/mls/all';
    return this.utils.apiGet(endpoint);
  }

  public getInscriptionSold (): Observable<any> {
    const endpoint = 'inscriptionssold';
    return this.utils.apiGet(endpoint);
  }

  public getMunicipalities (): Observable<any> {
    const endpoint = 'municipalities/';
    return this.utils.apiGet(endpoint);
  }

  public getMaxPrice (): Observable<any> {
    const endpoint = 'inscriptions/maxprice';
    return this.utils.apiGet(endpoint);
  }

  public getMaxPriceRental (): Observable<any> {
    const endpoint = 'inscriptions/maxpricerental';
    return this.utils.apiGet(endpoint);
  }

  public getTypes (all = false): Observable<any> {
    const endpoint = 'propertiestypes' + (all ? '?all=1' : '');
    return this.utils.apiGet(endpoint);
  }

  /*
    Gets single inscription. The similar parameter appends a numer of similar inscriptions equal to the parameter value
  */
  public getInscription (mls, similar = null): Observable<any> {
    const endpoint = 'inscriptions/' + mls + (similar ? '&similar=' + similar : '');
    return this.utils.apiGet(endpoint);
  }

  public getRooms (mls): Observable<any> {
    const endpoint = 'inscriptions/' + mls + '/rooms';
    return this.utils.apiGet(endpoint);
  }

  public getCharacteristics (mls): Observable<any> {
    const endpoint = 'inscriptions/' + mls + '/characteristics';
    return this.utils.apiGet(endpoint);
  }

  public getOpenHouses (mls): Observable<any> {
    const endpoint = 'inscriptions/' + mls + '/openhouses';
    return this.utils.apiGet(endpoint);
  }

  public getBrokers (mls): Observable<any> {
    const endpoint = 'inscriptions/' + mls + '/brokers';
    return this.utils.apiGet(endpoint);
  }

  public getExpenses (mls, type = null): Observable<any> {
    const endpoint = 'inscriptions/' + mls + '/expenses' + (type ? '?type=' + type : '');
    return this.utils.apiGet(endpoint);
  }

  public getPhotos (mls): Observable<any> {
    const endpoint = 'inscriptions/' + mls + '/photos';
    return this.utils.apiGet(endpoint);
  }

  public getNotes (mls): Observable<any> {
    const endpoint = 'inscriptions/' + mls + '/notes';
    return this.utils.apiGet(endpoint);
  }

  /*
    Get pagination from Centris
  */
  public getPaginationByMls (mls): Observable<any> {
    const endpoint = 'inscriptions/' + mls + '/pagination';
    return this.utils.apiGet(endpoint);
  }

  /*
    Get pagination from Centris
  */
  public getDocuments (mls): Observable<any> {
    const endpoint = 'inscriptions/' + mls + '/documents';
    return this.utils.apiGet(endpoint);
  }

  /*
    Post visit on page view
  */
  // public setVisit (mls): Observable<any> {
  //   const endpoint = 'statistics/inscriptions/' + mls + '/view', false);
  //   const options = { headers: new HttpHeaders({ 'Content-Type': 'application/json' }) };

  //   const currentLang = window.location.pathname.split('/')[1];
  //   const body: Object = JSON.stringify({ locale: currentLang });

  //   const request: any = this.http.post(endpoint, body, options) as any;
  //   return request.pipe(
  //     catchError(this.utils.apiHError)
  //   );
  // }

  /*
    Post visit on contact
  */
  // public setContactVisit (mls): Observable<any> {
  //   const endpoint = 'statistics/inscriptions/' + mls + '/contact', false);
  //   const options = { headers: new HttpHeaders({ 'Content-Type': 'application/json' }) };
  //   const currentLang = window.location.pathname.split('/')[1];
  //   const body: Object = JSON.stringify({ locale: currentLang });

  //   const request: any = this.http.post(endpoint, body, options) as any;
  //   return request.pipe(
  //     catchError(this.utils.apiHError)
  //   );
  // }

  /*
    Get price change information for an inscription
  */
  public getPriceChanged (mls): Observable<any> {
    const endpoint = 'inscriptions/' + mls + '/price-changed';
    return this.utils.apiGet(endpoint);
  }

  /*
    Get Open Graph OG image URL for a given MLS (for Facebook sharing)
    @param mls - MLS number of the inscription
    @param options - Optional parameters: { site_id?: number, type?: string }
    @returns Observable<any> with og_image_url and related info
    type must be one of: 'default', 'new', 'open_house', 'price_change', 'sold'
  */
  public getOgImage(
    mls: string,
    options?: { site_id?: number; type?: string }
  ): Observable<any> {
    const params: any = {};
    if (options?.site_id !== undefined) params.site_id = options.site_id;
    if (options?.type !== undefined) params.type = options.type;

    // Build endpoint string from params
    const invalidValues = [null, undefined, '', 'null,null', ','];
    const queryParams = Object.keys(params)
      .filter(p => !invalidValues.includes(params[p]))
      .map(p => p + '=' + params[p])
      .join('&');

    const endpoint = `inscriptions/${mls}/og-image` + (queryParams.length ? '?' : '') + queryParams;
    return this.utils.apiGet(endpoint);
  }
}
