import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { UtilsService } from '@/services/v3/utils/utils.service';

@Injectable()
export class LandingService {
  constructor (
    private utils: UtilsService
  ) { }

  /*
    Get a campaing data by slug
  */
  public getLanding (slug, preview = null): Observable<any> {
    const endpoint = 'landing-page/' + slug + (preview ? '?preview=' + preview : '');
    return this.utils.apiGet(endpoint);
  }
}
