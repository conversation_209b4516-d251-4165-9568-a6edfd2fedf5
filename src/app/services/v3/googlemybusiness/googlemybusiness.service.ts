import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { UtilsService } from '@/services/v3/utils/utils.service';

@Injectable()
export class GooglemybusinessService {
  constructor (
    private utils: UtilsService
  ) { }

  /*
    Get a list of testimony
  */
  public getGoogleMyBusiness (): Observable<any> {
    const endpoint = 'googlemybusiness';
    return this.utils.apiGet(endpoint);
  }
}
