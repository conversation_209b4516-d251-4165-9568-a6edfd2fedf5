import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { UtilsService } from '@/services/v3/utils/utils.service';

@Injectable()
export class BlocksService {
  constructor (
    private utils: UtilsService
  ) { }

  /*
    Get a single block by slug
  */
  public getBlock (slug): Observable<any> {
    const endpoint = 'editableblocks/' + slug;
    return this.utils.apiGet(endpoint, ({ data }) => data[0]);
  }
}
