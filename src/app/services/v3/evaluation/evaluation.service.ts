import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import { UtilsService } from '@/services/v3/utils/utils.service';

@Injectable()
export class EvaluationService {
  constructor (
    private utils: UtilsService,
    private translate: TranslateService
  ) { }

  /*
    POST Alert
  */
  public postEvaluation (dataForm): Observable<any> {
    const body: Object = JSON.stringify({
      email: dataForm.email,
      firstname: dataForm.name.firstName,
      lastname: dataForm.name.lastName,
      phone: dataForm.phone.phoneNumber,
      phone_ext: dataForm.phone.phoneExt,
      photos: dataForm.photos,
      language: this.translate.currentLang,
      address: dataForm.address,
      type: dataForm.type,
      year: dataForm.constructYear,
      bathroom: dataForm.bathrooms,
      bedroom: dataForm.bedrooms,
      garage: dataForm.garage,
      parking: dataForm.car_park,
      basement: dataForm.basement,
      swimming_pool: dataForm.swimming_pool,
      street_view_link: dataForm.street_view_link,
      reason: dataForm.reasonBuy,
      purpose: dataForm.purpose,
      professional_evaluation: dataForm.professional_evaluation,
      renovations: dataForm.renovations,
      delay_to_move: dataForm.moveDelay,
			delay_to_sold: dataForm.delayToSold,
      dimension: dataForm.dimensions,
      size: dataForm.surface,
      token_captcha: dataForm.token_captcha
    });

    const endpoint = 'contact/evaluation';
    return this.utils.apiPost(endpoint, body);
  }
}
