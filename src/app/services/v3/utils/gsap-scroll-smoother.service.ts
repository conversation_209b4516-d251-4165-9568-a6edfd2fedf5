import { Injectable } from '@angular/core';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { ScrollSmoother } from 'gsap/ScrollSmoother';

@Injectable({
  providedIn: 'root'
})
export class GsapScrollSmootherService {
  private scrollSmoother: any = null;
  private isInitialized = false;

  constructor() {
    gsap.registerPlugin(ScrollTrigger, ScrollSmoother);
  }

  /**
   * Initialize ScrollSmoother with default options
   */
  init(options?: any): void {
    if (this.isInitialized) {
      console.warn('ScrollSmoother is already initialized');
      return;
    }

    // Default options for ScrollSmoother
    const defaultOptions = {
      wrapper: '#smooth-wrapper',
      content: '#smooth-content',
      smooth: 1.2,
      effects: true,
      ...options
    };

    try {
      this.scrollSmoother = ScrollSmoother.create(defaultOptions);
      this.isInitialized = true;
      console.log('ScrollSmoother initialized successfully');
    } catch (error) {
      console.error('Error initializing ScrollSmoother:', error);
    }
  }

  /**
   * Destroys the ScrollSmoother instance
   */
  destroy(): void {
    if (this.scrollSmoother) {
      this.scrollSmoother.kill();
      this.scrollSmoother = null;
      this.isInitialized = false;
    }
  }

  /**
   * Scroll to an element or position
   */
  scrollTo(target: string | number | Element, smooth: boolean = true, position?: any): void {
    if (this.scrollSmoother) {
      this.scrollSmoother.scrollTo(target, smooth, position);
    }
  }

  /**
   * Refresh ScrollSmoother
   */
  refresh(): void {
    if (this.scrollSmoother) {
      this.scrollSmoother.refresh();
    }
  }

  /**
   * Pause/resume smooth scrolling
   */
  paused(value?: boolean): boolean | void {
    if (this.scrollSmoother) {
      if (value !== undefined) {
        this.scrollSmoother.paused(value);
      } else {
        return this.scrollSmoother.paused();
      }
    }
    return false;
  }

  /**
   * Gets the current scroll position
   */
  scrollTop(): number {
    return this.scrollSmoother ? this.scrollSmoother.scrollTop() : 0;
  }

  /**
   * Checks if ScrollSmoother is initialized
   */
  isReady(): boolean {
    return this.isInitialized && this.scrollSmoother !== null;
  }

  /**
   * Gets the ScrollSmoother instance for advanced manipulations
   */
  getInstance(): any {
    return this.scrollSmoother;
  }

  /**
   * Update the smooth scroll speed
   */
  updateSpeed(speed: number): void {
    if (this.scrollSmoother) {
      this.scrollSmoother.smooth(speed);
    }
  }

  /**
   * Enables/disables the parallax effects
   */
  toggleEffects(enabled: boolean): void {
    if (this.scrollSmoother) {
      this.scrollSmoother.effects(enabled);
    }
  }

  /**
   * Force un refresh immédiat de tous les ScrollTriggers
   */
  forceRefreshScrollTriggers(): void {
    console.log('Force refreshing all ScrollTriggers immediately');
    ScrollTrigger.refresh();
  }

  /**
   * Méthode spécifique pour les changements de route
   * Refresh les ScrollTriggers et recalcule les positions
   */
  onRouteChange(): void {
    // Marquer le contenu comme non prêt (sans loading)

    // Programmer un refresh après que le nouveau contenu soit rendu
    setTimeout(() => {
      this.forceRefreshScrollTriggers();
    }, 100);
  }
}
