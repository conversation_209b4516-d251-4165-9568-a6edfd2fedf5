import { Injectable, Renderer2, RendererFactory2 } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class GridHelperService {
  private renderer: Renderer2;
  private gridContainer: HTMLElement | null = null;
  private isActive = false;
  private ctrlDown = false;

  private readonly GRID_HELPER_MARGIN_CSS_VAR = '--grid-margin';
  private readonly GRID_HELPER_RGBA_COLOR = 'rgba(0, 60, 255, .3)';
  private readonly GRID_HELPER_COLUMNS_CSS_VAR = '--grid-columns';
  private readonly GRID_HELPER_UNIT_CSS_VAR = '--unit';

  constructor(private rendererFactory: RendererFactory2) {
    this.renderer = this.rendererFactory.createRenderer(null, null);
  }

  public initialize(): void {
    this.initGrid();
    this.addEventListeners();
  }
  
  private initGrid(): void {
    this.gridContainer = this.renderer.createElement('div');
    this.renderer.appendChild(document.body, this.gridContainer);

    this.setGridStyles();
    this.setGridColumns();
  }

  private setGridStyles(): void {
    if (!this.gridContainer) return;

    const styles = {
      zIndex: '10000',
      position: 'fixed',
      top: '0',
      left: '0',
      display: 'flex',
      width: '100%',
      height: '100%',
      columnGap: `var(${this.GRID_HELPER_UNIT_CSS_VAR})`,
      paddingLeft: `var(${this.GRID_HELPER_MARGIN_CSS_VAR})`,
      paddingRight: `var(${this.GRID_HELPER_MARGIN_CSS_VAR})`,
      pointerEvents: 'none',
      visibility: 'hidden',
    };

    for (const [key, value] of Object.entries(styles)) {
      this.renderer.setStyle(this.gridContainer, key, value);
    }
  }

  private setGridColumns(): void {
    if (!this.gridContainer) return;

    this.gridContainer.innerHTML = '';

    const columns = Number(
      getComputedStyle(document.documentElement).getPropertyValue(this.GRID_HELPER_COLUMNS_CSS_VAR) || 12
    );

    for (let i = 0; i < columns; i++) {
      const col = this.renderer.createElement('div');
      this.renderer.setStyle(col, 'flex', '1 1 0');
      this.renderer.setStyle(col, 'backgroundColor', this.GRID_HELPER_RGBA_COLOR);
      this.renderer.appendChild(this.gridContainer, col);
    }
  }

  private addEventListeners(): void {
    window.addEventListener('resize', () => {
      this.setGridColumns();
    });

    document.addEventListener('keydown', (e: KeyboardEvent) => {
      if (e.key === 'Control') {
        this.ctrlDown = true;
      } else if (this.ctrlDown && e.key === 'g') {
        this.toggleGridVisibility();
      }
    });

    document.addEventListener('keyup', (e: KeyboardEvent) => {
      if (e.key === 'Control') {
        this.ctrlDown = false;
      }
    });
  }

  private toggleGridVisibility(): void {
    if (!this.gridContainer) return;

    this.isActive = !this.isActive;
    const visibility = this.isActive ? 'visible' : 'hidden';
    this.renderer.setStyle(this.gridContainer, 'visibility', visibility);
  }
}
