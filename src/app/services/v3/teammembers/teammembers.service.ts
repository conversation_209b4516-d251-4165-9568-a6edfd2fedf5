import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { UtilsService } from '@/services/v3/utils/utils.service';

@Injectable()
export class TeamsMembersService {
  constructor (
    private utils: UtilsService
  ) { }

  /*
    Get a list of team members
  */
  public getTeams (): Observable<any> {
    const endpoint = 'teammembers';
    return this.utils.apiGet(endpoint);
  }
}
