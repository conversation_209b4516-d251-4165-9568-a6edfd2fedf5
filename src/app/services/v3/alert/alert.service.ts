import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

import { TranslateService } from '@ngx-translate/core';
import { UtilsService } from '@/services/v3/utils/utils.service';

@Injectable()
export class AlertService {
  constructor (
    private utils: UtilsService,
    private translate: TranslateService
  ) { }

  public postAlert (dataForm): Observable<any> {
    const body: Object = JSON.stringify({
      firstname: dataForm.name.firstName,
      lastname: dataForm.name.lastName,
      phone: dataForm.name.phone,
      phone_ext: dataForm.name.phoneExt,
      email_alert: dataForm.email.email,
      cities: dataForm.cities,
      property_types: dataForm.propertyTypes,
      language: this.translate.currentLang,
      budget: dataForm.budget,
      other: dataForm.other,
      token_captcha: dataForm.token_captcha,
      comments: 'N/A',
      city: 'N/A',
      country: 'N/A',
      state: 'N/A',
      postal_code: 'N/A',
      address: 'N/A',
      how_did_you_hear_choices: 'N/A',
      delay: 'N/A',
      situation: 'N/A'
    });

    const endpoint = 'contact/alert';
    return this.utils.apiPost(endpoint, body);
  }
}
