import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { UtilsService } from '@/services/v3/utils/utils.service';

@Injectable()
export class InscriptionGroupsService {
  constructor (
    private utils: UtilsService
  ) { }

  /*
    Get a list of property group
  */
  public getInscriptionGroups (limit = undefined, random = undefined): Observable<any> {
    const endpoint = 'inscription-groups' +
      (limit ? 'limit=' + limit + '&' : '') +
      (random ? 'random=' + random : '');

    return this.utils.apiGet(endpoint);
  }

  /*
    Get a single property group by id
  */
  public getInscriptionGroup (id): Observable<any> {
    const endpoint = 'inscription-groups/' + id;
    return this.utils.apiGet(endpoint);
  }
}
