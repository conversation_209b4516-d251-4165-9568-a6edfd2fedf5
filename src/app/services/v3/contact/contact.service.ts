import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import { UtilsService } from '@/services/v3/utils/utils.service';

@Injectable()
export class ContactService {
  constructor (
    private utils: UtilsService,
    private translate: TranslateService
  ) {}

  /*
    POST Contact (page contact)
  */
  public postContact (dataForm): Observable<any> {
    const body: Object = JSON.stringify({
      subject: dataForm.subject,
      firstname: dataForm.name.firstName,
      lastname: dataForm.name.lastName,
      city: 'N/A',
      country: 'N/A',
      state: 'N/A',
      postal_code: 'N/A',
      address: 'N/A',
      phone: dataForm.phone.phoneNumber,
      phone_ext: dataForm.phone.phoneExt,
      email_from: dataForm.email,
      comments: dataForm.message,
      language: this.translate.currentLang,
      how_did_you_hear_choices: 'N/A',
      token_captcha: dataForm.token_captcha
    });

    const endpoint = 'contact/general';
    return this.utils.apiPost(endpoint, body);
  }

  /*
    POST Contact (page property)
  */
  public postContactProperty (dataForm): Observable<any> {
    const body = JSON.stringify({
      subject: dataForm.subject,
      firstname: dataForm.firstName,
      lastname: dataForm.lastName,
      phone: dataForm.phone,
      email_from: dataForm.email,
      broker_id: parseInt(dataForm.code),
      bur_code: dataForm.burCode,
      comments: dataForm.message,
      appointment_datetime: dataForm.datetime,
      mls: parseInt(dataForm.mls),
      language: this.translate.currentLang,
      token_captcha: dataForm.token_captcha
    });

    const endpoint = 'contact/appointment';
    return this.utils.apiPost(endpoint, body);
  }
}
