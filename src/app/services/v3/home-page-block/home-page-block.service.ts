import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { UtilsService } from '@/services/v3/utils/utils.service';

@Injectable()
export class HomePageService {
  constructor (
    private utils: UtilsService
  ) { }

  /*
    Get a single block by slug
  */
  public getHomePage (): Observable<any> {
    const endpoint = 'home-page';
    return this.utils.apiGet(endpoint);
  }
}
