import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { UtilsService } from '@/services/v3/utils/utils.service';

@Injectable()
export class TestimonialsService {
  constructor (
    private utils: UtilsService
  ) { }

  /*
    Get a list of testimonials
  */
  public getTestimonials (limit = null, random = null): Observable<any> {
    // const endpoint = 'testimonials?' + // Deprecated v1 testimonials
    const endpoint = 'testimonials-v2?' +
      (limit ? 'limit=' + limit + '&' : '') +
      (random ? 'random=' + random : '');

    return this.utils.apiGet(endpoint);
  }
}
