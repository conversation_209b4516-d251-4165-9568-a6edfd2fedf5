import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { UtilsService } from '@/services/v3/utils/utils.service';

@Injectable()
export class HomestagingService {
  constructor (
    private utils: UtilsService
  ) { }

  /*
    Get a list of testimony
  */
  public getHomestaging (limit = null, random = null): Observable<any> {
    const endpoint = 'homestagings' +
      (limit ? 'limit=' + limit + '&' : '') +
      (random ? 'random=' + random : '');

    return this.utils.apiGet(endpoint);
  }
}
