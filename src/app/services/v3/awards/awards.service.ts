import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { UtilsService } from '@/services/v3/utils/utils.service';

@Injectable()
export class AwardsService {
  constructor (
    private utils: UtilsService
  ) { }

  /*
    Get a list of awards
  */
  public getAwards (): Observable<any> {
    const endpoint = 'awards';
    return this.utils.apiGet(endpoint);
  }

  /*
    Get a list of award categories
  */
  public getCategories (notempty): Observable<any> {
    const endpoint = 'awards/categories' + (notempty ? 'notempty=' + notempty : '');
    return this.utils.apiGet(endpoint);
  }
}
