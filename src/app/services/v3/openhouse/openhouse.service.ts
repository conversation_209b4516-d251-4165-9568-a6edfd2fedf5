import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

import { TranslateService } from '@ngx-translate/core';
import { UtilsService } from '@/services/v3/utils/utils.service';
import * as moment from 'moment';

@Injectable()
export class OpenhouseService {
  constructor (
    private utils: UtilsService,
    private translate: TranslateService
  ) { }

  /*
    Get all openhouse
  */
  public getOpenhouse (order: string = 'date'): Observable<any> {
    const endpoint = 'openhouses?order_by=' + order;
    return this.utils.apiGet(endpoint,
      ({ data }) => {
        for (const openhouse of data) {
          const visitDate = moment(String(openhouse.start_date));
          if (this.isToday(visitDate)) {
            this.translate.get('views.openhouse.today').subscribe(res => {
              openhouse.today = res;
            });
          }
        }
        return data;
      });
  }

  private isToday (dateToTest) {
    if (moment().startOf('day').diff(dateToTest, 'days') === 0) return true;
  }
}
