import { Directive, ElementRef, Input, OnInit, OnDestroy, Inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

/**
 * Configuration interface for scroll appear directive
 */
export interface ScrollAppearConfig {
  scrollClass?: string;
  scrollTrigger?: string;
  scrollStartPosition?: string;
  scrollEndPosition?: string;
  scrollTriggerEnd?: string;
  scrollRepeat?: boolean;
  scrollProgress?: boolean;
  scrollMarkers?: boolean;
}

@Directive({
  selector: '[data-scroll]',
  standalone: true
})
export class DataScrollDirective implements OnInit, OnDestroy {
  @Input() dataScroll: ScrollAppearConfig = {};
  @Input() scrollClass: string = '-show';
  @Input() scrollTrigger: string = '';
  @Input() scrollStartPosition: string = 'top 80%';
  @Input() scrollEndPosition: string = 'bottom top';
  @Input() scrollTriggerEnd: string = '';
  @Input() scrollRepeat: boolean = false;
  @Input() scrollProgress: boolean = false;
  @Input() scrollMarkers: boolean = false;

  private scrollTriggerInstance: ScrollTrigger | null = null;
  private isBrowser: boolean;

  constructor(
    private elementRef: ElementRef<HTMLElement>,
    @Inject(PLATFORM_ID) platformId: Object
  ) {
    this.isBrowser = isPlatformBrowser(platformId);
  }

  ngOnInit(): void {
    if (!this.isBrowser) {
      return;
    }

    // Merge configuration from input object and individual inputs
    const config = this.mergeConfiguration();
    
    // Create ScrollTrigger with a small delay to ensure DOM is ready
    setTimeout(() => {
      this.createScrollTrigger(config);
    }, 0);
  }

  ngOnDestroy(): void {
    this.destroyScrollTrigger();
  }

  /**
   * Merge configuration from object input and individual inputs
   */
  private mergeConfiguration(): Required<ScrollAppearConfig> {
    return {
      scrollClass: this.dataScroll.scrollClass ?? this.scrollClass,
      scrollTrigger: this.dataScroll.scrollTrigger ?? this.scrollTrigger,
      scrollStartPosition: this.dataScroll.scrollStartPosition ?? this.scrollStartPosition,
      scrollEndPosition: this.dataScroll.scrollEndPosition ?? this.scrollEndPosition,
      scrollTriggerEnd: this.dataScroll.scrollTriggerEnd ?? this.scrollTriggerEnd,
      scrollRepeat: this.dataScroll.scrollRepeat ?? this.scrollRepeat,
      scrollProgress: this.dataScroll.scrollProgress ?? this.scrollProgress,
      scrollMarkers: this.dataScroll.scrollMarkers ?? this.scrollMarkers
    };
  }

  /**
   * Create ScrollTrigger instance
   */
  private createScrollTrigger(config: Required<ScrollAppearConfig>): void {
    try {
      const triggerElement = config.scrollTrigger 
        ? document.querySelector(config.scrollTrigger) 
        : this.elementRef.nativeElement;
      
      const endTriggerElement = config.scrollTriggerEnd 
        ? document.querySelector(config.scrollTriggerEnd) 
        : this.elementRef.nativeElement;

      if (!triggerElement) {
        console.warn(`ScrollAppear: Trigger element not found: ${config.scrollTrigger}`);
        return;
      }

      this.scrollTriggerInstance = ScrollTrigger.create({
        trigger: triggerElement,
        start: config.scrollStartPosition,
        endTrigger: endTriggerElement,
        end: config.scrollEndPosition,
        markers: config.scrollMarkers,
        onToggle: ({ isActive }: { isActive: boolean }) => {
          this.handleScrollToggle(config, isActive);
        },
        onUpdate: ({ progress }: { progress: number }) => {
          this.handleScrollProgress(config, progress);
        }
      });

    } catch (error) {
      console.error('ScrollAppear: Error creating ScrollTrigger:', error);
    }
  }

  /**
   * Handle scroll toggle events
   */
  private handleScrollToggle(config: Required<ScrollAppearConfig>, isActive: boolean): void {
    const element = this.elementRef.nativeElement;
    
    if (config.scrollRepeat) {
      element.classList.toggle(config.scrollClass, isActive);
    } else if (isActive) {
      element.classList.add(config.scrollClass);
    }
  }

  /**
   * Handle scroll progress updates
   */
  private handleScrollProgress(config: Required<ScrollAppearConfig>, progress: number): void {
    if (config.scrollProgress) {
      this.elementRef.nativeElement.style.setProperty('--scroll-progress', progress.toString());
    }
  }

  /**
   * Destroy ScrollTrigger instance
   */
  private destroyScrollTrigger(): void {
    if (this.scrollTriggerInstance) {
      this.scrollTriggerInstance.kill();
      this.scrollTriggerInstance = null;
    }
  }

  /**
   * Refresh ScrollTrigger (useful for dynamic content)
   */
  refresh(): void {
    if (this.scrollTriggerInstance) {
      this.scrollTriggerInstance.refresh();
    }
  }
}
