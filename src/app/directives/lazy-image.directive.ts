import { Directive, ElementRef } from '@angular/core';

@Directive({
  selector: 'img'
})

export class LazyImageDirective {
  constructor ({ nativeElement }: ElementRef<HTMLImageElement>) {
    const supports = 'loading' in HTMLImageElement.prototype;
    // Adding data-unlazy to an image prevents it from lazy-loading
    const lazy = !nativeElement.dataset.unlazy;

    if (supports && lazy) {
      nativeElement.setAttribute('loading', 'lazy');
    }
  }
}
