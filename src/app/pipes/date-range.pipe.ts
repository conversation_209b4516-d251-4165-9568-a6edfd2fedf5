import { Pipe, PipeTransform } from '@angular/core';
import * as moment from "../../../node_modules/moment";

@Pipe({
  name: 'dateRange'
})
export class DateRangePipe implements PipeTransform {

  transform(items: any[], startDate: any, endDate: any): any {
    if(startDate != undefined && endDate != undefined){
      return items.filter(
        item =>  {
          let eventDate = moment(String(item.date));
          if(eventDate >= startDate && eventDate <= endDate)
            return item;
        }
      );
    }
    else
      return items;
  }
}
