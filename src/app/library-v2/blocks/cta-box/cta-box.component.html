<!--  -->
<!-- <lib2-cta-box
    [title]="'views.home.cta-box.title' | translate"
    [text]="'views.home.cta-box.text' | translate"
    [button]="{ 
        link: 'urls.contact' | translate,
        text: 'views.home.cta-box.cta-text' | translate,
        target: '_self', 
        class: '-primary' 
    }"
    tag="h2"
    [reverse]="false"
    [background]="false"
    layout="-default"
    class="-space-default"
></lib2-cta-box> -->

<div class="cta-box container {{ class }} {{ layout }}"
    [ngClass]="{ 
        '-reverse': reverse === true,
        '-no-background': !background
    }"
>
    <div class="inner">
        <div class="content">
            <ng-container [ngSwitch]="tag">
                <h1 *ngSwitchCase="'h1'" class="title">{{ title }}</h1>
                <h2 *ngSwitchCase="'h2'" class="title">{{ title }}</h2>
                <h3 *ngSwitchCase="'h3'" class="title">{{ title }}</h3>
                <h4 *ngSwitchCase="'h4'"class="title">{{ title }}</h4>
            </ng-container>
            <p class="text" *ngIf="text">{{ text }}</p>
        </div>
        <a *ngIf="button" 
            [routerLink]="button.link" 
            [attr.target]="button.target || '_self'" 
            class="btn {{ button.class }}"
        >
            {{ button.text }}
        </a>
    </div>
</div>
