import { Component, Input } from '@angular/core';

export interface SectionButton {
  link: string;
  text: string;
  target?: string;
  class?: string;
}

type tag = 'h2' | 'h3' | 'h4';
type SectionLayout = '-default' | '-column';

@Component({
  selector: 'lib2-cta-box',
  templateUrl: './cta-box.component.html'
})
export class CtaBoxComponent {
  @Input() tag: tag = 'h2';
  @Input() title: string = '';
  @Input() text: string = '';
  @Input() button: SectionButton;
  @Input() class: string = '';
  @Input() layout: SectionLayout = '-default';
  @Input() reverse: boolean = false;
  @Input() background: boolean = true;

  constructor () {

  }

  ngOnInit () {

  }
}
