<!-- Example -->
<!-- <lib2-section-estimation
    [label]="'library.search-sell.label' | translate"
    [title]="'library.search-sell.title' | translate"
    [text]="'library.search-sell.text' | translate"
    [button]="{ 
        text: 'library.search-sell.button' | translate, 
        link: ['urls.real-estate-online-evaluation' | translate],
        class: '-primary' 
    }"
    [image]="{ 
        src: 'assets/images/placeholder/placeholder.jpg', 
        alt: 'library.search-sell.title' | translate
    }"
    class="-space-default"
    [reverse]="false"
></lib2-section-estimation> -->

<div class="section-estimation container {{ class }}"
    [ngClass]="{
        '-reverse': reverse,
        '-no-image': !image,
    }"
>
    <div class="content">
        <p class="label -eyebrow" *ngIf="label">{{ label }}</p>
        <h2 class="title" *ngIf="title">{{ title }}</h2>
        <p class="text" *ngIf="text">{{ text }}</p>
        <form class="form-ctn" (ngSubmit)="onSubmitSell()">
            <div class="input">
                <input type="search" 
                    id="address-input" 
                    class="large" 
                    [(ngModel)]="addressText" 
                    [ngModelOptions]="{ standalone: true }"
                    placeholder="{{ 'library.search-sell.input-placeholder' | translate }}"
                />

                <i class="icon-pin"></i>
            </div>
            <button *ngIf="button" 
                [routerLink]="button.link" 
                type="submit"
                class="btn {{ button.class }}"
            >
                  {{ button.text | translate }}
            </button>
        </form>
    </div>
    <div class="image" *ngIf="image">
        <img [src]="image.src" [alt]="image.alt">
    </div>
</div>
