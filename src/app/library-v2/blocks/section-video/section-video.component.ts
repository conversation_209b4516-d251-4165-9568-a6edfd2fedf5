import { Component, Input, SimpleChanges } from '@angular/core';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';

type SectionLayout = '-default' | '-column';

@Component({
  selector: 'lib2-section-video',
  templateUrl: './section-video.component.html',
  styles: [
  ]
})
export class SectionVideoComponent {
  @Input() label: string = '';
  @Input() title: string = '';
  @Input() text: string = '';
  @Input() videoUrl: string = '';
  @Input() layout: SectionLayout = '-default';
  @Input() class: string = '';
  @Input() reverse: boolean = false;

  safeVideoUrl: SafeResourceUrl;

  constructor (
    private sanitizer: DomSanitizer
  ) {

  }

  ngOnChanges (changes: SimpleChanges) {
    if ('videoUrl' in changes) {
      let url = this.videoUrl;

      // Format for YouTube embed
      if (url.includes('youtube.com/watch?v=')) {
        url = url.replace('watch?v=', 'embed/');
      } else if (url.includes('youtu.be/')) {
        url = url.replace('youtu.be/', 'youtube.com/embed/');
      } else if (url.includes('youtube-nocookie.com/watch?v=')) {
        url = url.replace('watch?v=', 'embed/');
      } else if (url.includes('youtube-nocookie.com/embed/')) {
        // already in embed format, do nothing
      } else if (url.includes('youtube.com/embed/')) {
        // already in embed format, do nothing
      }

      this.safeVideoUrl = this.sanitizer.bypassSecurityTrustResourceUrl(url);
    }
  }
}
