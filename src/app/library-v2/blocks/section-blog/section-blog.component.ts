import { Component, Input } from '@angular/core';
import { BlogService } from '@/services/v3/blog/blog.service';

export interface SectionButton {
  link: string;
  text: string;
  target?: string;
  class?: string;
}

type SectionLayout = '-featured' | '-default';

@Component({
  selector: 'lib2-section-blog',
  templateUrl: './section-blog.component.html'
})
export class SectionBlogComponent {
  @Input() sectionTitle: string = '';
  @Input() icon: string = '';
  @Input() button: SectionButton;
  @Input() class: string = '';
  @Input() itemLayout: string = '';
  @Input() layout: SectionLayout = '-default';

  @Input() nbPosts: number = 3;

  @Input() category = undefined;

  public blogs = [];

  constructor (
    private blog: BlogService
  ) {}

  ngOnInit () {
    // wait for category to exist
    setTimeout(() => {
      this.fetchBlogs();
    }, 500);
  }

  fetchBlogs () {
    this.blog.getPosts(this.nbPosts, this.category).subscribe(({ data }) => {
      this.blogs = data;
    });
  }

  get blogColClass (): string {
    if (this.itemLayout === '-inline') {
      return 'col-12';
    }
    return this.nbPosts >= 3 ? 'col-12 col-t-lg-4' : 'col-12 col-t-lg-6';
  }
}
