<!-- Example -->
<!-- <lib2-section-blog
    [sectionTitle]="'library.blog-list-1.title' | translate"
    icon="icon-pin"
    [button]="{
        text: 'library.blog-list-1.all-posts' | translate,
        link: ['urls.real-estate-blog' | translate],
        class: '-cta-large',
        target: '_self'
    }"
    class="-space-default"
    itemLayout="-default"
    layout="-default"
    nbPosts="3"
    [category]="category"
></lib2-section-blog> -->

<div class="section-blog container {{ layout }} {{ class }}">
    <div class="top">
        <div class="title-ctn">
            <i class="icon {{ icon }}" *ngIf="icon"></i>
            <h2 class="title -no-space" *ngIf="sectionTitle">{{ sectionTitle }}</h2>
        </div>
        <a href="" class="cta -cta-large" 
            *ngIf="button" 
            [routerLink]="button.link" 
            [attr.target]="button.target || '_self'" 
            [ngClass]="button.class"
        >
            {{ button.text }}
        </a>
    </div>

    <div class="blogs-ctn grid">

        <!-- Featured layout -->
        <ng-container *ngIf="layout === '-featured'">
            <div class="col-12 col-t-lg-6">
                <lib2-blog-card
                    *ngIf="blogs.length"
                    [blog]="blogs[0]"
                    [showDescription]="false"
                ></lib2-blog-card>
            </div>
            <div class="col-12 col-t-lg-6 column-list">
                <ng-container *ngFor="let blog of blogs.slice(1)">
                    <lib2-blog-card
                        [blog]="blog"
                        [layout]="'-inline'"
                        [showDescription]="false"
                    ></lib2-blog-card>
                </ng-container>
            </div>
        </ng-container>

        <!-- Normal layout -->
        <ng-container *ngIf="layout !== '-featured'">
            <lib2-blog-card
                *ngFor="let blog of blogs"
                [blog]="blog"
                [ngClass]="blogColClass"
                [layout]="itemLayout"
            ></lib2-blog-card>
        </ng-container>
    </div>
</div>