import { Component, Input } from '@angular/core';

export interface SectionButton {
  link: string;
  text: string;
  target?: string;
  class?: string;
}

export interface SectionImage {
  src: string;
  alt: string;
}

type SectionLayout = '-boxed' | '-small-boxed' | '-full-width' | '-default';

@Component({
  selector: 'lib2-section-content',
  templateUrl: './section-content.component.html'
})
export class SectionContentComponent {
  @Input() label: string = '';
  @Input() title: string = '';
  @Input() text: string = '';
  @Input() button1: SectionButton;
  @Input() button2: SectionButton;
  @Input() image: SectionImage;
  @Input() class: string = '';
  @Input() layout: SectionLayout = '-default';
  @Input() backgroundUrl: string = '';
  @Input() reverse: boolean = false;

  constructor () {

  }

  ngOnInit () {
    // No small layout with image
    if (this.layout === '-small-boxed' && this.image) {
      this.layout = '-boxed';
    }
  }

  isInternalLink (link: string): boolean {
    return !!link && !/^https?:\/\//.test(link) && !/^\/\//.test(link);
  }
}
