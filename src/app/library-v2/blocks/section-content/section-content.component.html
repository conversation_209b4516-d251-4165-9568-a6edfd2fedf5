<!-- EXAMPLE -->
<!-- <lib2-section-content
    [label]="'library.buy-guide-cta.title' | translate"
    [title]="'library.buy-guide-cta.title' | translate"
    [text]="'library.buy-guide-cta.description' | translate"
    [button1]="{ 
        text: 'library.buy-guide-cta.download' | translate, 
        link: 'library.buy-guide-cta.download-link' | translate, 
        target: '_self', 
        class: '-primary' 
    }"
    [button2]="{ 
        text: 'library.buy-guide-cta.download' | translate, 
        link: 'library.buy-guide-cta.download-link' | translate, 
        target: '_blank', 
        class: '-secondary' 
    }"
    [image]="{ 
        src: 'assets/images/placeholder/placeholder.jpg', 
        alt: 'library.buy-guide-cta.alt-image' | translate
    }"
    backgroundUrl="assets/images/placeholder/placeholder-bg.jpg"
    class="-space-default"
    layout="-normal"
    [reverse]="false"
></lib2-section-content> -->

<div class="section-content container {{ class }} {{ layout }}"
    [ngClass]="{
        '-no-image': !image,
        '-has-background-url': backgroundUrl,
        '-reverse': reverse === true
    }"
    [style.backgroundImage]="backgroundUrl ? 'url(' + backgroundUrl + ')' : 'none'"
>
    <div class="inner grid">
        <div class="image -r-normal col-12 col-t-lg-6" *ngIf="image">
            <img [src]="image.src" [alt]="image.alt">
        </div>
        <div class="content col-12 col-t-lg-6">
            <p class="label -eyebrow-normal -no-top-space" *ngIf="label">{{ label }}</p>
            <h2 class="title -no-top-space">{{ title }}</h2>
            <p class="text" *ngIf="text" [innerHTML]="text"></p>
            <div class="btn-ctn -flex" *ngIf="button1 || button2">
                <a *ngIf="button1" 
                   [attr.target]="button1.target || '_self'" 
                   [attr.href]="button1.link"
                   class="btn {{ button1.class }}">
                   {{ button1.text }}
                </a>
                <a *ngIf="button2" 
                    [attr.target]="button2.target || '_self'" 
                    [attr.href]="button2.link"
                    class="btn {{ button2.class }}"
                >
                    {{ button2.text }}
                </a>
            </div>
        </div>
    </div>
</div>