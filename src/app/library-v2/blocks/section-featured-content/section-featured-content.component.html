<!-- Example -->
<!-- <lib2-featured-content
    [sectionTitle]="'views.home.featured-content.title' | translate"
    [cards]="[
        {
            image: 'https://picsum.photos/200/300',
            title: 'Card 1',
            text: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
            link: '/example-link-1'
        },
        {
            image: 'https://picsum.photos/200/300?random=1',
            title: 'Card 2',
            text: 'Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
            link: '/example-link-2'
        },
        {
            image: 'https://picsum.photos/200/300?random=1',
            title: 'Card 3',
            text: 'Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
            link: '/example-link-2'
        }
    ]"
    [button]="{ 
        link: 'kryzalid.net', 
        text: 'Bouton principal', 
        target: '_self', 
        class: '-primary' 
    }"
    class="-space-default"
></lib2-featured-content> -->

<div class="section-featured-content container {{ class }}"
    [ngClass]="{ 
        '-no-cta': !button,
        '-two': cards?.length === 2
    }"
>

    <div class="top">
        <h2 class="title -no-space" *ngIf="sectionTitle">{{ sectionTitle }}</h2>
        <a href="" class="cta -cta-large" 
            *ngIf="button" 
            [routerLink]="button.link" 
            [attr.target]="button.target || '_self'" 
            [ngClass]="button.class"
        >
            {{ button.text }}
        </a>
    </div>
    
    <div class="cards-ctn">
        <a class="card" *ngFor="let card of cards" [attr.href]="card.link">
            <div class="image" *ngIf="card.image">
                <span class="filter"></span>
                <img [src]="card.image" alt="{{ card.title }}" />
            </div>
            <div class="content">
                <h3 class="title -thumbnail-title" *ngIf="card.title">{{ card.title }}</h3>
                <p class="text -no-space" *ngIf="card.text">{{ card.text }}</p>
            </div>
        </a>
    </div>

</div>