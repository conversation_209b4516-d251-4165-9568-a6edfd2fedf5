import { Component, Input } from '@angular/core';

export interface FeaturedCard {
  image: string;
  title: string;
  text: string;
  link: string;
}

export interface SectionButton {
  link: string;
  text: string;
  target?: string;
  class?: string;
}

@Component({
  selector: 'lib2-featured-content',
  templateUrl: './section-featured-content.component.html'
})
export class SectionFeaturedContentComponent {
  @Input() sectionTitle: string = '';
  @Input() cards: FeaturedCard[] = [];
  @Input() button: SectionButton;
  @Input() class: string = '';
}
