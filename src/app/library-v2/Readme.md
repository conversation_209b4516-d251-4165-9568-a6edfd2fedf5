# 📌 Composants HTML - Refonte E-closion  

Ce dossier regroupe l’ensemble des **composants Angular** liés à la refonte des nouveaux composants d’E-closion.  

Ces composants doivent respecter le **design system** défini dans la [Mockup Figma](https://www.figma.com/design/0IhvvIsLH1d3htOGYWu525/E-closion-Template---1.5?node-id=0-1&p=f&m=dev).  

## 📂 Structure des composants  

Les composants sont organisés en différents dossiers selon leur type :  
- **Hero** (`/hero`)  
- **Slider** (`/slider`)  
- **Card** (`/card`)  
- Et ainsi de suite...  

### 📁 Exemple d’arborescence  
```shell
├── /src/app/library-v2/
│   │── hero/
│   │── slider/
│   │── card/
```
---


## ✅ Règles à suivre  

### 📌 1. Respect du design system  
Chaque composant doit correspondre à un style défini dans le dossier suivant : 
```typescript
/src/sass/components-v2/[type]/*
```
Puis loader le partiel de style dans /src/sass/components-v2/main.scss

### 📌 2. Nom et selector des composants  
- Chaque composant doit avoir un **selector identifiable** sous la forme **`lib2-<nom-composant>`**.  
- Exemple de déclaration correcte :  
  ```typescript
  @Component({
    selector: 'lib2-team-hero',
    templateUrl: './team-hero.component.html'
  })

**Exemple commande**
ng g c library-v2/*name* --skip-tests --inline-style --selector=lib2-*name*

- Ajouter l'import et l'appel dans la partie **LIBRARY COMPONENTS V2** et non pas celle de base

### 📌  3. Gestion des contenus
- Tout texte ou image utilisé dans un composant doit être passé via le composant parent grâce aux [input] d’Angular.