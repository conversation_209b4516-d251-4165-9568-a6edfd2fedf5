import { Component, OnInit, Input, ElementRef } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

type SectionLayout = '-hover' | '-default';

@Component({
  selector: 'lib2-property-card',
  templateUrl: './property-card.component.html'
})
export class PropertyCardComponent implements OnInit {
  @Input() layout: SectionLayout = '-default';
  @Input() wideCard: boolean = false;

  @Input() property;
  @Input() rentalSearch = false; // Used to hide rental label when search type is 'rental'

  public currentLang: any;
  propertyUrl: any;

  constructor (
    private hostElement: ElementRef,
    public translate: TranslateService
  ) {
    const userLang = window.location.pathname.split('/')[1];
    this.currentLang = userLang || 'fr';
  }

  ngOnInit () {
    // Build property URL
    if (this.property.neighborhood_slug) {
      this.propertyUrl = '/' + this.property.municipality_slug +
        '/' + this.property.neighborhood_slug +
        '/' + this.property.ext_address_slug +
        '/' + this.property.mls;
    } else {
      this.propertyUrl = '/' + this.property.municipality_slug +
        '/' + this.property.ext_address_slug +
        '/' + this.property.mls;
    }

    // Build display address string
    const { address_civic_start: civic1, address_civic_end: civic2, address_street: street, address_apt: apt } = this.property;
    this.property.displayAddress =
      (civic1 || '') +
      (civic1 && civic2 ? ' - ' + civic2 : '') +
      (street ? ' ' + street : '') +
      (apt ? ' ' + this.translate.instant('library.property-details.apt') + '.' + apt : '');
  }

  ngAfterViewInit () {
    // Only apply dynamic height calculation for hover layout
    if (this.layout !== '-hover') return;

    const propertyCard = this.hostElement.nativeElement.querySelector('.property-card');
    const moreInfo = this.hostElement.nativeElement.querySelector('.more-info');

    if (!propertyCard || !moreInfo) return;

    propertyCard.addEventListener('mouseover', () => {
      moreInfo.style.height = moreInfo.scrollHeight + 'px';
    });

    propertyCard.addEventListener('mouseleave', () => {
      moreInfo.style.height = '0px';
    });
  }
}
