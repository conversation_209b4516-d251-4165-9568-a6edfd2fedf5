<div 
	itemscope itemtype="http://schema.org/House" 
	class="property-card {{ layout }}" 
	[routerLink]="['urls.property-single' | translate] + propertyUrl "
	[ngClass]="{'-wide': wideCard}"
	>
	
	<div class="top-info">

		<!-- Labels -->
		<div class="labels">
	
			<!-- Rental Possible -->
			<span href="" *ngIf="!rentalSearch && property.price_sale && property.price_rental"
				class="label -label-rental-possible">
				{{ "library.properties.rental-possible" | translate }}
			</span>
	
			<!-- Sold -->
			<span href="" *ngIf="property.status"
				class="label -label-sold">
				{{ "library.properties.sold" | translate }}
			</span>
	
			<!-- New -->
			<span href="" *ngIf="property.is_new"
				class="label -label-new">
				{{ "library.properties.new" | translate }}
			</span>
	
			<!-- Openhouse -->
			<span href="" *ngIf="property.openhouses?.length > 0"
				class="label -label-view">
				{{ "library.properties.visit" | translate }}
				{{property.openhouses[0].start_date | localizedDate:'longDate'}}
			</span>
		</div>
	
		<!-- Video/360 visit -->
		<div class="icons">
			<div class="visit-video" *ngIf="property.video">
				<span class="icon-play"></span>
			</div>
			<div class="visit-360" *ngIf="property.visit_360">
				<span class="icon-360"></span>
			</div>
		</div>
	</div>

	<!-- Image -->
	<div class="image">
		<img itemprop="image" src="{{ property.ext_coverphoto }}"
		alt="{{ ['library.properties.alt-image' | translate] + property?.ext_address || '' }}" onerror="this.src='assets/images/placeholder/propriete-nb.jpg';">
		
		<div class="gradiant"></div>
		<div class="filter"></div>
	</div>
	

	<div class="property-info">
		<div class="bloc-head">
			<ng-container *ngIf="!property.status">
				<p class="price -no-space" *ngIf="property.price_sale && !rentalSearch; else showRental">

					<!-- Sale price -->
					<ng-container *ngIf="property.price_sale_unit">
						{{ property.price_sale | currency:'CAD':'symbol-narrow':'1.2-2':this.currentLang }} / 
						{{ property.price_sale_unit | lowercase }}
					</ng-container>

					<!-- Sale price + taxes -->
					<ng-container *ngIf="!property.price_sale_unit" class="s -inner">
						{{ property.price_sale | currency:'CAD':'symbol-narrow':'2.0-0':this.currentLang }}
						<span *ngIf="property.taxes_sale_not_included" class="small-tps">+tps/tvq</span>
					</ng-container>
				</p>

				<ng-template #showRental>
					<p class="price -no-space">

						<!-- Rental residential -->
						<span class="c -inner" *ngIf="property.property_category !== 'Commercial'">
							{{ property.price_rental | currency:'CAD':'symbol-narrow':'2.0-0':this.currentLang }}
							{{ 'library.property-hero.per-month' | translate }}
						</span>

						<!-- Rental commercial by year -->
						<span class="c a -inner" *ngIf="property.property_category === 'Commercial' && property.rental_period === 'A'">
							{{ property.price_rental | currency:'CAD':'symbol-narrow':'2.0-0':this.currentLang }}
							{{ 'library.property-hero.commercial-year' | translate }}
							<span *ngIf="property.taxes_rental_not_included" class="small-tps">+tps/tvq</span>
						</span>

						<!-- Rental commercial by month -->
						<span class="c m -inner" *ngIf="property.property_category === 'Commercial' && property.rental_period !== 'A'">
							{{ property.price_rental | currency:'CAD':'symbol-narrow':'2.0-0':this.currentLang }}
							{{ 'library.property-hero.commercial-month' | translate }}
							<span *ngIf="property.taxes_rental_not_included" class="small-tps">+tps/tvq</span>
						</span>
					</p>
				</ng-template>
			</ng-container>

			<!-- property type -->
			<p class="type -no-space">{{ property.property_type }}</p>
		</div>
		
		<div class="more-info" itemscope itemtype="http://schema.org/PostalAddress">

			<div class="address">
				<!-- Address -->
				<a [routerLink]="['urls.property-single' | translate] + propertyUrl " class="display-address" itemprop="address">
					{{ property.displayAddress }}
				</a>
				<!-- Neighborhood name -->
				<p class="location -no-space" itemprop="addressLocality">
					{{ property.municipality.description | shorten: 25: '...' }}
				</p>
			</div>

			<!-- Rooms number -->
			<div class="numbers">

				<!-- Bedroom -->
				<div class="ctn" *ngIf="property.rooms_bedroom_total_number">
					<i class="icon-bed"></i>
					<p itemprop="numberOfRooms" class="-no-space">{{ property.rooms_bedroom_total_number }}</p>
				</div>

				<!-- Bathroom -->
				<div class="ctn" *ngIf="property.rooms_bathroom_number">
					<i class="icon-shower"></i>
					<p itemprop="numberOfRooms" class="-no-space">{{ property.rooms_bathroom_number }}</p>
				</div>
			</div>
		</div>
	</div>
</div>