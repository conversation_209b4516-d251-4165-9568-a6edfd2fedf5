<div class="card blog-card {{ class }} {{ layout }}" [routerLink]="['urls.real-estate-blog' | translate, blog.slug ]">
    <a *ngIf='blog.coverphoto' 
        class="image" 
        [routerLink]="['urls.real-estate-blog' | translate, blog.slug ]"
        [attr.rel]="blog.no_index === 1 ? 'nofollow noindex' : null"
    >
        <div class="filter"></div>
        <img src="{{ blog.coverphoto }}" alt="{{'library.article.alt-image' | translate}}{{ blog.title }}">
    </a>
    <div class="article-info">
        <h3 class="title -thumbnail-title">
            <a [routerLink]="['urls.real-estate-blog' | translate, blog.slug ]">
                {{ blog.title }}
            </a>
        </h3>
        <p class="date -thumbnail-meta">{{ blog.publication_date | localizedDate:'longDate' }}</p>
        <p class="description" *ngIf="showDescription">{{ blog.abstract | shorten: 600: '...' }}</p>
    </div>
</div>
