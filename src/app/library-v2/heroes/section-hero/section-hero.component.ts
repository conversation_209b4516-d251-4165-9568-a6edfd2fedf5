import { Component, Input } from '@angular/core';

type SectionLayout = '-default' | '-overlay' | '-full' | '-contained';

export interface SectionImage {
  src: string;
  alt: string;
}

@Component({
  selector: 'lib2-section-hero',
  templateUrl: './section-hero.component.html'
})
export class SectionHeroComponent {
  @Input() label: string = '';
  @Input() title: string = '';
  @Input() text: string = '';
  @Input() image: SectionImage;
  @Input() class: string = '';
  @Input() layout: SectionLayout = '-default';

  constructor () {

  }

  ngOnInit () {

  }
}
