<!-- Example -->
<!-- <lib2-section-hero
    [label]="'views.buy.label' | translate"
    [title]="'views.buy.title' | translate"
    [text]="'views.buy.title' | translate"
    [image]="{ 
        src: 'assets/images/placeholder/hochelaga.png',
        alt: 'views.buy.title' | translate
    }"
    class="my-custom-class"
    layout="-default"
></lib2-section-hero> -->

<div class="section-hero {{ class }} {{ layout }}"
    [ngClass]="{
        '-has-text': text,
    }"
>
    <div class="content">
        <p class="label -eyebrow-large -no-top-space" *ngIf="label">{{ label }}</p>
        <h1 class="title">{{ title }}</h1>
    </div>
    
    <div class="ctn">
        <div class="image" *ngIf="image">
            <img [src]="image.src" [alt]="image.alt">
        </div>
        <p class="text" *ngIf="text">{{ text }}</p>
    </div>
</div>
