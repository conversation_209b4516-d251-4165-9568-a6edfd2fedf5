<!-- Example -->
<!-- <lib2-home-hero
    bannerImage="https://picsum.photos/1200/600?random=1" // optional will force remove video
    [button1]="{
        text: 'Search properties',
        link: ['urls.search' | translate],
        class: '-primary',
        target: '_self'
    }"
    [button2]="{
        text: 'Get a free evaluation',
        link: ['urls.evaluation' | translate],
        class: '-secondary',
        target: '_self'
    }"
    class="-custom"
></lib2-home-hero> -->

<div class="home-hero {{ class }}" 
    [ngClass]="{
      '-has-video': video,
      '-static-image': bannerImage
    }"
>
    <div class="video-wrapper">
        <iframe class="video" 
            *ngIf="cleanVideo" 
            [src]="cleanVideo" 
            title="YouTube video player" 
            frameborder="0" allow="autoplay" 
            allowfullscreen
        >
        </iframe>
        <img class="thumbnail" *ngIf="thumbnail" [src]="thumbnail" alt="Video thumbnail">
        <img class="static" [src]="bannerImage" alt="" *ngIf="bannerImage">
    </div>
    <div class="content">
        <h1 class="title -large" *ngIf="title">{{ title }}</h1>
        <p class="text" *ngIf="text">{{ text }}</p>
        <div class="btn-ctn" *ngIf="button1 || button2">
            <a [routerLink]="button1.link" 
                class="main-button" 
                [ngClass]="button1.class" 
                [target]="button1.target">
                {{ button1.text }}
            </a>
            <a [routerLink]="button2.link" 
                class="main-button"
                [ngClass]="button2.class" 
                [target]="button2.target">
                {{ button2.text }}
            </a>
        </div>
    </div>
</div>
