import { Component, Input, OnInit } from '@angular/core';
import { HomePageService } from '@/services/v3/home-page-block/home-page-block.service';
import { SafeResourceUrl, DomSanitizer } from '@angular/platform-browser';

export interface SectionButton {
  link: string;
  text: string;
  target?: string;
  class?: string;
}

@Component({
  selector: 'lib2-home-hero',
  templateUrl: './home-hero.component.html'
})
export class HomeHeroComponent implements OnInit {
  @Input() class: string = '';
  @Input() button1: SectionButton;
  @Input() button2: SectionButton;
  @Input() bannerImage: string = '';

  customContent: any;
  video: string;
  title: string;
  text: string;
  thumbnail: string;

  cleanVideo: SafeResourceUrl;
  blockTitle: string;

  constructor (
    private homePageService: HomePageService,
    private sanitizer: DomSanitizer
  ) {}

  ngOnInit () {
    this.homePageService.getHomePage().subscribe(({ data }) => {
      this.customContent = data;

      this.video = this.customContent.banner_video_formatted;
      this.title = this.customContent.section_video_title;
      this.text = this.customContent.section_video_description;
      this.thumbnail = this.customContent.banner_video_url.image;
      this.cleanVideo = this.sanitizer.bypassSecurityTrustResourceUrl(this.video);
    });
  }
}
