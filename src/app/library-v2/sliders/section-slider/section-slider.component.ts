import { Component, Input, TemplateRef, AfterViewInit, ViewChild, ElementRef } from '@angular/core';
import Swiper from 'swiper';
import { Navigation, Pagination, Autoplay } from 'swiper/modules';

export interface SectionButton {
  link: string;
  text: string;
  target?: string;
  class?: string;
}

@Component({
  selector: 'lib2-section-slider',
  templateUrl: './section-slider.component.html'
})
export class SectionSliderComponent implements AfterViewInit {
  @Input() title: string;
  @Input() icon: string;
  @Input() button: SectionButton;
  @Input() class: string = '';
  @Input() navigation: boolean = true;

  // Slider setup
  @Input() items: any[] = [];
  @Input() itemTemplate: TemplateRef<any>;

  // Swiper options
  @Input() loop: boolean = true;
  @Input() autoplay: boolean = false;
  @Input() touch: boolean = true;
  @Input() breakpoints: any;

  @ViewChild('swiperContainer', { static: false }) swiperContainer: ElementRef;

  slider: any;
  private resizeListener: () => void;

  constructor () {}

  ngOnInit () {

  }

  ngAfterViewInit () {
    this.initSlider();
    this.resizeListener = () => this.updateGridGutter();
    window.addEventListener('resize', this.resizeListener);
  }

  // Force update slider gutter on resize
  updateGridGutter () {
    const gridGutter = this.getGridGutter();
    if (this.slider) {
      this.slider.params.spaceBetween = gridGutter;
      this.slider.update();
    }
  }

  // Get grid gutter from CSS variable
  getGridGutter () {
    // get css variable living in :root --grid-gutter
    const rootStyles = getComputedStyle(document.documentElement);
    const gridGutter = parseInt(rootStyles.getPropertyValue('--grid-gutter'), 10) || 0;
    return gridGutter;
  }

  initSlider () {
    Swiper.use([Navigation, Pagination, Autoplay]);
    const gridGutter = this.getGridGutter();
    const AUTOPLAY_DELAY = 6000;

    // get navigation elements
    const prevEl = this.swiperContainer.nativeElement.closest('.section-slider').querySelector('.swiper-btn.prev');
    const nextEl = this.swiperContainer.nativeElement.closest('.section-slider').querySelector('.swiper-btn.next');

    this.slider = new Swiper(this.swiperContainer.nativeElement, {
      loop: this.loop,
      allowTouchMove: this.touch,
      autoplay: this.autoplay ? { delay: AUTOPLAY_DELAY } : false,
      spaceBetween: gridGutter,
      speed: 800,
      observer: true,
      runCallbacksOnInit: false,
      slidesPerView: 3.5,
      freeMode: false,
      navigation: {
        nextEl,
        prevEl
      },
      breakpoints: this.breakpoints
    });
  }
}
