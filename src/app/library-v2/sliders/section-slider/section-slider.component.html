<!-- Example -->
<!-- <lib2-section-slider
    [title]="'views.home.section-slider.title' | translate"
    icon="icon-star"
    [button]="{
        text: 'views.home.section-slider.cta-text' | translate,
        link: ['urls.real-estate-properties' | translate],
        class: '-cta-large',
        target: '_self'
    }"
    class="-space-default"
    [navigation]="true"
    [items]="properties"
    [itemTemplate]="sliderTemplate"
    [loop]="true"
    [autoplay]="false"
    [touch]="true"
    [breakpoints]="breakpoints"
>
</lib2-section-slider>


<ng-template #sliderTemplate let-item>
    <lib-properties [property]="item" class="properties"></lib-properties> 
</ng-template> -->

<div class="section-slider container {{ class }}">
    <div class="top">
        <div class="left">
            <i *ngIf="icon" class="icon {{ icon }}"></i>
            <h2 class="title -no-space" *ngIf="title">{{ title }}</h2>
        </div>

        <div class="right">
            <a href="" class="cta -cta-small" 
                *ngIf="button" 
                [routerLink]="button.link" 
                [attr.target]="button.target || '_self'" 
                [ngClass]="button.class"
            >
                {{ button.text }}
            </a>

             <div class="btn-ctn" *ngIf="navigation">
                <div class="swiper-btn prev"><i class="icon-arrow-left"></i></div>
                <div class="swiper-btn next"><i class="icon-arrow-right"></i></div>
            </div>
        </div>
    </div>


    <div class="swiper-container" #swiperContainer>
        <div class="swiper-wrapper">

            <ng-container *ngFor="let item of items">
                <div class="swiper-slide">
                    <ng-container *ngTemplateOutlet="itemTemplate; context: { $implicit: item }"></ng-container>
                </div>
            </ng-container>

        </div>
    </div>
</div>
