import { Component, Input, ViewChild, ElementRef } from '@angular/core';
import { TestimonialsService } from '@/services/v3/testimonials/testimonials.service';
import { GooglemybusinessService } from '@/services/v3/googlemybusiness/googlemybusiness.service';
import Swiper from 'swiper';
import { Navigation, Pagination, Autoplay } from 'swiper/modules';

type SectionLayout = '-default' | '-slider' | '-carousel';
type testimonialsProvider = 'eclosion' | 'google';

export interface TestimonyReview {
  image?: string;
  author: string;
  rating?: number | string;
  date?: string;
  text: string;
}

@Component({
  selector: 'lib2-section-testimony',
  templateUrl: './section-testimony.component.html'
})
export class SectionTestimonyComponent {
  @Input() label: string = '';
  @Input() title: string = '';
  @Input() layout: SectionLayout = '-default';
  @Input() testimonialsProvider: testimonialsProvider = 'eclosion';
  @Input() class: string = '';

  @ViewChild('swiperContainer', { static: false }) swiperContainer: ElementRef;

  public reviews: TestimonyReview[] = [];
  private testimonialSwiper: any;

  constructor (
    private testimonialsService: TestimonialsService,
    private googlemybusiness: GooglemybusinessService
  ) {

  }

  ngOnInit () {
    // Set either Eclosion or Google as the testimonials provider
    if (this.testimonialsProvider === 'eclosion') {
      this.testimonialsService.getTestimonials(3, 'rdm').subscribe(({ data }) => {
        // Remap the reviews into the expected format
        this.reviews = data.map((review: any) => ({
          author: review.author,
          text: review.description
        }));

        this.initSlider();
      });
    } else if (this.testimonialsProvider === 'google') {
      this.googlemybusiness.getGoogleMyBusiness().subscribe(({ data }) => {
        const googleReviews = JSON.parse(data.google_my_business_reviews);
        this.reviews = googleReviews.reviews
          // Filter reviews with no text if layout is -default
          .filter((review: any) => {
            if (this.layout === '-default') {
              return review.comment && review.comment.trim() !== '';
            }
            return true;
          })
          .map((review: any) => ({
            image: review.reviewer.profilePhotoUrl,
            author: review.reviewer.displayName,
            rating: this.getNumericRating(review.starRating),
            date: this.formatDate(review.createTime),
            text: this.filterText(review.comment)
          }));

        this.initSlider();
      });
    }
  }

  initSlider () {
    Swiper.use([Navigation, Pagination, Autoplay]);

    const rootStyles = getComputedStyle(document.documentElement);
    const gridGutter = parseInt(rootStyles.getPropertyValue('--grid-gutter'), 10) || 0;

    const breakpoints = this.layout === '-carousel'
      ? {
          1024: {
            slidesPerView: 3,
            spaceBetween: gridGutter
          },
          768: {
            slidesPerView: 2,
            spaceBetween: gridGutter
          }
        }
      : null;

    const container = this.swiperContainer.nativeElement;
    const paginationEl = container.querySelector('.swiper-pagination');
    const prevEl = container.querySelector('.swiper-btn.prev');
    const nextEl = container.querySelector('.swiper-btn.next');

    this.testimonialSwiper = new Swiper(container, {
      speed: 800,
      observer: true,
      loop: true,
      autoplay: {
        delay: 6000
      },
      runCallbacksOnInit: false,
      slidesPerView: 1,
      spaceBetween: 0,
      freeMode: false,
      pagination: {
        el: paginationEl,
        type: 'bullets'
      },
      navigation: {
        nextEl,
        prevEl
      },
      breakpoints: breakpoints
    });
  }

  // Reformat for google reviews
  getNumericRating (rating: string): number {
    const ratingMap: { [key: string]: number } = {
      'ONE': 1,
      'TWO': 2,
      'THREE': 3,
      'FOUR': 4,
      'FIVE': 5
    };
    return ratingMap[rating] || 0;
  }

  formatDate (dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-CA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }

  filterText (text: string): string {
    if (!text) {
      return '';
    }
    const index = text.indexOf('(Translated by Google)');
    const cleanText = index !== -1 ? text.substring(0, index).trim() : text;
    return `<p>${cleanText}</p>`;
  }
}
