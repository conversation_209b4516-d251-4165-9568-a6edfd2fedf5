<!-- Example -->
<!-- <lib2-section-testimony
    [label]="'library.reviews.label' | translate"
    [title]="'library.reviews.title' | translate"
    layout="-default"
    testimonialsProvider="google"
    class="-space-default"
></lib2-section-testimony> -->

<div class="section-testimony container {{ layout }} {{ class }}">
    <div class="content">
        <p *ngIf="label" class="label -eyebrow">{{ label }}</p>
        <h2 class="title -no-space">{{ title }}</h2>
    </div>

    <div class="swiper-container swiper-testimony" #swiperContainer>
        <div class="swiper-wrapper inner">

            <div class="swiper-slide" *ngFor="let review of reviews">
                <div class="info-ctn">
                    <div class="image" *ngIf="review.image">
                        <img src="{{ review.image }}" alt="Reviewer Image">
                    </div>
                    <div class="info">
                        <p class="author -no-space" *ngIf="layout !== '-default'">{{ review.author }}</p>
                        <div class="stars" *ngIf="review.rating">
                            <i *ngFor="let star of [1, 2, 3, 4, 5]" 
                                class="icon-star" 
                                [ngClass]="{
                                    'checked': star <= review.rating
                                }"
                            ></i>
                        </div>
                        <p class="date -no-space" *ngIf="review.date">{{ review.date }}</p>
                    </div>
                </div>
                <div class="text" [innerHTML]="review.text"></div>
                <p class="author -cta-large" *ngIf="layout === '-default'">{{ review.author }}</p>
            </div>
        </div>

        <div class="btn-ctn">
            <div class="swiper-btn prev"><i class="icon-arrow-left"></i></div>
            <div class="swiper-btn next"><i class="icon-arrow-right"></i></div>
        </div>

        <div class="swiper-pagination"></div>
    </div>
</div>
