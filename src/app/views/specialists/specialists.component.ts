import { Component, OnInit } from '@angular/core';
import { CollaboratorsService } from '@/services/v3/collaborators/collaborators.service';
import { MetatagsService } from '@/services/v3/metatags/metatags.service';

@Component({
  selector: 'view-specialists',
  templateUrl: './specialists.component.html'
})

export class SpecialistsComponent implements OnInit {
  public specialists: any = [];
  public specialistCategories: any = [];
  public cPage: number = 1;
  public currentCategory: string;
  public totalLength: number;
  public limitPerPage = 9;

  constructor (
    private collaboratorsService: CollaboratorsService,
    private metatagsService: MetatagsService
  ) {
    this.metatagsService.updateMetatags('specialists');
  }

  ngOnInit () {
    this.collaboratorsService.getCollaborators().subscribe(({ data }) => {
      this.specialists = data;
      this.totalLength = this.specialists.length;
    });

    this.collaboratorsService.getCategories().subscribe(({ data }) => {
      this.specialistCategories = data;
    });
  }

  onPageChange (number: number) {
    this.cPage = number;
    document.getElementById('specialist-list').scrollIntoView({ behavior: 'smooth' });
  }

  onCategoryChange (categorySlug: string) {
    this.currentCategory = categorySlug;
    this.cPage = 1;
    this.totalLength = this.specialists.filter(s => s.slug_category === categorySlug).length;
  }
}
