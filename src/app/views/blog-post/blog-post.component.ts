import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { DomSanitizer } from '@angular/platform-browser';
import { HostListener } from '@angular/core';
import { Meta } from '@angular/platform-browser';


import { TranslateService } from '@ngx-translate/core';

import { BlogService } from '@/services/v3/blog/blog.service';
import { MetatagsService } from '@/services/v3/metatags/metatags.service';


@Component({
  selector: 'view-blog-post',
  templateUrl: './blog-post.component.html'
})

export class BlogPostComponent implements OnInit {
  public post: any;
  public iframeUrl: any;

  constructor (
    private router: Router,
    private blog: BlogService,
    private route: ActivatedRoute,
    private translate: TranslateService,
    private metatagsService: MetatagsService,
    private sanitizer: DomSanitizer,
    private meta: Meta
  ) {
    this.router.routeReuseStrategy.shouldReuseRoute = function () { return false; };

    const preview = this.route.snapshot.queryParams.preview;
    const slug = this.route.snapshot.paramMap.get('slug');

    this.blog.getPost(slug, preview).subscribe(({ success, data }) => {
      if (!success) return this.router.navigateByUrl(this.translate.instant('urls.home'));

      // Use meta data if available
      const title = data.meta_title || data.title;
      const desc = data.meta_description || data.abstract;
      const image = data.image_url.fullsize;
      this.metatagsService.updateMetatags({ title, desc, image });

      // Force no index if specified
      if (data.no_index === 1) {
        this.meta.updateTag({ name: 'robots', content: 'noindex, nofollow' });
      }

      if (data.iframe_url !== 'https://') this.iframeUrl = this.sanitizer.bypassSecurityTrustResourceUrl(data.iframe_url);
      this.post = data;
    });
  }

  ngOnInit () {
  }


}
