import { Component, OnInit } from '@angular/core';
import { BlogService } from '@/services/v3/blog/blog.service';
import { MetatagsService } from '@/services/v3/metatags/metatags.service';

@Component({
  selector: 'view-blog',
  templateUrl: './blog.component.html'
})

export class BlogComponent implements OnInit {
  public blogs = [];
  public blogCategories = [];

  public cPage: number = 1;
  public currentCategory: string;
  public totalLength: number;
  public limitPerPage = 4;

  constructor (
    private blogService: BlogService,
    private metatagsService: MetatagsService
  ) {
    this.metatagsService.updateMetatags('blog');
  }

  ngOnInit () {
    this.blogService.getPosts('', '').subscribe(({ data }) => {
      this.blogs = data;
      this.totalLength = data.length;
    });

    // Get blog categories
    this.blogService.getCategories().subscribe(({ data }) => {
      this.blogCategories = data;
    });
  }

  onPageChange (number: number) {
    this.cPage = number;
    document.getElementById('blog-list').scrollIntoView({ behavior: 'smooth' });
  }

  onCategoryChange (categorySlug: string) {
    this.currentCategory = categorySlug;
    this.cPage = 1;
    this.totalLength = this.blogs.filter(p => p.category_slug === categorySlug).length;
  }
}
