<lib-header></lib-header>

<h1 class="-center -no-bottom-space">{{ 'library.blog-list-2.title' | translate }}</h1>

<!-- todo move to a modular cpn -->
<ul class="sortable-list tabs -center">
    <input class="input-tab" id="tab1" type="radio" name="tabs" checked>
    <label (click)="onCategoryChange('')" for="tab1">{{ 'library.blog-list-3.all' | translate }}</label>

    <ng-container *ngFor="let category of blogCategories">
        <input class="input-tab" id="tab-{{category.slug}}" type="radio" name="tabs">
        <label (click)="onCategoryChange(category.slug)" for="tab-{{category.slug}}">{{category.name}}</label>
    </ng-container>
</ul>

<!-- Grid view -->
<div class="blogs-ctn container grid -grid">
    <lib2-blog-card 
        *ngFor="let blog of blogs | 
        orderBy: '-publication_date' | 
        filterBy: ['category_slug']: currentCategory | 
        paginate: { 
            id: 'blog-pagination', 
            itemsPerPage: limitPerPage, 
            currentPage: cPage, 
            totalItems:totalLength 
        }" 
        [blog]="blog"
        class="col-12 col-t-lg-4 col-t-6"
    >
    </lib2-blog-card>
</div>

<!-- List view -->
<!-- <div class="blogs-ctn container -list">
    <lib2-blog-card 
        *ngFor="let blog of blogs | 
        orderBy: '-publication_date' | 
        filterBy: ['category_slug']: currentCategory | 
        paginate: { 
            id: 'blog-pagination', 
            itemsPerPage: limitPerPage, 
            currentPage: cPage, 
            totalItems:totalLength 
        }" 

        [blog]="blog"
        layout="-inline"
    >
    </lib2-blog-card>
</div> -->

<pagination-controls id="blog-pagination" class="paginiation-controls"
    (pageChange)="onPageChange($event)"
    maxSize="5"
    directionLinks="true"
    autoHide="true"
    previousLabel=""
    nextLabel="">
</pagination-controls>

<lib-footer></lib-footer>
