import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';

import { InscriptionGroupsService } from '@/services/v3/inscriptiongroups/inscriptiongroups.service';
import { MetatagsService } from '@/services/v3/metatags/metatags.service';

@Component({
  selector: 'view-propertygroup',
  templateUrl: './propertygroup.component.html'
})

export class PropertygroupComponent implements OnInit {
  public propertygroup: any;

  constructor (
    private router: Router,
    private inscriptionGroupsService: InscriptionGroupsService,
    private route: ActivatedRoute,
    private metatagsService: MetatagsService
  ) {
    const id = this.route.snapshot.paramMap.get('slug');
    this.inscriptionGroupsService.getInscriptionGroup(id).subscribe(({ data }) => {
      this.propertygroup = data;
    });

    this.router.routeReuseStrategy.shouldReuseRoute = function () {
      return false;
    };

    this.router.events.subscribe((evt) => {
      if (evt instanceof NavigationEnd) {
        // trick the Router into believing it's last link wasn't previously loaded
        this.router.navigated = false;
      }
    });

    // Set metas
    this.metatagsService.updateMetatags('property-groups');
  }

  ngOnInit () {
  }
}
