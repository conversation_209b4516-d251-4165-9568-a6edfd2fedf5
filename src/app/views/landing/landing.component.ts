import { Component, OnInit } from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import { TranslateService } from "@ngx-translate/core";

import { MetatagsService } from "@/services/v3/metatags/metatags.service";
import { LandingService } from "@/services/v3/landing/landing.service";
import { DomSanitizer } from "@angular/platform-browser";

@Component({
  selector: "view-landing",
  templateUrl: "./landing.component.html",
})
export class LandingComponent implements OnInit {
  slug;
  preview;
  campaign;
  urlTranslated;
  translateLang;
  hasTranslation;

  constructor(
    private landingService: LandingService,
    public translate: TranslateService,
    private metatagsService: MetatagsService,
    private route: ActivatedRoute,
    private router: Router,
    private sanitizer: DomSanitizer
  ) {
    this.preview = this.route.snapshot.queryParams.preview;
    this.slug = this.route.snapshot.paramMap.get("slug");

    translate.onLangChange.subscribe(() => {
      if (this.urlTranslated) this.router.navigateByUrl(this.urlTranslated);
    });
  }

  async ngOnInit() {
    const { success, total, message, data } = await this.landingService
      .getLanding(this.slug, this.preview)
      .toPromise();

    // Redirect if invalid
    if (!success || !total) {
      return this.router.navigateByUrl(this.translate.instant("urls.home"));
    }

    this.campaign = data;
    this.campaign.content = this.sanitizer.bypassSecurityTrustHtml(
      this.campaign.content
    );

    // Set metas
    const {
      meta_title: title,
      meta_description: desc,
      cover_image: image,
    } = data;
    this.metatagsService.updateMetatags({ title, desc, image });

    // Hide Translation Switch Lang
    this.translateLang = this.translate.currentLang === "fr" ? "en" : "fr";
    this.hasTranslation = !!this.campaign["slug_" + this.translateLang];
    if (this.hasTranslation) this.urlTranslated = this.getTranslateUrl();
  }

  switchLang() {
    const toLang = this.translate.currentLang === "fr" ? "en" : "fr";
    this.urlTranslated = this.getTranslateUrl();
    this.translate.use(this.translateLang);
  }

  getTranslateUrl() {
    let url = this.router.url;
    const toLang = this.translate.currentLang === "fr" ? "en" : "fr";
    const _temp = url.split("/");
    url = "/" + _temp[1] + "/" + _temp[2];
    return (
      this.translate.instant("urls.translate." + url) +
      "/" +
      this.campaign["slug_" + this.translateLang] +
      (this.preview ? "?preview=true" : "")
    );
  }
}
