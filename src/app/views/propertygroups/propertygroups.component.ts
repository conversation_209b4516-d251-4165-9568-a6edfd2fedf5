import { Component, OnInit } from '@angular/core';
import { InscriptionGroupsService } from '@/services/v3/inscriptiongroups/inscriptiongroups.service';
import { MetatagsService } from '@/services/v3/metatags/metatags.service';

@Component({
  selector: 'view-propertygroups',
  templateUrl: './propertygroups.component.html'
})

export class PropertygroupsComponent implements OnInit {
  public propertygroups: any;

  constructor (
    private inscriptionGroupsService: InscriptionGroupsService,
    private metatagsService: MetatagsService
  ) {
    this.metatagsService.updateMetatags('property-groups');
  }

  ngOnInit () {
    this.inscriptionGroupsService.getInscriptionGroups().subscribe(({ data }) => {
      this.propertygroups = data;
    });
  }
}
