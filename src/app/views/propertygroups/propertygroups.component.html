<lib-header></lib-header>
<div class="propertygroups-page">
	<div class="property-group-head container">
		<h1 class="page-title">{{ 'views.propertygroups.title' | translate }}</h1>
		<p class="page-description">{{ 'views.propertygroups.description' | translate }}</p>
		<div class="btn-ctn">
			<a [routerLink]="['urls.contact' | translate ]" class="main-button -primary">{{ 'views.propertygroups.button' | translate }}</a>
		</div>
	</div>
	<div class="property-group-card-ctn container">
		<div class="propertygroup-card-cpn" *ngFor="let propertygroup of propertygroups">
			<lib-propertygroup-card [propertygroup]="propertygroup"></lib-propertygroup-card>
		</div>
	</div>
</div>

<lib-footer></lib-footer>
