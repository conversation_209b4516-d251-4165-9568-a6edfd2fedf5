<lib-header></lib-header>
<lib-property-hero [property]="property" [photos]="photos"></lib-property-hero>

<lib-property-sheet 
  [property]="property" 
  [rooms]='rooms' 
  [brokers]='brokers' 
  [openHouses]='openHouses' 
  [characteristics]='characteristics' 
  [addenda]='addenda' 
  [expenses]="expenses" 
  [documentsList]="documentsList">
</lib-property-sheet>

<lib-property-statistics [property]="property"></lib-property-statistics>
<lib-property-map></lib-property-map>

<!-- <lib-properties-list-2 *ngIf="properties?.length > 0" [properties]="properties"></lib-properties-list-2> -->

<!-- Properties slider -->
<div *ngIf="properties?.length > 0">
  <lib2-section-slider
      [title]="'views.home.section-slider.title' | translate"
      icon="icon-pin"
      [button]="{
          text: 'library.properties-list.all' | translate,
          link: ['urls.search-properties' | translate],
          class: '-cta-large',
          target: '_self'
      }"
      class="-space-default"
      [navigation]="true"
      [items]="properties"
      [itemTemplate]="sliderTemplate"
      [loop]="true"
      [autoplay]="false"
      [touch]="true"
      [breakpoints]="breakpoints"
  ></lib2-section-slider>
  
  <ng-template #sliderTemplate let-item>
      <lib2-property-card [property]="item" class="properties" [wideCard]="true" layout="-hover"></lib2-property-card> 
  </ng-template>
</div>

<lib-property-navigation [previousNav]="previousNav" [nextNav]="nextNav"></lib-property-navigation>
<lib-footer></lib-footer>
