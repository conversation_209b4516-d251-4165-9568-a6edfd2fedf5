import { Component, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { MetatagsService } from '@/services/v3/metatags/metatags.service';

@Component({
  selector: 'view-sell',
  templateUrl: './sell.component.html'
})

export class SellComponent implements OnInit {
  category: any;
  pdfGuide: any;

  constructor (
    private translate: TranslateService,
    private metatagsService: MetatagsService
  ) {
    this.metatagsService.updateMetatags('sell');
  }

  async ngOnInit () {
    this.pdfGuide = 'assets/images/common/sellers-guide-' + this.translate.currentLang + '.jpg';
    this.category = await this.translate.get('views.sell.blog-category').toPromise();
  }
}
