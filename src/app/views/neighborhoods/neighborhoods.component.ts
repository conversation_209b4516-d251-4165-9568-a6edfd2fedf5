import { Component, OnInit } from '@angular/core';
import { LocalStorageService } from 'ngx-webstorage';

import { NeighborhoodsService } from '@/services/v3/neighborhoods/neighborhoods.service';
import { MetatagsService } from '@/services/v3/metatags/metatags.service';

@Component({
  selector: 'view-neighborhoods',
  templateUrl: './neighborhoods.component.html'
})

export class NeighborhoodsComponent implements OnInit {
  public neighborhoods: any;

  constructor (
    private storage: LocalStorageService,
    private neighborhoodsService: NeighborhoodsService,
    private metatagsService: MetatagsService
  ) {
    this.metatagsService.updateMetatags('neighborhoods');
  }

  ngOnInit () {
    this.neighborhoodsService.getNeighborhoods().subscribe(({ data }) => {
      // this.neighborhoods = data;
      // this.addToLocalStorage();
      this.neighborhoods = data.features.map(f => {
        const props = { ...f.properties };
        const text = props.description?.replace(/<h[1-6].*?>.*?<\/h[1-6]>/g, '');
        const firstP = text?.match(/<p.*?>(.*?)<\/p>/)?.[1] || text;
        const desc = firstP?.substring(0, 250);
        return {
          ...props,
          description: desc.length === 250 ? desc + '...' : desc
        };
      });
      this.addToLocalStorage();
    });
  }

  addToLocalStorage () {
    if (Array.isArray(this.neighborhoods) && this.neighborhoods.length) {
      localStorage.setItem('neighborhoods', JSON.stringify(this.neighborhoods));
    }
  }
}
