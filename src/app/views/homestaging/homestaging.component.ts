import { Component, OnInit } from '@angular/core';
import { HomestagingService } from '@/services/v3/homestaging/homestaging.service';
import { MetatagsService } from '@/services/v3/metatags/metatags.service';

@Component({
  selector: 'view-homestaging',
  templateUrl: './homestaging.component.html'
})

export class HomestagingComponent implements OnInit {
  public homestagings: any;

  constructor (
    private homestagingService: HomestagingService,
    private metatagsService: MetatagsService
  ) {
    this.metatagsService.updateMetatags('homestaging');
  }

  ngOnInit () {
    this.homestagingService.getHomestaging().subscribe(({ data }) => {
      this.homestagings = data;
    });
  }
}
