<lib-header></lib-header>
<div class="favorites-page -space-lg">
	<div class="container">
		<div class="favorite-head -page-head">
			<h1 class="-center">{{ 'views.favorites.title' | translate }}</h1>
		</div>

		<div class="propeties-wrap" *ngIf="properties">
			<div class="properties-list-ctn">
				<div class="properties" *ngFor="let property of properties.data">
					<lib-properties [property]="property"></lib-properties>
				</div>
			</div>

			<div *ngIf="properties.count == 0 && isLoad" class="-center">
				<p>{{ 'views.favorites.no-favorites' | translate }}</p>
			</div>
		</div>

		<div class="button-ctn">
			<a [routerLink]="['urls.search-properties' | translate ]" class="main-button -primary">{{ 'views.favorites.button' | translate }}</a>
		</div>
	</div>
</div>
<lib-footer></lib-footer>
