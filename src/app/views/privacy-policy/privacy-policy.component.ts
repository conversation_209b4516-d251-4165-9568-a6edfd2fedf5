import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@angular/core";
import { TranslateService } from "@ngx-translate/core";

@Component({
  selector: "view-private-policy",
  templateUrl: "./privacy-policy.component.html",
})
export class PrivacyPolicyComponent implements OnInit, OnDestroy {
  constructor(public translate: TranslateService) {}

  public scriptElement: any;
  public privacyContent: any;

  ngOnInit() {
    this.scriptElement = this.loadScript(
      "https://consent.cookiebot.com/fa83cde6-5977-4847-ab75-c89e10975b54/cd.js"
    );
    this.translate.get("views.privacy-policy.content").subscribe((res: any) => {
      this.privacyContent = res;
    });
  }

  ngOnDestroy() {
    this.removeScript();
  }

  private loadScript(scriptUrl: string) {
    const cookiesPolicy = document.getElementById("cookiesPolicy");
    const script = document.createElement("script");

    script.innerHTML = "";
    script.src = scriptUrl;
    script.async = false;
    script.defer = true;

    // Check the current language and set data-culture accordingly
    const currentLang = this.translate.currentLang;
    if (currentLang === "fr") {
      script.setAttribute("data-culture", "fr");
    } else if (currentLang === "en") {
      script.setAttribute("data-culture", "en");
    }

    cookiesPolicy.appendChild(script);

    return script;
  }

  private removeScript() {
    if (this.scriptElement) {
      this.scriptElement.remove();
    }
  }
}
