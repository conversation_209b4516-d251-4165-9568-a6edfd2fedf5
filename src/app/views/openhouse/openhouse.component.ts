import { Component, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { DpDatePickerModule } from 'ng2-date-picker';

import { MetatagsService } from '@/services/v3/metatags/metatags.service';
import { OpenhouseService } from '@/services/v3/openhouse/openhouse.service';

@Component({
  selector: 'view-openhouse',
  templateUrl: './openhouse.component.html'
})

export class OpenhouseComponent implements OnInit {
  public cPage: number = 1;
  public visits: any = [];
  public beginDate: any;
  public endDate: any;

  selectedDate = new Date();
  datePickerConfig = {
      min: new Date().toLocaleDateString(this.translate.currentLang === 'fr' ? 'fr-FR' : 'en-US', {
      day: '2-digit',
      month: '2-digit', 
      year: 'numeric'
    }).split('/').join('/'),
    format: this.translate.currentLang === 'fr' ? 'DD/MM/YYYY' : 'MM/DD/YYYY',
    firstDayOfWeek: this.translate.currentLang === 'fr' ? 'mo' : 'su'
  };

  constructor (
    private openHouseService: OpenhouseService,
    public translate: TranslateService,
    private metatagsService: MetatagsService
  ) {
    this.openHouseService.getOpenhouse().subscribe(data => {
      this.visits = data;
    });

    // Set metas
    this.metatagsService.updateMetatags('openhouse');
  }

  ngOnInit () {
  }

  onPageChange (number: number) {
    this.cPage = number;
  }
}
