import { Component, OnInit } from '@angular/core';

import { InscriptionsService } from '@/services/v3/inscriptions/inscriptions.service';
import { MetatagsService } from '@/services/v3/metatags/metatags.service';
import { HomePageService } from '@/services/v3/home-page-block/home-page-block.service';
import { BlocksService } from '@/services/v3/contentblocks/contentblocks.service';
import { ProgramsService } from '@/services/v3/programs/programs.service';

@Component({
  selector: 'view-home',
  templateUrl: './home.component.html'
})

export class HomeComponent implements OnInit {
  customContent: any;
  properties: any;

  breakpoints: any;

  // Home api block
  public blockTitleHome: string;
  public blockContentHome: string;

  // Evaluation api block
  public blockTitleEvaluation: string;
  public blockContentEvaluation: string;

  // Block vedette
  public programs: {
    image: string;
    title: string;
    text: string;
    link: string;
  }[] = [];

  constructor (
    private homePageService: HomePageService,
    private inscriptionsService: InscriptionsService,
    private metatagsService: MetatagsService,
    private blocksService: BlocksService,
    private programsService: ProgramsService
  ) {
    this.metatagsService.updateMetatags('home');

    // Get banner + block video
    this.homePageService.getHomePage().subscribe(({ data }) => {
      this.customContent = data;
    });

    // Home api block
    this.blocksService.getBlock('bloc-accueil').subscribe(data => {
      this.blockTitleHome = data.title;
      this.blockContentHome = data.text;
    });

    // Evaluation api block
    this.blocksService.getBlock('bloc-evaluation-immobiliere').subscribe(data => {
      this.blockTitleEvaluation = data.title;
      this.blockContentEvaluation = data.text;
    });

    // Programs
    this.programsService.getPrograms().subscribe(({ data }) => {
      this.programs = (data || []).map(item => ({
        image: item.photo,
        title: item.title,
        text: item.description,
        link: item.link
      }));
    });

    this.breakpoints = {
      320: {
        slidesPerView: 1
      },
      768: {
        slidesPerView: 1.4
      },
      1024: {
        slidesPerView: 2.5
      },
      1440: {
        slidesPerView: 3.5
      }
    };
  }

  ngOnInit () {
    const search = { sold: 0, sort: 'newest' };
    // if you need to get featured and random properties
    // { featured: 1, sort: 'rand' }
    this.inscriptionsService.getInscriptions(6, search).subscribe(({ data }) => {
      if (data.length > 0) {
        this.properties = data;
      }
    });
  }
}
