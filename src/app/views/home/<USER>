<lib-header></lib-header>
<div class="main-content" data-scroll>
    <!-- todo might redo -->
    <!-- <lib-search-simple></lib-search-simple> -->

    <!-- Block home -->
    <lib2-section-content
        [title]="blockTitleHome"
        [text]="blockContentHome"
        [button1]="{ 
            text: 'library.cta-broker.button' | translate, 
            link: 'urls.real-estate-agents' | translate, 
            target: '_self', 
            class: '-primary' 
        }"
        [button2]="{ 
            text: 'library.cta-broker.button' | translate, 
            link: 'urls.real-estate-agents' | translate, 
            target: '_self', 
            class: '-secondary' 
        }"
        [image]="{ 
            src: 'assets/images/placeholder/placeholder.jpg', 
            alt: 'library.cta-broker.alt-image' | translate
        }"
        backgroundUrl="assets/images/placeholder/placeholder-bg.jpg"
        class="-space-default"
        layout="-normal"
        [reverse]="false"
    ></lib2-section-content>

    <!-- Estimation Form -->
    <lib2-section-estimation
        [label]="'library.search-sell.label' | translate"
        [title]="'library.search-sell.title' | translate"
        [text]="'library.search-sell.text' | translate"
        [button]="{ 
            text: 'library.search-sell.button' | translate, 
            link: ['urls.real-estate-online-evaluation' | translate],
            class: '-primary' 
        }"
        [image]="{ 
            src: 'assets/images/placeholder/placeholder.jpg', 
            alt: 'library.search-sell.title' | translate
        }"
        class="-space-default"
        [reverse]="false"
    ></lib2-section-estimation>

    <!-- Properties slider -->
    <lib2-section-slider
        [title]="'views.home.section-slider.title' | translate"
        icon="icon-pin"
        [button]="{
            text: 'views.home.section-slider.cta-text' | translate,
            link: ['urls.real-estate-properties' | translate],
            class: '-cta-large',
            target: '_self'
        }"
        class="-space-default"
        [navigation]="true"
        [items]="properties"
        [itemTemplate]="sliderTemplate"
        [loop]="true"
        [autoplay]="false"
        [touch]="true"
        [breakpoints]="breakpoints"
    ></lib2-section-slider>

    <ng-template #sliderTemplate let-item>
        <lib2-property-card [property]="item" class="properties" layout="-hover"></lib2-property-card> 
    </ng-template>

    <!-- immo alert -->
    <lib2-section-content
        [label]="'library.cta-alert.label' | translate"
        [title]="'library.cta-alert.title' | translate"
        [text]="'library.cta-alert.text' | translate"
        [button1]="{ 
            text: 'library.cta-alert.btn' | translate, 
            link: 'urls.real-estate-alert' | translate, 
            target: '_self', 
            class: '-primary' 
        }"
        [image]="{ 
            src: 'assets/images/placeholder/placeholder.jpg', 
            alt: 'library.buy-guide-cta.alt-image' | translate
        }"
        backgroundUrl="assets/images/placeholder/placeholder-bg.jpg"
        class="-space-default"
        layout="-boxed"
        [reverse]="true"
    ></lib2-section-content>

    <!-- Estimation CTA -->
    <!-- <lib2-section-content
        [title]="blockTitleEvaluation | translate"
        [text]="blockContentEvaluation | translate"
        [button1]="{ 
            text: 'library.cta-evaluation.btn' | translate, 
            link: 'urls.real-estate-online-evaluation' | translate, 
            target: '_self', 
            class: '-primary' 
        }"
        backgroundUrl="assets/images/placeholder/cta/banner-2.jpg"
        class="-space-default"
        layout="-small-boxed"
    ></lib2-section-content> -->

    <!-- Custom content -->
    <lib2-section-content
        *ngFor="let section of customContent?.left_right_section"
        [title]="section.title"
        [text]="section.text"
        [button1]="{ 
            text: section.btn_label, 
            link: section.btn_url,
            target: section.target,
            class: '-primary' 
        }"
        [image]="{ 
            src: section.image,
            alt: section.title
        }"
        class=""
        layout="-full-width"
        [reverse]="section.orientation !== 'left'"
    ></lib2-section-content>

    <lib2-sold-map
        class="-custom"
        layout="-default"
    ></lib2-sold-map>

    <!-- Video -->
    <lib2-section-video
        *ngIf="customContent?.section_video_formatted"
        [label]="'views.home.section-video.label' | translate"
        [title]="customContent.section_video_title"
        [text]="customContent.section_video_description"
        [videoUrl]="this.customContent.section_video_formatted"
        class="-space-default"
        layout="-default"
        [reverse]="false"
    ></lib2-section-video>

    <!-- Bloc vedette -->
    <lib2-featured-content
        [sectionTitle]="'views.home.featured-content.title' | translate"
        [cards]="programs"
        class="-space-default"
    ></lib2-featured-content>

    <lib2-section-blog
        [sectionTitle]="'library.blog-list-1.title' | translate"
        [button]="{
            text: 'library.blog-list-1.all-posts' | translate,
            link: ['urls.real-estate-blog' | translate],
            class: '-cta-large',
            target: '_self'
        }"
        class="-space-default"
        itemLayout="-default"
        layout="-default"
        nbPosts="3"
    ></lib2-section-blog>

    <!-- Random CTA -->
    <lib2-cta-box
        [title]="'views.home.cta-box.title' | translate"
        [text]="'views.home.cta-box.text' | translate"
        [button]="{ 
            link: 'urls.contact' | translate,
            text: 'views.home.cta-box.cta-text' | translate,
            target: '_self', 
            class: '-primary' 
        }"
        tag="h2"
        [background]="false"
        layout="-column"
        class="-space-default"
    ></lib2-cta-box>

    <!-- Testimonials -->
    <!-- <lib2-section-testimony
        [label]="'library.reviews.label' | translate"
        [title]="'library.reviews.title' | translate"
        layout="-default"
        testimonialsProvider="google"
        class="-space-default"
    ></lib2-section-testimony> -->

    
<!-- todo might use section-cta or whatsoever to have a more simple cta maybe -->
<!-- Plain full-width color CTA with title/text -->
    <!-- <lib-cta-evaluation-small></lib-cta-evaluation-small> -->
    <!-- <lib-cta-alert-small></lib-cta-alert-small> -->


<lib-footer></lib-footer>

</div>
