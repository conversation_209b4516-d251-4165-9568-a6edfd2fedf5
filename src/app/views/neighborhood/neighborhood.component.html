<lib-header></lib-header>
<lib-neighborhood-hero [neighborhood]="neighborhood"></lib-neighborhood-hero>
<lib-neighborhood-highlights [neighborhood]="neighborhood"></lib-neighborhood-highlights>
<lib-neighborhood-map-2 [neighborhood]="neighborhood"></lib-neighborhood-map-2>
<lib-neighborhood-demographics [neighborhood]="neighborhood" [formatTitle]="formatTitle"></lib-neighborhood-demographics>
<lib-neighborhood-avgcosts [neighborhood]="neighborhood" [formatTitle]="formatTitle"></lib-neighborhood-avgcosts>
<lib-neighborhood-photos [neighborhood]="neighborhood" ></lib-neighborhood-photos>
<lib-centered-cta [url]="['urls.neighborhoods' | translate ]" [label]="'views.neighborhood.cta' | translate"></lib-centered-cta>
<!-- <lib-neighborhood-list [neighborhood]="neighborhood" [properties]="properties" [formatTitle]="formatTitle"></lib-neighborhood-list> -->

<!-- Properties slider -->
<div *ngIf="properties?.length > 0">
  <lib2-section-slider
      [title]="'views.home.section-slider.title' | translate"
      icon="icon-pin"
      [button]="{
          text: 'library.properties-list.all' | translate,
          link: ['urls.search-properties' | translate],
          class: '-cta-large',
          target: '_self'
      }"
      class="-space-default"
      [navigation]="true"
      [items]="properties"
      [itemTemplate]="sliderTemplate"
      [loop]="true"
      [autoplay]="false"
      [touch]="true"
      [breakpoints]="breakpoints"
  ></lib2-section-slider>
  
  <ng-template #sliderTemplate let-item>
      <lib2-property-card [property]="item" class="properties" [wideCard]="true" layout="-hover"></lib2-property-card> 
  </ng-template>
</div>

<lib-neighborhood-navigation [previousNav]="previousNav" [nextNav]="nextNav"></lib-neighborhood-navigation>
<lib-footer></lib-footer>