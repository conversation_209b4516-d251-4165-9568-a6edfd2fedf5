<lib-header></lib-header>

<lib2-section-hero
    [label]="'views.buy.label' | translate"
    [title]="'views.buy.title' | translate"
    [image]="{ 
        src: 'assets/images/placeholder/banner/ban-devenirproprietaire.jpg',
        alt: 'views.buy.title' | translate
    }"
    class="buy-hero"
    layout="-full"
></lib2-section-hero>

<!-- todo redo -->
<lib-buy-sheet></lib-buy-sheet>

<lib2-section-content
    [title]="'library.buy-guide-cta.title' | translate"
    [text]="'library.buy-guide-cta.description' | translate"
    [button1]="{ 
        text: 'library.buy-guide-cta.download' | translate, 
        link: 'library.buy-guide-cta.download-link' | translate, 
        target: '_blank', 
        class: '-primary' 
    }"
    [image]="{
        src: 'assets/images/placeholder/placeholder.jpg',
        alt: 'library.buy-guide-cta.alt-image' | translate
    }"
    class="-space-default"
    layout="-boxed"
    [reverse]="true"
></lib2-section-content>

<!-- todo redo -->
<ng-container *ngIf="properties && properties.length > 0">
    <lib-properties-featured [properties]="properties"></lib-properties-featured>
</ng-container>

<lib2-section-blog
    [sectionTitle]="'library.blog-list-1.title' | translate"
    [button]="{
        text: 'library.blog-list-1.all-posts' | translate,
        link: ['urls.real-estate-blog' | translate],
        class: '-cta-large',
        target: '_self'
    }"
    class="-space-default"
    itemLayout="-default"
    layout="-default"
    nbPosts="3"
    [category]="'views.buy.blog-category' | translate"
></lib2-section-blog>

<lib2-section-testimony
    [label]="'library.reviews.label' | translate"
    [title]="'library.reviews.title' | translate"
    layout="-slider"
    testimonialsProvider="google"
    class="-space-default"
></lib2-section-testimony>

<!-- Todo might redo -->
<!-- <lib-search-buy></lib-search-buy> -->
<!-- <lib-openhouses-list [propertiesOpenHouse]="propertiesOpenHouse"></lib-openhouses-list> -->
<!-- <lib-alert-cta-1></lib-alert-cta-1> -->
<lib-footer></lib-footer>
