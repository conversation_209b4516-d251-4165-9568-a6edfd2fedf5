.blog-card {
    cursor: pointer;

    .image {
        border-radius: 24px;
        overflow: hidden;
        position: relative;
        aspect-ratio: 4/3;

        &:before, &:after {
            content: '';
            position: absolute;
            left: 50%;
            top: 50%;
            width: 40px;
            height: 6px;
            background: white;
            border-radius: 100px;
            opacity: 0;
            transform: translate(-50%, -50%) scale(0.8);
            transition: $transition;
            z-index: 9;
            pointer-events: none;
        }
        &:after {
            transform: translate(-50%, -50%) rotate(90deg) scale(0.8);
        }

        .filter {
            position: absolute;
            inset: 0;
            z-index: 1;
            background: rgba(0,0,0,0.20);
            pointer-events: none;
            transition: $transition;
            opacity: 0;
        }

        img {
            @include img();
        }

    }

    &:hover {
        .image {
            .filter {
                opacity: 1;
            }

            &:before, &:after {
                opacity: 1;
            }
        }
    }
    
    .article-info {
        margin-top: 25px;
        .title {
            margin: 25px 0;
        }

        .date {
            margin: 25px 0;
        }

        .description {

        }
    }

    @media (max-width: $tablet-lg) {
        display: flex;
        gap: 20px;

        .image {
            border-radius: 24px;
            flex: 0 0 33%;
            aspect-ratio: 1/1;
        }

        
        .article-info {
            margin-top: 0;
            
            .title {
                margin-top: 0;
                margin-bottom: 15px;
            }
    
            .date, .description {
                margin: 15px 0;
            }
        }
    }

    @media (max-width: $mobile-lg) {
        display: flex !important;
        flex-direction: column; 

        .image {
            aspect-ratio: 16/10 !important;
        }
    }

    // Layouts
    &.-inline {

        @media (min-width: $mobile-lg) {
            // Same as .grid but cant extend it
            display: grid;
            grid-template-columns: repeat($base-column-nb, 1fr);
            grid-gap: var(--grid-gutter);
            gap: var(--grid-gutter);

            padding: rem(64px) 0;
            border-top: 1px solid $light-grey;
    
            .image {
                grid-column: 1/5;
                aspect-ratio: 10 / 9;
            }
    
            .article-info {
                margin-top: 0;
                grid-column: 5/13;
    
                .title {
                    margin-top: 0;
                }
            }
        }

        @media (max-width: $tablet) {
            padding: rem(40px) 0;
        }
    }
}