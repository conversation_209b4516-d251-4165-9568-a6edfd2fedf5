.property-card {
    position: relative;

    // Labels and icons
    .top-info {
        display: flex;
        justify-content: space-between;
        position: absolute;
        z-index: 99;
        width: 100%;
        padding: rem(16px);


        .labels {
            .label {
                padding: rem(4px) rem(16px);
                border-radius: 3px;
                font-size: var(--font-size-cta-small);
                line-height: var(--line-height-cta-small);
                font-weight: 600;
                color: $white;
                background: $primary-color;

                &.-label-rental-possible {

                }

                &.-label-sold {

                }

                &.-label-new {

                }

                &.-label-view {

                }
            }
        }

        .icons {
            display: flex;
            justify-content: flex-end;

            .visit-video, .visit-360 {
                background-color: $primary-color;
                border-radius: 100%;
                width: rem(32px);
                height: rem(32px);
                @include flex();

                span::before {
                    color: $white;
                    font-size: rem(20px);
                }
            }
        }
    }

    // Image
    .image {
        aspect-ratio: 1/1;
        position: relative;
        border-radius: 16px;
        overflow: hidden;

        img {
            @include img();
        }
    }

    // Bottom infos
    .property-info {
        padding: rem(20px) 0;

        .bloc-head {
            display: flex;
            align-items: center;

            .price, .type {
                line-height: 1;
            }

            .price {
                font-family: var(--font-title);
                font-size: var(--font-size-price-big);
                padding-right: rem(16px) !important;
                border-right: 1px solid #636363;
                white-space: nowrap;
                
                .small-tps {
                    font-size: rem(10px);
                }
            }
            .type {
                padding-left: rem(16px) !important;
                font-size: rem(14px);
                font-weight: 700;
            }
        }

        .more-info {

            .address {
                padding: rem(16px) 0;

                .display-address, .location {
                    font-size: var(--font-size-small);
                    line-height: var(--line-height-small);
                    font-weight: 600;
                    color: $paragraph;
                }
            }

            .numbers {
                display: flex;
                gap: 20px;

                .ctn {
                    display: flex;
                    align-items: center;
                    gap: 6px;
                    color: $paragraph;

                    p {
                        font-weight: 600;
                    }
                }
            }
        }
    }

    .filter {
        position: absolute;
        height: 100%;
        width: 100%;
        left: 0;
        right: 0;
        top: 0;
        bottom:0;
        transition: $transition;
        transition-duration: 0.3s;
        background: linear-gradient(0deg, rgba(0, 8, 29, 0.40)) 0%, rgba(0, 8, 29, 0.40);

        @media (max-width: 992px) {
            opacity: 0;
            background-color: rgba(0,0,0,0.3);
        }
    }

    &:hover {
		.filter {
			background-color: rgba(0,0,0,0.3);
			opacity: 1;
		}
	}

    &.-wide {
        .image {
            aspect-ratio: 34/25; // 408x300px ratio
        }
    }

    // Layout
    &.-hover {

        .gradiant {
            position: absolute;
            height: 50%;
            width: 100%;
            left: 0;
            right: 0;
            bottom:0;
            top: 50%;
            background: linear-gradient(180deg, rgba(0, 0, 0, 0.00) 0%, rgba(0, 0, 0, 0.80) 100%);
        }

        .property-info {
            width: 100%;
            position: absolute;
            bottom: 0;
            padding-left: rem(16px);
            padding-right: rem(16px);
            z-index: 99;

            .more-info {
                height: 0;
                overflow: hidden;
                transition: $transition;
            }
        }

        p, a, span, i {
            color: $white !important;
        }
    }
}