.sold-map {
    .top {
        padding: rem(70px);
        
        .title {
            text-align: center;
        }
    }

    #map {
        background-image: url('/assets/images/placeholder/map-placeholder.webp');
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        min-height: rem(700px);
    }

    @media (max-width: $tablet) {

    }

    // Layouts 
    &.-default-boxed {
        #map {
            margin: 0 var(--container-margin);
            border-radius: 32px;
            overflow: hidden;
        }

        @media (max-width: $tablet) {

        }
    }

    &.-horizontal {
        @extend .grid;
        gap: 0;

        > :first-child {
            grid-column: 1/4;
        }

        > :last-child {
            grid-column: 4/13;
        }

        .top {
            display: flex;
            align-items: center;
            background-color: var(--primary-color);
            padding: rem(60px);
            
            .title {
                text-align: start;
                color: $white;
            }
        }

        @media (max-width: $tablet) {

        }
    }
}