.section-estimation {
    @extend .grid;
    align-items: center;

    > :first-child {
        grid-column: 1/7;
    }
    > :last-child {
        grid-column: 8/13;
    }

    .content {

        .label {

        }

        .title {
            margin: 40px 0 50px 0;
        }

        .text {
            margin: 40px 0;
        }

        .form-ctn {
            width: 100%;

            .input {
                position: relative;
                margin: 40px 0;

                i {
                    position: absolute;
                    top: 50%;
                    right: 20px;
                    transform: translateY(-50%);
                    color: var(--primary-color);
                    font-size: 25px;
                }
            }
            
            input {
                width: 100%;
            }
            .btn {
    
            }

        }

    }

    .image {
        border-radius: 32px;
        overflow: hidden;

        img {
            @include img();
        }
    }

    @media (max-width: $tablet-lg) {

        > :first-child {
            grid-column: 1/13 !important;
        }
        > :last-child {
            grid-column: 1/13 !important;
        }

        .content {
            text-align: center;
        }

        .btn {
            margin: 0 auto
        }
    }

    // Reverse
    &.-reverse {
        > :first-child {
            grid-column: 7/13;
            grid-row: 1/1;
        }
        > :last-child {
            grid-column: 1/6;
            grid-row: 1/1;
        }

         @media (max-width: $tablet-lg) {
            > :first-child {
                grid-column: 1/13 !important;
                grid-row: 1/2;
            }
            > :last-child {
                grid-column: 1/13 !important;
                grid-row: 2/3;
            }
        }
    }

    // No Image
    &.-no-image {
        flex-direction: column;

         @media (max-width: $tablet) {

        }
    }
}