.cta-box {

    .inner {
        padding: rem(64px);
        display: flex;
        align-items: center;
        gap: 32px;
        border-radius: 32px;
        background-color: $background;
    }

    .content {
        width: 100%;

        .title {
            @extend .-large;
            margin-top: 0;
            margin-bottom: 0;
        }
    }

    .btn {
        min-width: 20%;
    }

    @media (max-width: $tablet-lg) {
        .inner {
            padding: rem(30px);
        }
    }

    @media (max-width: $mobile-lg) {
        .inner {
            padding: 0;
        }
    }

    // Reverse
    &.-reverse {
        .inner { flex-direction: row-reverse };

        .content {
            text-align: end;
        }

        @media (max-width: $tablet) {
        
        }
    }

    // No background
    &.-no-background {
        .inner {
            background-color: transparent;
        }

        @media (max-width: $tablet) {
        
        }
    }

    // Layouts
    &.-column {
        .inner { flex-direction: column };

        .content {
            text-align: center;
        }

        @media (max-width: $tablet) {
        
        }
    }
}