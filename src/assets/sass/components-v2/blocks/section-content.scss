.section-content {
    
    .content {
        display: flex;
        flex-direction: column;
        justify-content: center;

        .title {
            // margin: 40px 0 24px 0;
        }

        .text {
            // margin: 40px 0;
        }
    }

    .image {
        overflow: hidden;
        margin-right: grid-space(math.div(1,12), 0);
        
        img {
            @include img();
        }
    }

    .btn-ctn {
        margin-top: var(--scale-xs);
        flex-wrap: wrap;
        gap: rem(8px);
    }

    @media (max-width: $tablet-lg) {
        .image {
            max-height: 60vh;
        }

        .inner {
            gap: 30px;
        }

        .content {
            .title {
                // margin: 24px 0;
            }

            .text {
                // margin: 24px 0;
            }
        }
    }

    // Reverse
    &.-reverse {
        .inner  > *:first-child {
            order: 2;
        }
        .inner > *:last-child {
            order: 1;

            margin-left: 0;
            margin-right: grid-space(math.div(1,12), 0);
        }

        @media (max-width: $tablet-lg) {
            .inner  > *:first-child {
                order: 1;
            }

            .inner > *:last-child {
                order: 2;
            }
        }
    }


    // Layouts
    &.-default {
        @media (max-width: $tablet) {
        
        }
    }


    &.-boxed {
        
        .inner { 
            display: flex;
            gap: grid-space(math.div(1,12), 0);
            padding: rem(64px) grid-space(math.div(1,12), 0); 
            background-color: $background;
            border-radius: 40px;

            .image {
                width: 40%;
                aspect-ratio: 1/1;
            }

            .content {
                width: 60%;
                margin: 0;
            }
        }

        // No image
        &.-no-image {
            text-align: center;

            .btn-ctn {
                justify-content: center;
            }

            .content {
                width: 100%;
            }
        }

        @media (max-width: $tablet-lg) {
            .inner {
                flex-direction: column;
                padding: 32px;
                gap: 32px;

                .image {
                    width: 100%;
                }

                .content {
                    width: 100%;
                    margin: 0;
                }
            }
        }

        @media (max-width: $mobile-lg) {
            .inner {
                padding: 20px;
            }
        }
    }

    &.-small-boxed {
        @extend .-boxed;
        
        .inner {
            @extend .container;
            @extend .-narrow;
        }

        max-height: rem(800px);
        background-repeat: no-repeat;
        background-position: center;
        background-size: cover;

        @media (max-width: $tablet) {
        
        }
    }

    &.-full-width {
        padding: 0;

        .inner {
            gap: 0;
        }

        .image {
            border-radius: 0;
        }

        .inner > *:last-child {
            margin: 0;
            @extend .container;
        }

        @media (max-width: $tablet-lg) {

            .inner {
                gap: 30px;
            }
        }
    }
}