.section-blog {
    .top {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: rem(70px);

        .title-ctn {
            display: flex;
            align-items: center;
            gap: 24px;

            .icon {
                font-size: 40px;
                color: var(--color-title);
            }
            .title {
                
            }
        }
        

        .cta {

        }
    }

    .blogs-ctn {

    }

    @media (max-width: $tablet-lg) {

        .top {
            flex-direction: column;
            gap: 20px;
            align-items: flex-start;
            margin-bottom: rem(40px);
        }
    }

    // Layouts
    &.-featured {

        .blogs-ctn .column-list {
            padding-left: grid-space(math.div(1, 12), 0);
        }
        .blog-card.-inline {
            padding: rem(40px) 0;

        }

        @media (max-width: $tablet-lg) {

            .blogs-ctn {
                gap: 0;

                .column-list {
                    padding-left: 0;
                }
            }

            // Exactly the same as .-inline but can't extend it
            .blog-card:first-of-type {
                // Same as .grid but cant extend it
                display: grid;
                grid-template-columns: repeat($base-column-nb, 1fr);
                grid-gap: var(--grid-gutter);
                gap: var(--grid-gutter);

                padding: rem(40px) 0;
                border-top: 1px solid $light-grey;

                .image {
                    grid-column: 1/5;
                    aspect-ratio: 10 / 9;
                }

                .article-info {
                    margin-top: 0;
                    grid-column: 5/13;

                    .title {
                        margin-top: 0;
                    }
                }
            }
        }
    }
}