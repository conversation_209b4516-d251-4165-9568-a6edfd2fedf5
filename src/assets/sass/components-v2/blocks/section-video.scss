.section-video {
    display: flex;
    gap: 40px;

    .content {
         flex-basis: 35%;


        .label {

        }
        
        .title {

        }
        
        .text {

        }
    }

    .video {
        border-radius: 24px;
        overflow: hidden;
        aspect-ratio: 16 / 9;
        flex-basis: 65%;

        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
    }

    @media (max-width: $tablet) {
        flex-direction: column;
    }

    // Reverse
    &.-reverse {
        flex-direction: row-reverse;

        @media (max-width: $tablet) {

        }
    }

    // Layouts
    &.-column {
        flex-direction: column;
        @extend .-small;
        
        .content {
            text-align: center;
        }

        @media (max-width: $tablet) {
            
        }
    }
}