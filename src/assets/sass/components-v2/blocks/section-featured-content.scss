.section-featured-content {
    .top {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: rem(70px);
        
        .title {
            
        }

        .cta {

        }
    }

    .cards-ctn {
        display: flex;
        gap: var(--grid-gutter);

        .card {
            flex-basis: 100%;
            cursor: pointer;
            
            .image {
                position: relative;
                border-radius: 32px;
                overflow: hidden;
                aspect-ratio: 1/1;

                img {
                    @include img();
                }

                &:before, &:after {
                    content: '';
                    position: absolute;
                    left: 50%;
                    top: 50%;
                    width: 40px;
                    height: 6px;
                    background: white;
                    border-radius: 100px;
                    opacity: 0;
                    transform: translate(-50%, -50%) scale(0.8);
                    transition: $transition;
                    z-index: 9;
                    pointer-events: none;
                }
                &:after {
                    transform: translate(-50%, -50%) rotate(90deg) scale(0.8);
                }

                .filter {
                    position: absolute;
                    inset: 0;
                    z-index: 1;
                    background: rgba(0,0,0,0.20);
                    pointer-events: none;
                    transition: $transition;
                    opacity: 0;
                }
            }

            .title {
                margin: 30px 0;
            }

            .text {
                color: $paragraph;
            }

            &:hover {
                .image {
                    .filter {
                        opacity: 1;
                    }

                    &:before, &:after {
                        opacity: 1;
                    }
                }
            }
        }
    }

    @media (max-width: $tablet-lg) {

        .top {
            flex-direction: column;
            align-items: flex-start;
            gap: 20px;
            margin-bottom: rem(40px);
        }

        .cards-ctn {
            flex-direction: column;

            .card {
                display: flex;
                gap: 20px;

                .image {
                    border-radius: 24px;
                    flex: 0 0 33%;
                }

                .title {
                    margin-top: 0;
                    margin-bottom: 15px;
                }
            }
        }
    }

    @media (max-width: $mobile-lg) {
        .cards-ctn {
            .card { 
                flex-direction: column; 

                .image {
                    aspect-ratio: 16/10 !important;
                }
            }

            gap: 50px;
        }
    }

    // No cta top 
    &.-no-cta {
        .top {
            justify-content: center;
        }
    }

    // Only two item
    &.-two {
        .image {
            aspect-ratio: 16/10 !important;
        }

        @media (max-width: $tablet-lg) {
            .image {
                aspect-ratio: 1/1 !important;
            }
        }

        @media (max-width: $mobile-lg) {
            .image {
                aspect-ratio: 16/10 !important;
            }
        }
    }
}