.home-hero {
    width: 100vw;
    height: auto;
    max-height: 90vh;
    min-height: 90vh;
    aspect-ratio: 16 / 9;
    position: relative;
    overflow: hidden;

    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;


    .video-wrapper {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 0;

        .video {
            width: 100%;
            height: auto;
            aspect-ratio: 16/9;
            position: relative;
            z-index: 10;
            top: 0;
            left: 0;
            object-fit: contain;
        }

        .thumbnail {
            width: 100%;
            height: auto;
            position: absolute;
            top: 0;
            left: 0;
            object-fit: cover;
            z-index: 1;
            background-color: $white;
        }

        .static {
            width: 100%;
            height: 100%;
            position: relative;
            object-fit: cover;
            z-index: 50;
            top: 0;
            left: 0;
        }
    }

    .content {
        z-index: 100;
        margin: 0 var(--container-margin);

        @media (min-width: $desktop-lg) {
            max-width: grid-space(math.div(8,12), 2);
        }

        .title {
            text-align: center;
            color: $white;
        }

        .text {
            text-align: center;
            color: $white;
        }

        .btn-ctn {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: rem(12px);

            .btn {

            }
        }
    }

    @media (max-width: $desktop-sm) {
        max-height: 70vh;
        min-height: 70vh;

        .content .title {
            font-size: rem(32px);
            line-height: rem(40px);
        }
    }

    @media (max-width: $tablet) {
        max-height: 60vh;
        min-height: 60vh;

        &, .video, .thumbnail {
            aspect-ratio: unset !important;
        }

        .video, .thumbnail {
            height: 100% !important;
        }

        .video {
            transform: scale(1.9);
        }
    }

    @media (max-width: $mobile-lg) {
        max-height: 50vh;
        min-height: 50vh;

        .video {
            transform: scale(1.6);
        }
    }

    // Has a video
    &.-has-video {

    }

    // Static image
    &.-static-image {
        .video, .thumbnail {
            display: none;
        }
    }
}