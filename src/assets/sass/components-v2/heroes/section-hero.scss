.section-hero {
    .content {
        .label {

        }

        .title {

        }

        .text {

        }
    }

    .image {
        img {
            @include img();
        }
    }

    @media (max-width: $tablet) {

    }

    // Layouts
    &.-default {
        max-height: 80vh;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        .content {
            position: relative;
            padding: rem(32px);
            background: $white;
            max-width: grid-space(math.div(8,12), 1);
            text-align: center;
            z-index: 9;

            display: flex;
            flex-direction: column;
            gap: rem(32px);
            
            .title {
                margin-top: 0;
                margin-bottom: 0;
            }
        }

        .image {
            position: absolute;
            width: 100%;
            height: 100%;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            filter: grayscale(50%);
        }

        @media (max-width: $tablet) {
            align-items: flex-start;

            .content {
                max-width: 90%;

                .title {
                    text-align: start;
                }
            }
        }
    }

    &.-overlay {
        max-height: 80vh;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        align-items: flex-start;

        .image {
            position: absolute;
            width: 100%;
            height: 100%;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);

            &::after {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: linear-gradient(0deg, rgba(0, 0, 0, 0.30) 0%, rgba(0, 0, 0, 0.30) 100%);
            }
        }

        .content {
            z-index: 9;
            color: $white;
            margin-left: rs(40px, 100px);
            margin-bottom: rs(40px, 100px);

            .label {
                margin-bottom: rem(32px);
            }


            .title {
                color: $white;
                max-width: grid-space(math.div(8,12), 1);
                margin: 0;
                overflow-wrap: anywhere;
            }
        }

        @media (max-width: $tablet) {
            .content {
                margin-left: var(--container-margin);
                margin-right: var(--container-margin);

                .title {
                    max-width: unset;
                }
            }
        }
    }

    &.-full {
        // height: 100%;

        .content {
            padding-top: rem(120px);
            margin-left: var(--container-margin);
            margin-right: var(--container-margin);
            margin-bottom: rem(100px);
            text-align: center;

            .title {
                max-width: grid-space(math.div(8,12), 1);
                margin: 0 auto;
            }
        }

        .image {
            img {
                max-height: 70vh;
            }
        }

        @media (max-width: $tablet) {
            .content {
                padding-top: rem(80px);
                margin-bottom: rem(40px);

                .title {
                    max-width: unset;
                }
            }
        }
    }

    &.-contained {

        margin-left: var(--container-margin);
        margin-right: var(--container-margin);
        
        .content {
            padding-top: rem(120px);
            margin-bottom: rem(80px);
            text-align: center;

            .title {
                max-width: grid-space(math.div(8,12), 1);
                margin: 0 auto;
            }
        }
        
        .image {
            border-radius: 32px;
            overflow: hidden;
        }

        @media (max-width: $tablet) {
            .content {
                padding-top: rem(80px);
                margin-bottom: rem(40px);

                .title {
                    max-width: unset;
                }
            }
        }

        &.-has-text {
            text-align: start;

            .content {
                text-align: start;

                .title {
                    margin-left: 0;
                }
            }

            .ctn {
                display: grid;
                grid-template-columns: repeat(12, 1fr);
                gap: var(--grid-gutter);

                // first child
                & > *:first-child {
                    grid-column: 1 / 9;
                }

                // second child
                & > *:last-child {
                    grid-column: 9 / -1;
                }

                .text {
                    margin-top: 0;
                }
            }

            @media (max-width: $tablet) {
                .content {
                    // text-align: center;
                }
                
                .ctn {
                    display: flex;
                    flex-direction: column-reverse;
                }
            }
        }
    }
}