.sortable-list {
	@include flex();
	gap: rem(24px);
	margin: rem(64px) 0;

	.input-tab {
		display: none;
	}

	input:checked + label {
		color: var(--primary-color);

		&:after {
			width: 100%;
		}
	}

	input:hover + label {
		color: var(--primary-color);

		&:after {
			width: 100%;
		}
	}

	label {
		color: var(--color-text);
		text-align: center;
		font-weight: 600;
		position: relative;
		padding: 7px 0px;
		overflow: inherit;
		display: inline-block;
		line-height: initial;

		&:hover{
			cursor: pointer;
		}

		&:after {
			content: "";
			position: absolute;
			bottom: 0;
			left: 0;
			background: var(--primary-color);
			height: 2px;
			display: block;
			width: 0px;
			transition: all 0.5s cubic-bezier(0.19, 1, 0.22, 1);
		}
	}
}