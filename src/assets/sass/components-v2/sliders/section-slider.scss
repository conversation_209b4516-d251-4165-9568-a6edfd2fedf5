.section-slider {
    overflow-x: hidden;
    
    .top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: rem(70px);
        gap: 20px;

        .left, .right {
            display: flex;
            align-items: center;
            gap: 24px;
        }

        .left {
            .icon {
                font-size: 40px;
                color: var(--color-title);
            }

            .title {

            }
        }

        .right {
            .cta {

            }

            .btn-ctn {
                display: flex;
            }
        }
    }

    .swiper-container {
        .swiper-wrapper {
            .swiper-slide {}
        }
    }

    @media (max-width: $tablet-lg) {

        .top {
            flex-direction: column;
            align-items: flex-start;
            margin-bottom: rem(30px);
        }
    }
}