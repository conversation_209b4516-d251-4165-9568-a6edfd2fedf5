.section-testimony {

    .content {
        .label {

        }

        .title {

        }
    }

    .swiper-container {
        overflow-x: hidden;
        
        .inner {

        }

        .btn-ctn {

        }

        .swiper-pagination {
            position: relative;
        }
    }

    // Slide
    .swiper-slide {
        display: flex;
        flex-direction: column;
        gap: rem(35px);

        .info-ctn {
            display: flex;
            align-items: center;
            gap: 20px;
            
            .image {
                min-height: 80px;
                max-height: 80px;
                min-width: 80px;
                max-width: 80px;
                border-radius: 100%;
                overflow: hidden;
                
                img {
                    @include img();
                }
            }
            
            .info {
                display: flex;
                flex-wrap: wrap;
                gap: 10px;
                
                .author {
                    @extend .-h4;
                    flex-basis: 100%;
                }
                .star {
                    flex-basis: 50%;
                }

                .date {
                    flex-basis: 100%;
                    font-size: rem(14px);
                    color: #777;
                    text-wrap: nowrap;
                }
            }

            .icon-star {

                &:before { color: #ffcc00; }
                &:not(.checked) {
                    &:before { color: #ccc; }
                }
            }
        }

        .text {
            margin: 0 auto;

            // Truncate text to 8 lines
            display: -webkit-box;
            -webkit-line-clamp: 8;
            -webkit-box-orient: vertical;
            overflow: hidden;
            max-width: grid-space(math.div(8,12), 1);

            & > p:first-child {
                margin: 0;
            }
        }
    }

    @media (max-width: $tablet) {
        .swiper-slide {
            .info-ctn {
                .info {
                    gap: 4px 8px;
                }
            }
        }

        .text {
            width: unset;
        }
    }


    // Layouts
    &.-default {
        text-align: center;
        position: relative;
        background: $background;
        
        .swiper-wrapper {
            margin: rem(70px) 0;
            align-items: center;
        }

        .info-ctn {
            display: none;
        }

        .btn-ctn {
            position: absolute;
            display: flex;
            justify-content: space-between;
            width: 100%;
            left: 0;
            top: 50%;
            padding: 0 40px;
        }

        .swiper-slide {
            .text, .text * {
                @extend .--testimony;
                font-style: italic;
            }

            .author {
                font-weight: 600;
                color: $black;
            }
        }

        @media (max-width: $tablet) {
        
        }
    }

    &.-carousel {
        background: $background;
        position: relative;

        .content {
            text-align: center;
        }

        .swiper-wrapper {
            margin: rem(100px) 0;
        }

        .swiper-slide {
            background-color: $white;
            border-radius: 16px;
            padding: rem(30px);

            .info-ctn {

                .info {
                    .author {
                        @extend .-h4;
                        flex-basis: 100%;
                    }
                    .star {
                        flex-basis: 50%;
                    }

                    .date {
                        flex-basis: 50%;
                        font-size: rs(14, 14px);
                        color: #777;
                        text-wrap: nowrap;
                    }
                }
            }
        }

        .btn-ctn {
            position: absolute;
            display: flex;
            justify-content: space-between;
            width: 100%;
            left: 0;
            top: 50%;
            padding: 0 40px;
        }

        @media (max-width: $tablet) {
            .swiper-wrapper {
                margin: rem(40px) 0;
            }
        }
    }

    &.-slider {
        @extend .grid;
        @extend .-space-lg;

        .content {
            grid-column: 1/7;

            .title {
                max-width: grid-space(math.div(5,12), 1);
            }

        }

        .swiper-container {
            grid-column: 7/13;

            .swiper-wrapper {
                height: fit-content;
            }
        }

        .swiper-slide {
            flex-direction: column-reverse;
            justify-content: flex-end;

            .info-ctn {
                .info {
                    .author {
                        @extend .-cta-large;
                    }
                }
            }

            .text {
                margin: 0;
            }
        }

        .swiper-pagination {
            display: none;
        }

        .btn-ctn {
            margin-top: 80px;
            display: flex;
            justify-content: flex-end;
            gap: 8px;

            .swiper-btn {
                width: 50px;
                height: 50px;
            }
        }

        @media (max-width: $tablet) {
            .content {
                grid-column: 1/13;

                .title {
                    max-width: 100%;
                }
            }

            .swiper-container {
                grid-column: 1/13;
            }

            .btn-ctn {
                margin-top: 50px;
            }
        }
    }
}