# 🎨 Composants - Styles  

Ce dossier regroupe l’ensemble des **styles** liés à la refonte des nouveaux composants.  

Les styles doivent correspondre aux composants situés dans :  
```
/src/app/library-v2/* 
```

## 📂 Structure des styles  

Les styles des composants sont organisés en différents dossiers selon leur type :  
- **Hero** (`/hero`)  
- **Slider** (`/slider`)  
- **Card** (`/card`)  
- Et ainsi de suite...  

⚠️ **Les sous-dossiers doivent correspondre aux sous-dossiers des composants Angular.**  

### 📁 Exemple d’arborescence  
```shell
├──/src/sass/components-v2/
│   │── hero/
│   │──slider/
│   │──card/
```

---

## ✅ Règles à suivre  

### 📌 1. Correspondance avec les composants  
- Chaque fichier ajouté doit **correspondre à un composant** présent dans `library-v2`.  

### 📌 2. Nommage des fichiers  
- Le nom du fichier **doit correspondre** au nom du composant HTML avec le suffixe `-cpn.scss`.  
- **Exemple :**  
```scss
- team-hero-cpn.scss
- slider-gallery-cpn.scss
- card-product-cpn.scss
```