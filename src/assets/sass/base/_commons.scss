// RADIUS
.-r-small {
  border-radius: var(--radius-small);
}

.-r-normal {
  border-radius: var(--radius-medium);
}

.-r-large {
  border-radius: var(--radius-large);
}

// SPACING
.-space-sm {
  padding-top: var(--padding-section-vertical-sm);
  padding-bottom: var(--padding-section-vertical-sm);
}

.-space-default {
  padding-top: var(--padding-section-vertical-default);
  padding-bottom: var(--padding-section-vertical-default);
}

.-space-lg {
  padding-top: var(--padding-section-vertical-lg);
  padding-bottom: var(--padding-section-vertical-lg);
}



// Slider BTN
.btn-ctn {
	z-index: 9;
	gap: 10px;

	.swiper-btn {
		@include size(40px);
		@include flex();
		background: $primary-color;
		border-radius: 100%;
		transition: $transition;
		cursor: pointer;

		i {
			color: $white;
			font-size: rem(18px);
		}

		&:hover {
			background: darken($primary-color, 10%);
		}
	}
}

// Default CTA
.cta{
  display: flex;
  align-items: center;
  gap: 12px;
  
  &.-cta-large, &.-cta-small {
    &:after {
      content: "\e90a";
      font-family: 'icomoon' !important;
      font-size: rem(18px);
    }
  }
}


.list-custom{
	padding-left: 0px;
	padding-bottom: 30px;
	list-style: none;

	li {
		position: relative;
		padding: 24px 0px 24px 40px;
		font-family: var(--font-special);
		border-bottom: 1px solid var(--color-border);
		&:before {
			color: var(--primary-color);
			font-family: 'icomoon';
			speak: none;
			font-style: normal;
			font-weight: normal;
			font-variant: normal;
			text-transform: none;
			line-height: 1;
			-webkit-font-smoothing: antialiased;
			-moz-osx-font-smoothing: grayscale;
			content: "\e90d";
			position: absolute;
			left: 0;
			top: 28px;
		}
	}
}






// OLD TO REWORK
/* List */
.page-description, .-page-description{
	ul{
		@extend .list-custom;
	}
}

.list {
	padding-left: 0px;
	padding-bottom: 30px;

	li {
		position: relative;
		padding: 16px 0px 16px 40px;
		font-family: var(--font-special);
		border-bottom: 1px solid rgba($color: $black, $alpha: 0.2);

		&:before {
			color: var(--primary-color);
			font-family: 'icomoon';
			speak: none;
			font-style: normal;
			font-weight: normal;
			font-variant: normal;
			text-transform: none;
			line-height: 1;
			-webkit-font-smoothing: antialiased;
			-moz-osx-font-smoothing: grayscale;
			content: "\e90d";
			position: absolute;
			left: 0;
			top: 22px;
		}
	}
}

// /*
// 	Loading
// */

.lds-ripple {
	display: inline-block;
	position: relative;
	width: 64px;
	height: 64px;
}

.lds-ripple div {
	position: absolute;
	border: 4px solid rgba(0, 0, 0, 0.35);
	opacity: 1;
	border-radius: 50%;
  	transform-origin: center;
	animation: lds-ripple 1.3s cubic-bezier(0, 0.2, 0.8, 1) infinite;
}

.lds-ripple div:nth-child(2) {
	animation-delay: -0.5s;
}

@keyframes lds-ripple {
	0% {
		top: 28px;
		left: 28px;
		width: 0;
		height: 0;
		opacity: 1;
	}
	100% {
		top: -1px;
		left: -1px;
		width: 58px;
		height: 58px;
		opacity: 0;
	}
}


// /*
// 	Loading circle
// */

$base-line-height: 50px;
$color: var(--primary-color);
$off: var(--primary-color);
$spin-duration: 1s;
$pulse-duration: 750ms;

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-circle {
  border-radius: 50%;
  width: $base-line-height;
  height: $base-line-height;
  border: .25rem solid $off;
  border-top-color: $color;
  animation: spin $spin-duration infinite linear;
}

