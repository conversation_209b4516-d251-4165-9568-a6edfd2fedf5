
// Helpers

// Color of selection
::-moz-selection {
    /* Code for Firefox */
    background: $selection-color
}

::selection {
    background: $selection-color
}

// This class is used to hide text visually while keeping it accessible to screen readers.
.sr-only {
    border: 0;
    clip: rect(0 0 0 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
    color: black;
    font-size: 12px;
    text-align: center;
    padding: 5px;
}

.sr-only.focusable:active,
.sr-only.focusable:focus {
    clip: auto;
    height: auto;
    margin: 0;
    overflow: visible;
    position: static;
    width: auto;
}