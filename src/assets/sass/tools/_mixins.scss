/**
** Custom scss Mixins
*/

// This mixin defines position properties such as top, right, bottom, and left based on the provided values.
// ```
// .positioned-element {
//     @include position($top: 10px, $right: 20px);
// }
// ```
// - $top: Distance from the top edge of the containing element. If provided, sets the 'top' property.
// - $right: Distance from the right edge of the containing element. If provided, sets the 'right' property.
// - $bottom: Distance from the bottom edge of the containing element. If provided, sets the 'bottom' property.
// - $left: Distance from the left edge of the containing element. If provided, sets the 'left' property.
@mixin position($top: null, $right: null, $bottom: null, $left: null) {
  @if ($top) { top: $top; }
  @if ($right) { right: $right; }
  @if ($bottom) { bottom: $bottom; }
  @if ($left) { left: $left; }
}

// This mixin defines size properties such as width and height based on the provided values.
// ```
// .sized-element {
//     @include size(100px);
//     // Or for different width and height:
//     // @include size(200px, 150px);
// }
// ```
// - $width: Width of the element. This parameter is required.
// - $height: Height of the element. If not provided, it defaults to the value of $width, making the element a square.
@mixin size($width, $height: $width) {
  width: $width;
  height: $height;
}

// This mixin defines flexbox properties such as display, flex-direction, align-items, and justify-content based on the provided values.
// ```
// .flex-container {
//     @include flex($align: center, $justify: flex-end, $direction: column, $gap: 10px);
// }
// ```
// - $align: Alignment of flex items along the cross-axis. Defaults to 'center'.
// - $justify: Alignment of flex items along the main-axis. Defaults to 'center'.
// - $direction: Direction of the flex container's main axis. Defaults to 'row'.
// - $gap: Optional gap between flex items. It defaults to `var(--grid-gutter)`.
@mixin flex($align: center, $justify: center, $direction: row, $gap:  var(--grid-gutter)) {
  display: flex;
  flex-direction: $direction;
  align-items: $align;
  justify-content: $justify;
  @if $gap { gap: $gap; }
}

// This mixin defines CSS grid properties such as display, grid-template-columns, and gap based on the provided values.
// ```
// .grid-container {
//     @include grid(3, 20px);
// }
// ```
// - $columns: Number of columns in the grid layout. It can be either a number (to create equal-width columns using `repeat`) or a custom grid-template-columns value.
// - $gap: The gap between grid items. It defaults to `var(--grid-gutter)`.
@mixin grid($columns: 1, $gap: var(--grid-gutter)) {
  display: grid;
  @if (type_of($columns) == 'number') { grid-template-columns: repeat($columns, 1fr); }
  @else { grid-template-columns: $columns; }
  gap: $gap;
}

// This mixin defines font size and related properties such as line-height, weight, and spacing based on the provided values.
// ```
// .styled-text {
//     @include fontSize(16px, $line-height: 1.5, $weight: bold, $spacing: 1px);
// }
// ```
// - $size: Font size of the text. This parameter is required.
// - $line-height: Line height of the text. If provided, sets the 'line-height' property.
// - $weight: Font weight of the text. If provided, sets the 'font-weight' property.
// - $spacing: Letter spacing of the text. If provided, sets the 'letter-spacing' property.

@mixin fontSize($size, $line-height: null, $weight: null, $spacing: null) {
  font-size: $size;
  font-size: rem($size);

  @if ($line-height) {
    line-height: #{$line-height};
    line-height: rem($line-height);
  }

  @if ($weight) {
    font-weight: #{$weight};
  }

  @if ($spacing) {
    letter-spacing: #{$spacing};
  }
}


// This mixin generates a pseudo-element with specified properties such as position, display, and content.
// ```
// .container::before {
//     @include pseudoElement($position: absolute, $display: block, $content: 'Before content');
// }
// ```
// - $position: Positioning of the pseudo-element. Defaults to 'absolute'.
// - $display: Display type of the pseudo-element. Defaults to 'block'.
// - $content: Content of the pseudo-element. Defaults to an empty string.
@mixin pseudoElement($position: absolute, $display: block, $content: '') {
  position: $position;
  display: $display;
  content: $content;
}

// This mixin generates a '::before' pseudo-element with specified properties such as position, display, and content.
// ```
// .container {
//     @include before($position: absolute, $display: block, $content: 'Before content');
// }
// ```
// - $position: Positioning of the pseudo-element. Defaults to 'absolute'.
// - $display: Display type of the pseudo-element. Defaults to 'block'.
// - $content: Content of the pseudo-element. Defaults to an empty string.
@mixin before($position: absolute, $display: block, $content: '') {
  &::before { @include pseudoElement($position, $display, $content) };
}

// This mixin generates a '::after' pseudo-element with specified properties such as position, display, and content.
// ```
// .container {
//     @include after($position: absolute, $display: block, $content: 'after content');
// }
// ```
// - $position: Positioning of the pseudo-element. Defaults to 'absolute'.
// - $display: Display type of the pseudo-element. Defaults to 'block'.
// - $content: Content of the pseudo-element. Defaults to an empty string.
@mixin after($position: absolute, $display: block, $content: '') {
  &::after { @include pseudoElement($position, $display, $content) };
}

// This mixin defines CSS properties for an image, adjusting width, height, and object-fit.
// ```
// .image-container {
//     @include img($fit: cover);
// }
// ```
// - $fit: Determines how the image should fit within its container. Defaults to 'cover'.
@mixin img($fit: cover) {
  width: 100%;
  height: 100%;
  object-fit: $fit;
}

// This mixin generates a pseudo-element with icon code and optional font size. 
// It works specifically with IcoMoon icons (referencing icon.scss).
// ```
// .icon-container {
//     @include icon('icon-code', $fontSize: 24px);
// }
// ```
// - $code: The code of the icon from the IcoMoon font.
// - $fontSize: Optional font size of the icon. If provided, sets the 'font-size' property.
@mixin icon($code, $fontSize: null) {
  @include pseudoElement();
  font-family: 'icomoon';
  content: $code;
  @if ($fontSize) { font-size: $fontSize; }
}

// This mixin truncates text beyond a specified boundary, displaying ellipsis.
// ```
// .truncated-text {
//     @include truncate(200px);
// }
// ```
// - $truncation-boundary: The maximum width at which text should be truncated, beyond which ellipsis will be displayed.
@mixin truncate($truncation-boundary) {
  max-width: $truncation-boundary;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

// This mixin hides the scrollbar across different browsers.
// ```
// .scrollable-container {
//     @include noScrollBar();
// }
// ```
@mixin noScrollBar() {
  &::-webkit-scrollbar { display: none; }
  & {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }
} 

// This mixin sets basic focus outline properties for an element.
// ```
// .focus-element {
//     @include focus();
// }
// ```
@mixin focus() {
  outline-color: $focus-color;
	outline-style: solid;
	outline-width: 1px;
	outline-offset: 0.2em;
}

// This mixin sets basic container properties by centering the container horizontally and adding padding on the left and right sides.
// ```
// .custom-container {
//     @include container;
// }
// ```
@mixin container {
  margin-right: auto;
  margin-left: auto;
  padding-left: var(--container-margin);
  padding-right: var(--container-margin);
}

// Todo: might delete this one 
@mixin clearfix {
  &:after {
    content: "";
    display: table;
    clear: both;
  }
}