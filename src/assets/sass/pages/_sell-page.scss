view-sell {
	.section-estimation {
		background: $background;
	}
}

.sell-sheet-page {
	padding-top: 80px;
	opacity: 0;
	transition: 0.5s all ease;
	background-color: $white;

	&.show { opacity: 1; }

	.page-list-ctn {
		padding-left: 0;

		& > h1, & > h2, & > h3 {
			margin-top: 0;	
		}

		.page-description {
			p, li, div {
				color: var(--color-text);
				font-family: var(--font-secondary);
				font-size: var(--font-size-small);
				line-height: var(--line-height-small);
			}

			h3 {
				margin: 50px 0 30px;
				font-family: var(--font-secondary);
			}
		}
	}

	.team-info-wrap {
		padding-right: 0;
		padding-bottom: 30px;
	}

	@media (max-width: 992px) {
		.team-info-wrap {
			padding-left: 0px;	
		}
	}

	@media (max-width: 767px) {
		padding-top: 50px;

		.page-list-ctn {
			// h1.page-title {
			// 	font-size: 24px;
			// 	line-height: 24px;
			// }
		}
	}
}