view-blog-post {

}

$articleShareSize: 16px;
$articleShareColor: var(--color-text);

.blog-details-page {
  background-color: $white;
  padding-top: 60px;
  padding-bottom: 100px;

  .backTop {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: var(--primary-color);
    justify-content: center;
    display: flex;
    align-items: center;
    position: fixed;
    right: 20px;
    bottom: 20px;
    z-index: 10;
    opacity: 0.7;
    animation: $transition;
    cursor: pointer;

    .icon-arrow_upward::before { font-size: 30px; }

    &:hover { opacity: 1; }

    a { color: white; }
  }

  .centered-cta {
    margin-bottom: 60px;
    .main-button { width: 100%; }
  }

  .contact-cta {
    @include flex(center, space-between);
    margin-bottom: 60px;
    padding: 35px 50px;
    background-color: var(--color-bg);

    .text-ctn {
      margin-right: 30px;

      .description {
        margin: 0;
        color: var(--color-text);
        font-family: var(--font-secondary);
        font-size: 13px;
        line-height: var(--line-height-cta-small);
      }
    }

    .main-button {
      min-width: 200px;
      margin-left: auto;
    }

    @media (max-width: 767px) {
      display: block;
      padding: 30px;

      .text-ctn {
        text-align: center;
        margin-right: 0;
      }

      .main-button {
        margin: 20px auto 0;
      }
    }
  }

  .-smaller {
    position: relative;
  }

  .article-share-cpn {
    position: sticky;
    height: 0;
    top: 40vh;
    margin-left: -80px;

    .fa, i {
      font-size: $articleShareSize;
      color: $articleShareColor;
    }

    div {
      display: flex;
      padding-top: 20px;
      align-items: baseline;
      flex-direction: column;

      a, button {
        margin-bottom: 20px;
        margin-right: 0;
      }
    }
  }

  .-mobile {
    display: none;
  }

  .main-img-ctn {
    text-align: center;
    
    img {
      border-radius: 24px;
      width: 100%;
      height: 100%;
      object-fit: cover;
      max-height: 540px;
      background-position: center;
    }
  }

  .-page-head {
    text-align: left;

    .resume {
      font-size: var(--font-size-blog);
      line-height: var(--line-height-blog);
      color: var(--color-text);
      font-family: var(--font-secondary);
      padding-bottom: 40px;
      margin-bottom: 0;
      border-bottom: 1px solid #F2F2F2;
    }
  }

  .details-info {
    // padding-bottom: 40px;
    margin-top: 15px;

    p {
      margin: 0px;
      display: inline-block;
      color: var(--color-text);
      font-family: var(--font-secondary);
      font-size: 13px;
      line-height: 24px;
    }

    .date {
      margin-left: 20px;
      opacity: 0.6;
    }

    .categories {
      color: var(--primary-color);
      padding-right: 20px;
      border-right: 1px solid #D8D8D8;
    }
  }

  // reduce default headings size
  .laraberg {
    h1 {
      @extend .-small;
    }

    h2 {
      @extend .-small;
    }

    h3 {
      @extend .-small;
    }

    h4 {
      @extend .-small;
    }

  }

  .main-text-ctn {
    padding-top: 15px;

    p {
      color: var(--color-text);
      font-family: var(--font-secondary);
      font-size: var(--font-size-small);
      line-height: var(--line-height-small);
    }

    ul {
      padding-left: 0px;
      padding-bottom: 30px;

      li {
        position: relative;
        padding: 16px 0px 16px 40px;
        // color: $listColor;
        font-family: var(--font-secondary);
        font-size: var(--font-size-small);
        line-height: var(--line-height-small);
        border-bottom: 1px solid #D8D8D8;

        &:before {
          color: var(--primary-color);
          font-family: 'icomoon';
          speak: none;
          font-style: normal;
          font-weight: normal;
          font-variant: normal;
          text-transform: none;
          line-height: 1;
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
          content: "\e90d";
          position: absolute;
          left: 0;
          top: 22px;
        }

        p {
          margin: 0;
        }
      }
    }

    .embeddedContent {
      margin: 40px 0;
      position: relative;
      padding-bottom: 56.25%;
      /* 16:9 */
      height: 0;

      iframe {
        position: absolute;
        top: 0;
        left: 0;
        width: 100% !important;
        height: 100% !important;
      }
    }
  }

  @media (max-width: 992px) {
    .-mobile {
      position: unset;
      display: flex;
      justify-content: space-between;
      margin-left: 0px;
      padding-top: 20px;
      padding-bottom: 60px;

      .fa,
      i {
        font-size: $articleShareSize;
        color: $articleShareColor;
      }

      div {
        display: flex;
        flex-direction: row;
        align-items: baseline;
        padding-top: 00px;
        padding-right: 0px;
        margin: 0 auto;

        a,
        button {
          margin-bottom: 0px;
          margin-right: 20px;
        }
      }
    }

    .-desktop {
      display: none;
    }
  }

  @media (max-width: 767px) {
    padding-top: 50px;
    padding-bottom: 70px;

    .-page-head {
      .resume {
        font-size: 20px;
        line-height: 30px;
      }
    }

    .main-text-ctn h2 {
      font-size: 24px;
    }

    .article-share-cpn {
      padding-bottom: 40px;
    }
  }
}

@media print {
  .no-print,
  .no-print * {
    display: none !important;
  }
}