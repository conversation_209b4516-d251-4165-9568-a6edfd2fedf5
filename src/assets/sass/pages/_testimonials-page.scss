view-testimonials {
	
}


/*
	Testimonials Page
*/

.testimonials-page {
	padding-bottom: 75px;
	background: var(--color-bg);

	.testimonials-wrap {
		margin-top: 40px;
		column-count: 2;
		column-gap: 15px;

		@media (max-width: 992px) {
			display: block;
			column-count: inherit;
			column-gap: inherit;
		}

		.iframe-container {
			margin-bottom: 20px;
			position: relative;
			padding-bottom: 56.25%; /* 16:9 */
			padding-top: 25px;
			height: 0;

			iframe {
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				transform: translate3d(0, 0, 0); // This line prevents inner iframe position bug in Chrome
			}
		}

		&.-full{
			column-count: initial;
		}
	}
}