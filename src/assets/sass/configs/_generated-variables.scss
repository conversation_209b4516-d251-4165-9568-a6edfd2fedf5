/* Generated SCSS Variables from tokens.json */
/* Theme mode: light */
/* Generated on: 2025-08-06T15:07:14.936Z */

:root {

    // ==========================================
    // TYPOGRAPHY VARIABLES
    // ==========================================

    /* Font Families */
    --font-family-title: "the-seasons"; /* Mobile: "The Seasons" */
    --font-family-body: "Noto sans"; /* Mobile: "Manrope" */
    --font-family-cta: "Manrope"; /* Mobile: "Poppins" */
    --font-family-nav: "Manrope"; /* Mobile: "Poppins" */
    --font-family-eyebrow: "Noto sans"; /* Mobile: "Poppins" */
    --font-family-meta: "Noto sans"; /* Mobile: "Poppins" */

    /* Font Sizes */
    --text-nav-header-primary-size: #{rem(20px)};
    --text-title-h-1-size: #{responsive-size(56px, 72px, $desktop-lg)};
    --text-eyebrow-sm-size: #{rem(14px)};
    --text-cta-sm-size: #{responsive-size(14px, 16px, $desktop-lg)};
    --text-thumbnail-blog-meta-sm-size: #{rem(16px)};
    --text-table-accent-size: #{rem(16px)};
    --text-table-value-size: #{rem(16px)};
    --text-form-label-size: #{rem(18px)};
    --text-form-select-size: #{rem(18px)};
    --text-form-placeholder-size: #{rem(16px)};
    --text-form-checkbox-size: #{rem(18px)};
    --text-thumbnail-blog-title-sm-size: #{responsive-size(16px, 24px, $desktop-lg)};
    --text-title-display-size: #{responsive-size(56px, 88px, $desktop-lg)};
    --text-title-h-1-small-size: #{responsive-size(48px, 64px, $desktop-lg)};
    --text-title-h-2-size: #{responsive-size(32px, 56px, $desktop-lg)};
    --text-title-h-3-size: #{responsive-size(32px, 40px, $desktop-lg)};
    --text-title-h-4-size: #{rem(24px)};
    --text-title-h-5-size: #{rem(20px)};
    --text-title-h-6-size: #{responsive-size(16px, 20px, $desktop-lg)};
    --text-body-xs-size: #{responsive-size(12px, 16px, $desktop-lg)};
    --text-body-sm-size: #{responsive-size(14px, 18px, $desktop-lg)};
    --text-body-md-size: #{responsive-size(16px, 20px, $desktop-lg)};
    --text-body-lg-size: #{responsive-size(20px, 24px, $desktop-lg)};
    --text-body-bold-size: #{responsive-size(18px, 20px, $desktop-lg)};
        --font-size-s: #{rem(30px)};
        --font-size-m: #{rem(40px)};
        --font-size-xl: #{rem(56px)};
        --font-size-2xl: #{rem(64px)};
        --font-size-l: #{responsive-size(48px, 46px, $desktop-lg)};
        --font-size-3xl: #{rem(72px)};
        --font-size-4xl: #{rem(88px)};
        --font-size-xs: #{rem(24px)};
        --font-size-3xs: #{rem(18px)};
        --font-size-2xs: #{rem(20px)};
        --font-size-5xs: #{rem(14px)};
    --text-title-h-3-small-size: #{responsive-size(24px, 30px, $desktop-lg)};
    --text-title-h-2-small-size: #{responsive-size(32px, 46px, $desktop-lg)};
    --text-eyebrow-md-size: #{responsive-size(16px, 18px, $desktop-lg)};
    --text-eyebrow-lg-size: #{responsive-size(18px, 20px, $desktop-lg)};
    --text-cta-md-size: #{responsive-size(16px, 18px, $desktop-lg)};
    --text-cta-lg-size: #{responsive-size(18px, 20px, $desktop-lg)};
    --text-thumbnail-property-price-sm-size: #{rem(24px)};
    --text-thumbnail-property-type-sm-size: #{rem(14px)};
    --text-thumbnail-property-address-sm-size: #{rem(12px)};
    --text-thumbnail-blog-title-md-size: #{rem(30px)};
    --text-thumbnail-blog-meta-md-size: #{rem(18px)};
    --text-thumbnail-blog-excerpt-md-size: #{rem(20px)};
    --text-thumbnail-blog-title-lg-size: #{rem(40px)};
    --text-thumbnail-blog-meta-lg-size: #{rem(20px)};
    --text-thumbnail-blog-excerpt-lg-size: #{rem(20px)};
    --text-thumbnail-property-price-md-size: #{rem(24px)};
    --text-thumbnail-property-address-md-size: #{rem(28px)};
        --font-size-4xs: #{rem(16px)};
        --font-size-6xs: #{rem(12px)};
    --text-nav-header-secondary-size: #{rem(18px)};
    --text-thumbnail-property-type-md-size: #{rem(14px)};

    /* Font Weights */
    --text-nav-header-primary-weight: 600;
    --text-title-h-1-weight: 300;
    --text-title-display-weight: 300;
    --text-title-h-1-small-weight: 300;
    --text-title-h-2-weight: 400;
    --text-title-h-3-weight: 300;
    --text-title-h-4-weight: 500;
    --text-title-h-5-weight: 600;
    --text-title-h-6-weight: 700;
    --text-body-xs-weight: 300;
    --text-body-sm-weight: 300;
    --text-body-md-weight: 300;
    --text-body-lg-weight: 300;
    --text-body-bold-weight: 700;
    --font-weight-light: 300;
    --text-title-h-3-small-weight: 300;
    --text-title-h-2-small-weight: 400;
    --text-thumbnail-property-price-sm-weight: 0;
    --text-thumbnail-property-price-md-weight: 0;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --text-eyebrow-sm-weight: 600;
    --text-eyebrow-md-weight: 600;
    --text-eyebrow-lg-weight: 600;
    --text-cta-sm-weight: 600;
    --text-cta-md-weight: 600;
    --text-cta-lg-weight: 600;
    --text-form-label-weight: 300;
    --text-form-select-weight: 300;
    --text-form-placeholder-weight: 300;
    --text-form-checkbox-weight: 300;
    --text-table-accent-weight: 700;
    --text-table-value-weight: 300;
    --text-thumbnail-blog-title-sm-weight: 500;
    --text-thumbnail-blog-title-md-weight: 500;
    --text-thumbnail-blog-title-lg-weight: 500;
    --text-thumbnail-blog-meta-sm-weight: 500;
    --text-thumbnail-blog-meta-md-weight: 300;
    --text-thumbnail-blog-meta-lg-weight: 300;
    --text-thumbnail-blog-excerpt-md-weight: 300;
    --text-thumbnail-blog-excerpt-lg-weight: 300;
    --text-nav-header-secondary-weight: 600;
    --text-thumbnail-property-type-sm-weight: 700;
    --text-thumbnail-property-type-md-weight: 300;
    --font-weight-regular: 400;

    /* Other Typography */
    --text-nav-footer-primary-title: #{rem(20px)};
    --text-nav-footer-primary-menu: 500;
    --text-nav-footer-secondary-title: #{rem(18px)};
    --text-nav-footer-secondary-menu: 400;
    --text-thumbnail-price: #{rem(0px)};
    --text-thumbnail-accent: #{rem(0px)};
    --text-thumbnail-property-medium: #{rem(0px)};
    --text-thumbnail-broker-sm-title-small: #{rem(24px)};
    --text-thumbnail-broker-sm-meta-small: #{rem(14px)};
    --text-thumbnail-broker-sm-phone-small: #{rem(14px)};
    --text-thumbnail-broker-md-title-medium: #{rem(30px)};
    --text-thumbnail-broker-md-meta-medium: #{rem(14px)};
    --text-thumbnail-broker-md-phone-medium: #{rem(16px)};
    --text-thumbnail-broker-lg-title-large: #{rem(40px)};
    --text-thumbnail-broker-lg-meta-large: #{rem(18px)};
    --text-thumbnail-broker-lg-phone-large: #{rem(18px)};


    // ==========================================
    // COLOR VARIABLES
    // ==========================================

    /* Brand Colors */
    --color-brand-primary-900: #000e35;
    --color-brand-primary-500: #0d2055;
    --color-brand-primary-300: #c0cfe1;
    --color-brand-secondary-900: #660000;
    --color-brand-secondary-500: #ff1200;
    --color-brand-complement-500: #194881;
    --color-brand-primary-100: #e3edf9;
    --color-brand-secondary-300: #f5d9d2;

    /* Text Colors */
    --color-text-body-primary: #333333;
    --color-text-title-primary: #151515;
    --color-text-body-links: #0d2055;
    --color-text-title-eyebrow: #0d2055;
    --color-cta-primary-default-text: #ffffff;
    --color-cta-primary-hover-text: #ffffff;
    --color-cta-secondary-default-text: #000e35;
    --color-cta-secondary-hover-text: #ffffff;
    --color-text-body-testimony: #660000;
    --color-text-body-secondary: #4a4a4a;
    --color-elements-icon-contextual: #0d2055;
    --color-cta-tertiary-default-text: #0d2055;
    --color-cta-tertiary-hover-text: #000e35;
    --color-nav-primary-text: #151515;
    --color-nav-secondary-text: #333333;
    --color-text-body-links-hover: #000e35;
    --color-text-body-phone: #000e35;
    --color-text-body-input: #4a4a4a;

    /* Background Colors */
    --color-bg-default: #ffffff;
    --color-bg-section-1: #e3edf9;
    --color-bg-input: #ffffff;
    --color-bg-overlay-low: #00071d66;
    --color-cta-primary-default-bg: #0d2055;
    --color-cta-primary-hover-bg: #194881;
    --color-cta-secondary-default-bg: #ffffff00;
    --color-cta-secondary-hover-bg: #194881;
    --color-nav-slider-default-bg: #0d2055;
    --color-nav-slider-hover-bg: #194881;
    --color-bg-overlay-high: #00081db2;
    --color-bg-box: #e3edf9;
    --color-bg-section-2: #c0cfe1;

    /* CTA Colors */
    --color-cta-primary-default-border: #ffffff00;
    --color-cta-primary-hover-border: #ffffff00;
    --color-cta-secondary-default-border: #000e35;
    --color-cta-secondary-hover-border: #ffffff00;
    --color-cta-tertiary-default-symbol: #0d2055;
    --color-cta-tertiary-hover-symbol: #000e35;

    /* Element Colors */
    --color-elements-vendu: #ff1200;
    --color-elements-nouveau: #000e35;
    --color-elements-visite-libre: #151515;
    --color-elements-chart-chart-1: #000e35;
    --color-elements-chart-chart-2: #ff1200;
    --color-elements-chart-chart-3: #660000;
    --color-elements-chart-chart-4: #c0cfe1;
    --color-elements-chart-chart-5: #194881;
    --color-elements-icon-interface: #000e35;
    --color-elements-icon-phone: #000e35;
    --color-elements-video: #151515;
    --color-elements-icon-star: #f1bf31;

    /* Navigation Colors */
    --color-nav-slider-default-arrow: #ffffff;
    --color-nav-slider-hover-icon: #ffffff;
    --color-nav-pager-default: #0d2055;
    --color-nav-pager-hover: #000e35;

    /* Base Colors */
    --color-base-600: #4a4a4a;
    --color-base-700: #333333;
    --color-base-100: #dddddd;
    --color-base-800: #151515;
    --color-base-0: #ffffff;
    --color-base-400: #a5a5a5;
    --color-base-transparent: #ffffff00;

    /* Other Colors */
    --color-border-default: #a5a5a5;


    // ==========================================
    // INTERFACE VARIABLES
    // ==========================================

    /* Border Radius */
    --radius-small: #{rem(16px)};
    --radius-medium: #{rem(40px)};
    --radius-large: #{rem(72px)};

    /* Spacing */
    --spacing-eyebrow-standard: #{responsive-size(32px, 40px, $desktop-lg)};
    --spacing-eyebrow-large: #{responsive-size(40px, 56px, $desktop-lg)};
    --spacing-body-lg-top: #{rem(32px)};
    --spacing-body-lg-bottom: #{rem(32px)};
    --spacing-heading-h-1-bottom: #{rem(40px)};
    --spacing-heading-h-1-top: #{rem(80px)};
    --spacing-heading-h-2-top: #{rem(80px)};
    --spacing-heading-h-2-bottom: #{responsive-size(24px, 40px, $desktop-lg)};
    --spacing-heading-h-3-top: #{rem(64px)};
    --spacing-heading-h-3-bottom: #{rem(32px)};
    --spacing-heading-h-4-top: #{rem(56px)};
    --spacing-heading-h-4-bottom: #{rem(24px)};
    --spacing-heading-h-5-top: #{rem(56px)};
    --spacing-heading-h-5-bottom: #{rem(24px)};
    --spacing-heading-h-6-top: #{rem(40px)};
    --spacing-heading-h-6-bottom: #{rem(24px)};
    --spacing-body-md-top: #{responsive-size(16px, 24px, $desktop-lg)};
    --spacing-body-md-bottom: #{responsive-size(16px, 24px, $desktop-lg)};
    --spacing-body-sm-top: #{rem(16px)};
    --spacing-body-sm-bottom: #{rem(16px)};
    --spacing-body-caption-top: #{rem(16px)};
    --spacing-body-caption-bottom: #{rem(16px)};

    /* Padding */
    --padding-section-vertical-sm: #{rem(64px)};
    --padding-section-vertical-default: #{responsive-size(40px, 120px, $desktop-lg)};
    --padding-section-vertical-lg: #{rem(180px)};

    // padding interne des boites // 
    --padding-box-horizontal-sm: #{responsive-size(16px, 24px, $desktop-lg)};
    --padding-box-horizontal-md: #{responsive-size(24px, 32px, $desktop-lg)};
    --padding-box-horizontal-lg: #{responsive-size(24px, 72px, $desktop-lg)};
    --padding-box-vertical-small: #{responsive-size(24px, 32px, $desktop-lg)};
    --padding-box-vertical-medium: #{responsive-size(32px, 40px, $desktop-lg)};
    --padding-box-vertical-large: #{responsive-size(40px, 64px, $desktop-lg)};

    // padding pour decaller les elements // 
    --padding-indent-standard: #{rem(80px)};
    --padding-indent-large: #{rem(96px)};
    --padding-indent-small: #{rem(40px)};

    // Container ?// 
    --padding-section-horizontal-none: #{rem(0px)};
    --padding-section-horizontal-small: #{responsive-size(16px, 360px, $desktop-lg)};
    --padding-section-horizontal-default: #{responsive-size(16px, 148px, $desktop-lg)};
    --padding-section-horizontal-fullwidth: #{responsive-size(24px, 40px, $desktop-lg)};

    /* Grid System */
    --grid-gap: #{responsive-size(16px, 32px, $desktop-lg)};
    --grid-contained-small: #{rem(360px)};
    --grid-fullwidth: #{rem(40px)};
    --grid-contained-default: #{rem(148px)};

    /* Scale System */
    --scale-0: #{rem(0px)};
    --scale-2xs: #{rem(16px)};
    --scale-xs: #{rem(24px)};
    --scale-s: #{rem(32px)};
    --scale-m: #{rem(40px)};
    --scale-l: #{rem(56px)};
    --scale-xl: #{rem(64px)};
    --scale-2xl: #{rem(72px)};
    --scale-3xl: #{rem(80px)};
    --scale-4xl: #{rem(96px)};
    --scale-5xl: #{rem(120px)};
    --scale-6xl: #{rem(180px)};
    --scale-3xs: #{rem(10px)};

    /* Strokes */
    --stroke-stroke-small: "2";
    --stroke-stroke-medium: "3";
    --stroke-stroke-large: "8";

    /* Sizing */
    --sizing-img-min-height: #{responsive-size(320px, 490px, $desktop-lg)};
    --sizing-img-section-min-height: #{responsive-size(380px, 800px, $desktop-lg)};
    --sizing-img-blog-sm-width: #{responsive-size(120px, 180px, $desktop-lg)};
    --sizing-img-max-height: #{responsive-size(600px, 1024px, $desktop-lg)};
    --sizing-device: "desktop"; /* Mobile: "mobile" */

    /* Icon Spacing */
    --icon-bottom-3: #{rem(32px)};
    --icon-top-2: #{rem(72px)};
    --icon-bottom: #{rem(32px)};
    --icon-top-3: #{rem(56px)};
    --icon-top: #{rem(72px)};
    --icon-bottom-6: #{rem(32px)};
    --icon-top-6: #{rem(32px)};
    --icon-top-4: #{rem(56px)};
    --icon-bottom-5: #{rem(32px)};
    --icon-top-5: #{rem(40px)};
    --icon-bottom-4: #{rem(24px)};
    --icon-bottom-2: #{rem(48px)};

}

// ==========================================
// MOBILE OVERRIDES
// ==========================================

@media (max-width: $tablet-lg) {
    :root {
        /* Typography Mobile Overrides */
        --font-family-title: "The Seasons";
        --font-family-body: "Manrope";
        --font-family-cta: "Poppins";
        --font-family-nav: "Poppins";
        --font-family-eyebrow: "Poppins";
        --font-family-meta: "Poppins";

        /* Font Weight Mobile Overrides */
        --text-title-h-2-weight: 300;
        --font-weight-light: 400;
        --text-title-h-2-small-weight: 300;
        --text-thumbnail-blog-title-sm-weight: 400;

        /* Interface Mobile Overrides */
        --sizing-device: "mobile";

    }
}
