@font-face {
  font-family: 'icomoon';
  src:  url('/assets/fonts/icons/icomoon.eot?b3h4rt');
  src:  url('/assets/fonts/icons/icomoon.eot?b3h4rt#iefix') format('embedded-opentype'),
    url('/assets/fonts/icons/icomoon.ttf?b3h4rt') format('truetype'),
    url('/assets/fonts/icons/icomoon.woff?b3h4rt') format('woff'),
    url('/assets/fonts/icons/icomoon.svg?b3h4rt#icomoon') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="icon-"], [class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-tiktok:before {
  content: "\e905";
}
.icon-x:before {
  content: "\e952";
}
.icon-star:before {
  content: "\e953";
  color: #fc0;
}
.icon-house_360:before {
  content: "\e94f";
}
.icon-camera:before {
  content: "\e950";
}
.icon-fill-play:before {
  content: "\e951";
}
.icon-acheter-vendre:before {
  content: "\e920";
  color: #40bf76;
}
.icon-affaire:before {
  content: "\e929";
  color: #40bf76;
}
.icon-argent:before {
  content: "\e92b";
  color: #40bf76;
}
.icon-arts:before {
  content: "\e92c";
  color: #40bf76;
}
.icon-augmentation:before {
  content: "\e92e";
  color: #40bf76;
}
.icon-auto:before {
  content: "\e92f";
  color: #40bf76;
}
.icon-bibliotheque:before {
  content: "\e931";
  color: #40bf76;
}
.icon-ce-qu-il-disent:before {
  content: "\e932";
  color: #40bf76;
}
.icon-couple:before {
  content: "\e933";
  color: #40bf76;
}
.icon-education:before {
  content: "\e934";
  color: #40bf76;
}
.icon-lac-piscine:before {
  content: "\e935";
  color: #40bf76;
}
.icon-medaille:before {
  content: "\e936";
  color: #40bf76;
}
.icon-nightlife:before {
  content: "\e937";
  color: #40bf76;
}
.icon-nourriture:before {
  content: "\e938";
  color: #40bf76;
}
.icon-parc-amusement:before {
  content: "\e93a";
  color: #40bf76;
}
.icon-parc:before {
  content: "\e93f";
  color: #40bf76;
}
.icon-personnes-agees:before {
  content: "\e940";
  color: #40bf76;
}
.icon-population:before {
  content: "\e941";
  color: #40bf76;
}
.icon-sante:before {
  content: "\e942";
  color: #40bf76;
}
.icon-satistiques:before {
  content: "\e943";
  color: #40bf76;
}
.icon-sourire:before {
  content: "\e944";
  color: #40bf76;
}
.icon-terrasse:before {
  content: "\e945";
  color: #40bf76;
}
.icon-thumbs-up:before {
  content: "\e946";
  color: #40bf76;
}
.icon-train:before {
  content: "\e947";
  color: #40bf76;
}
.icon-velo:before {
  content: "\e948";
  color: #40bf76;
}
.icon-logo-youtube:before {
  content: "\ea9d";
}
.icon-more-info:before {
  content: "\e94c";
}
.icon-round-dollar:before {
  content: "\e94b";
}
.icon-fav-property:before {
  content: "\e94a";
}
.icon-shower:before {
  content: "\e949";
}
.icon-website:before {
  content: "\e93c";
}
.icon-search:before {
  content: "\e93e";
}
.icon-close:before {
  content: "\e93d";
}
.icon-bus:before {
  content: "\e928";
  color: #40bf76;
}
.icon-coeur:before {
  content: "\e92a";
  color: #40bf76;
}
.icon-famille:before {
  content: "\e92d";
  color: #40bf76;
}
.icon-foret:before {
  content: "\e930";
  color: #40bf76;
}
.icon-sport:before {
  content: "\e939";
  color: #40bf76;
}
.icon-ville:before {
  content: "\e93b";
  color: #40bf76;
}
.icon-logo-facebook:before {
  content: "\e900";
}
.icon-logo-googleplus:before {
  content: "\e901";
}
.icon-logo-instagram:before {
  content: "\e902";
}
.icon-logo-linkedin:before {
  content: "\e903";
}
.icon-logo-mail:before {
  content: "\e904";
}
.icon-360:before {
  content: "\e906";
}
.icon-alerte:before {
  content: "\e907";
}
.icon-arrow-externe:before {
  content: "\e908";
}
.icon-arrow-left:before {
  content: "\e909";
}
.icon-arrow-right:before {
  content: "\e90a";
}
.icon-calendar:before {
  content: "\e90b";
}
.icon-check:before {
  content: "\e90c";
  color: #40bf76;
}
.icon-checked:before {
  content: "\e90d";
}
.icon-dropdown-menu:before {
  content: "\e90e";
}
.icon-favorite:before {
  content: "\e90f";
}
.icon-alerte-1:before {
  content: "\e910";
}
.icon-eval-1:before {
  content: "\e911";
}
.icon-mail-outline:before {
  content: "\e912";
}
.icon-mail:before {
  content: "\e913";
}
.icon-map-minus:before {
  content: "\e914";
}
.icon-map-plus:before {
  content: "\e915";
}
.icon-mobile:before {
  content: "\e916";
}
.icon-phone-outline:before {
  content: "\e917";
}
.icon-phone:before {
  content: "\e918";
}
.icon-pin-outline:before {
  content: "\e919";
}
.icon-pin:before {
  content: "\e91a";
}
.icon-play:before {
  content: "\e91b";
}
.icon-preference:before {
  content: "\e91c";
}
.icon-print-outline:before {
  content: "\e91d";
}
.icon-print:before {
  content: "\e91e";
}
.icon-refresh:before {
  content: "\e91f";
}
.icon-user:before {
  content: "\e921";
}
.icon-area:before {
  content: "\e922";
}
.icon-bed:before {
  content: "\e923";
}
.icon-garage:before {
  content: "\e924";
}
.icon-alert-1:before {
  content: "\e925";
}
.icon-eval-12:before {
  content: "\e926";
}
.icon-sink:before {
  content: "\e927";
}
.icon-arrow_upward:before {
  content: "\e94e";
}
.icon-facebook:before {
  content: "\e94d";
}
