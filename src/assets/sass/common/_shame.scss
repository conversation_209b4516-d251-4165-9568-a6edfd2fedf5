/**
  * This file contains temporary css code that should probably be put elsewhere.
  * To enforce good coding practices, keep it as empty as possible.
  * For more information: https://csswizardry.com/2013/04/shame-css/
  */


.-no-space {
  margin: 0 !important;
  padding: 0 !important;
}

.-no-top-space {
  margin-top: 0 !important;
  padding-top: 0 !important;
}

.-no-bottom-space {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

.-white {
  color: $white;
}

.-black {
  color: $black;
}

.-dark {
  color: var(--dark-color);
}

.-center {
  text-align: center;
}

.-relative{
  position: relative;
}

.-no-list-style {
  list-style: none;
  padding-left: 0;
}

.-no-scroll {
  overflow: hidden;
}
  
.-open-menu {
  overflow: hidden;
}

.-hide {
	display: none !important;
}

.-flex {
  display: flex;
}

.-flex-reverse {
  @media (max-width: 992px) {
    flex-direction: column-reverse !important;
	}
}

.-flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--grid-gutter);
}

.-flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--grid-gutter);
}

.-emphase-title {
  border-left: 10px solid var(--primary-color);
  padding-left: 20px;
}

.alignfull {
  margin-right: calc(var(--container-margin) * -1);
  margin-left: calc(var(--container-margin) * -1);
  overflow: hidden;
}


// Old class might remove later
.-show-mobile {
	display: none;

	@media (max-width: 992px) {
		display: block;
	}
}

.-hide-mobile {
	@media (max-width: 992px) {
		display: none;
	}
}



// // // // 
// Swiper
// // // //
.swiper-button-disabled {
  opacity: .4;
  cursor: not-allowed;
  pointer-events: none;
}

// Hide duplicated swiper icon
.swiper-button-next::after,
.swiper-button-prev::after {
  content: none;
}

.swiper-button-lock {
  display: none !important;
}

.loading {
	opacity: 0 !important;
}

#file-upload {
  display: none;
}

.grecaptcha-badge {
  visibility: hidden;
}


// Banner for IE users
#banner-ie {
	display: none;
	background: #fdf2ab;
    background-position: 8px 17px;
    z-index: 111111;
    border-bottom: 1px solid #a29330;
    text-align: left;
    cursor: pointer;
    background-color: #fff8ea;
    font: 17px Calibri,Helvetica,Arial,sans-serif;

	div {
		padding: 11px 12px 11px 30px;
		line-height: 1.7em;

		a {
			text-indent: 0;
			color: #fff;
			text-decoration: none;
			box-shadow: 0 0 2px rgba(0,0,0,.4);
			padding: 1px 10px;
			border-radius: 4px;
			font-weight: 400;
			background: #029df7;
			white-space: nowrap;
			margin: 0 2px;
			display: inline-block;
		}

		#chrome {
			background-color: #5ab400;
		}

		#firefox {
			background-color: #f7ae02;
		}
	}

	@media all and (-ms-high-contrast:none)
     {
     	display:block !important;
     	*::-ms-backdrop { display:block !important;} /* IE11 */
     }
}