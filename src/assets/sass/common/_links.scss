a,
button,
input[type="submit"] {
  position: relative;
  text-decoration: none;
  transition: $transition;

  &::before, &::after { transition: $transition; }
}

// Reset button tags
button {
	background: none;
	border: none;
	color: inherit;
	cursor: pointer;
}

// Regular links
a:not(.btn) {
	color: var(--color-text-body-links);

	&:hover {
		opacity: 0.7;
		color: var(--color-text-body-links-hover);
	}
}

// Actual buttons
.btn {
	display: inline;
	border-width: 2px;
	border-style: solid;
	line-height: 0.8;
	border-radius: 100px;
	text-align: center;

	@extend .-cta-medium;
	padding: rem(20px) rem(32px);

	background-color: var(--color-cta-primary-default-bg);
	color: var(--color-cta-primary-default-text);
	border-color: var(--color-cta-primary-default-border);

	&:hover {
		background-color: var(--color-cta-primary-hover-bg);
		color: var(--color-cta-primary-hover-text);
		border-color: var(--color-cta-primary-hover-border);
	}

	&.-small {
		@extend .-cta-small;
		padding: rem(12px) rem(24px);
	}

	&.-large {
		@extend .-cta-large;
		padding: rem(20px) rem(40px);
	}

	&.-secondary {
		background-color: var(--color-cta-secondary-default-bg);
		color: var(--color-cta-secondary-default-text);
		border-color: var(--color-cta-secondary-default-border);

		&:hover {
			background-color: var(--color-cta-secondary-hover-bg);
			color: var(--color-cta-secondary-hover-text);
			border-color: var(--color-cta-secondary-hover-border);
		}
	}

	&.-tertiary {
		display: inline-flex;
		color: var(--color-cta-tertiary-default-text);
		gap: rem(8px);
		align-items: center;

		&:after {
			color: var(--color-cta-tertiary-default-symbol);
			content: "\e90a";
			font-family: 'icomoon';
			font-size: rem(17px);
		}

		padding: 0;
		background: transparent;
		border: none;

		&:hover {
			color: var(--color-cta-tertiary-hover-text);

			&:after {
				color: var(--color-cta-tertiary-hover-symbol);
				transform: translate(4px, 0);
			}
		}
	}
}



