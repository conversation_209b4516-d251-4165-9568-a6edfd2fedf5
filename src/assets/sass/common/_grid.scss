/**
*** This file contains code to create dynamique css grid
*** with global params : $base-column-nb and $column-gap ( see config.scss )
*** This grid is base on CSS Grid guide : https://css-tricks.com/snippets/css/complete-guide-grid/
*** 
*** Sample:
*** 
*** ```html
*** <div class="grid">
        <div class="col-12 col-t-8 col-d-6">Col 12 on mobile / 8 on tablet / 6 on desktop</div>
        <div class="col-12 col-t-8 col-d-6">Col 12 on mobile / 8 on tablet / 6 on desktop</div>
*** </div>
*** ```
***
*** Unused generated style will be clean when compile with Purge CSS ( PurgeCSSPlugin)
*** See detail on webpack.config.js
***/


// Define breakpoints with pixel values subtracted by 1px
$breakpoints: (
    m-min: calc($mobile-min - 1px),
    m-sm: calc($mobile-sm - 1px),
    m: calc($mobile - 1px),
    m-lg: calc($mobile-lg - 1px),
    t-sm: calc($tablet-sm - 1px), 
    t: calc($tablet - 1px),
    t-lg: calc($tablet-lg - 1px),
    t-max: calc($tablet-max - 1px),
    d-sm: calc($desktop-sm - 1px),
    d: calc($desktop - 1px),
    d-lg: calc($desktop-lg - 1px),
    d-xlg: calc($desktop-xlg - 1px),
    d-xxlg: calc($desktop-xxlg - 1px),
);


// Mixin to create grid selectors based on breakpoints
@mixin create-selectors($breakpoint: null) {
    $infix: if($breakpoint == null, '', '-#{$breakpoint}');

    @for $i from 1 through $base-column-nb {
        // Define column classes
        .col#{$infix}-#{$i} {
            grid-column-end: span $i;
        }
        // Define column offset classes
        .col-offset#{$infix}-#{$i} {
            // grid-column-start: $i + 1;
            margin-left: grid-space(math.div($i, $base-column-nb), 0);
        }
        // Define row classes
        .row#{$infix}-#{$i} {
            grid-row-end: span $i;
        }
        // Define row offset classes
        .row-offset#{$infix}-#{$i} {
            grid-row-start: $i + 1;
        }
    }
}

// Define grid container properties
.grid {
    display: grid;
    grid-template-columns: repeat($base-column-nb, 1fr);
    grid-gap: var(--grid-gutter);
    gap: var(--grid-gutter);
}

// Include grid selectors for default settings
@include create-selectors;

// Media queries for each breakpoint to include grid selectors accordingly
@each $breakpoint, $width in $breakpoints {
    @media (min-width: $width) {
        @include create-selectors($breakpoint);
    }
}