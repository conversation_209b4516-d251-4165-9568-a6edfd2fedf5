/**
* This file contains code related to typography - titles, paragraphs, text modifiers, etc
*/


// // // // //
// HEADINGS // 
// // // // //

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-family-title);
  color: var(--color-text-title-primary); 

  // Clickable titles
  a {
    color: var(--color-text-title-primary);
  }
}

h1 {
  font-size: var(--text-title-h-1-size);
  line-height: var(--line-height-110);
  font-weight: var(--text-title-h-1-weight);
  margin: var(--spacing-heading-h-1-top) 0 var(--spacing-heading-h-1-bottom);

  &.-large, &.-display {
    font-size: var(--text-title-display-size);
    line-height: var(--line-height-110);
    font-weight: var(--text-title-display-weight);
  }

  &.-small {
    font-size: var(--text-title-h-1-small-size);
    line-height: var(--line-height-120);
    font-weight: var(--text-title-h-1-small-weight);
  }
}

h2 {
  font-size: var(--text-title-h-2-size);
  line-height: var(--line-height-120);
  font-weight: var(--text-title-h-2-weight);
  margin: var(--spacing-heading-h-2-top) 0 var(--spacing-heading-h-2-bottom);

  &.-small {
    font-size: var(--text-title-h-2-small-size);
    line-height: var(--line-height-120);
    font-weight: var(--text-title-h-2-small-weight);
  }
}

h3 {
  font-size: var(--text-title-h-3-size);
  line-height: var(--line-height-120);
  font-weight: var(--text-title-h-3-weight);
  letter-spacing: var(--letter-spacing-n1);
  margin: var(--spacing-heading-h-3-top) 0 var(--spacing-heading-h-3-bottom);

  &.-small {
    font-size: var(--text-title-h-3-small-size);
    line-height: var(--line-height-120);
    font-weight: var(--text-title-h-3-small-weight);
    letter-spacing: var(--letter-spacing-n1);
  }
}

h4 {
  font-size: var(--text-title-h-4-size);
  line-height: var(--line-height-120);
  font-weight: var(--text-title-h-4-weight);
  margin: var(--spacing-heading-h-4-top) 0 var(--spacing-heading-h-4-bottom);
}

h5 {
  font-size: var(--text-title-h-5-size);
  line-height: var(--line-height-100);
  font-weight: var(--text-title-h-5-weight);
  margin: var(--spacing-heading-h-5-top) 0 var(--spacing-heading-h-5-bottom);
}

h6 {
  font-size: var(--text-title-h-6-size);
  line-height: var(--line-height-100);
  font-weight: var(--text-title-h-6-weight);
  margin: var(--spacing-heading-h-6-top) 0 var(--spacing-heading-h-6-bottom);
}

.-h1 { @extend h1; }
.-h2 { @extend h2; }
.-h3 { @extend h3; }
.-h4 { @extend h4; }
.-h5 { @extend h5; }
.-h6 { @extend h6; }



// // // // // //
//  PARAGRAPHS // 
// // // // // //

.--x-small {
  font-size: var(--text-body-xs-size);
  line-height: var(--line-height-150);
  font-weight: var(--text-body-xs-weight);
}

.--small {
  font-size: var(--text-body-sm-size);
  line-height: var(--line-height-150);
  font-weight: var(--text-body-sm-weight);
  margin: var(--spacing-body-sm-top) 0 var(--spacing-body-sm-bottom) 0;
}

p, .-p, .--normal{
  font-size: var(--text-body-md-size);
  line-height: var(--line-height-150);
  font-weight: var(--text-body-md-weight);
  margin: var(--spacing-body-md-top) 0 var(--spacing-body-md-bottom) 0;
}

.--large {
  font-size: var(--text-body-lg-size);
  line-height: var(--line-height-160);
  font-weight: var(--text-body-lg-weight);
  margin: var(--spacing-body-lg-top) 0 var(--spacing-body-lg-bottom) 0;
}

.--caption {
  font-size: var(--text-body-xs-size);
  line-height: var(--line-height-150);
  font-weight: var(--text-body-xs-weight);
  margin: var(--spacing-body-caption-top) 0 var(--spacing-body-caption-bottom) 0;
}

.--testimony {
  font-size: var(--text-body-testimony-size, rem(28px)); //
  line-height: var(--line-height-140);
  font-weight: var(--text-body-testimony-weight);
}

.--bold {
  font-size: var(--text-body-md-size);
  line-height: var(--line-height-140);
  font-weight: var(--text-body-md-weight-bold);
}

// // // // //
// EYEBROW  // 
// // // // //

.-eyebrow-small {
  font-size: var(--text-eyebrow-sm-size);
  line-height: var(--line-height-100);
  font-family: var(--font-family-eyebrow);
  font-weight: var(--text-eyebrow-sm-weight);
  letter-spacing: var(--letter-spacing-p3);
  text-transform: uppercase;
}

.-eyebrow-normal {
  font-size: var(--text-eyebrow-md-size);
  line-height: var(--line-height-100);
  font-family: var(--font-family-eyebrow);
  font-weight: var(--text-eyebrow-md-weight);
  letter-spacing: var(--letter-spacing-p3);
  text-transform: uppercase;
  margin: var(--spacing-eyebrow-standard) 0;
}

.-eyebrow-large {
  font-size: var(--text-eyebrow-lg-size);
  line-height: var(--line-height-100);
  font-family: var(--font-family-eyebrow);
  font-weight: var(--text-eyebrow-lg-weight);
  letter-spacing: var(--letter-spacing-p3);
  text-transform: uppercase;
  margin: var(--spacing-eyebrow-large) 0;
}

// // // // // //
//  THUMBNAIL  // 
// // // // // //

// PROPERTY
// price
.-thumbnail-property-price-sm {
  font-size: var(--text-thumbnail-property-price-sm-size);
  font-weight: var(--text-thumbnail-property-price-sm-weight, 400);
  line-height: var(--line-height-100);
}

.-thumbnail-property-price-md {
  font-size: var(--text-thumbnail-property-price-md-size);
  font-weight: var(--text-thumbnail-property-price-md-weight, 400);
  line-height: var(--line-height-100);
}


// type
.-thumbnail-property-type-sm {
  font-size: var(--text-thumbnail-property-type-sm-size);
  font-weight: var(--text-thumbnail-property-type-sm-weight, 400);
  line-height: var(--line-height-100);
}

.-thumbnail-property-type-md {
  font-size: var(--text-thumbnail-property-type-md-size);
  font-weight: var(--text-thumbnail-property-type-md-weight, 400);
  line-height: var(--line-height-100);
}

// address
.-thumbnail-property-address-sm {
  font-size: var(--text-thumbnail-property-address-sm-size);
  line-height: var(--line-height-100);
  font-weight: 400;
}

.-thumbnail-property-address-md {
  font-size: var(--text-thumbnail-property-address-md-size);
  line-height: var(--line-height-100);
  font-weight: 400;
}

// BROKER
// title 
.-thumbnail-broker-title-small {
  font-size: var(--text-thumbnail-broker-sm-title-small);
  line-height: var(--line-height-100);
  font-family: var(--font-family-meta);
}

.-thumbnail-broker-title-medium {
  font-size: var(--text-thumbnail-broker-md-title-medium);
  line-height: var(--line-height-100);
  font-family: var(--font-family-meta);
}

.-thumbnail-broker-title-large {
  font-size: var(--text-thumbnail-broker-lg-title-large);
  line-height: var(--line-height-100);
  font-family: var(--font-family-meta);

}

// Meta 
.-thumbnail-broker-meta-small {
  font-size: var(--text-thumbnail-broker-sm-meta-small);
  line-height: var(--line-height-100);
}

.-thumbnail-broker-meta-medium {
  font-size: var(--text-thumbnail-broker-md-meta-medium);
  line-height: var(--line-height-100);
}

.thumbnail-broker-meta-large {
  font-size: var(--text-thumbnail-broker-lg-meta-large);
  line-height: var(--line-height-100);
}

// Phone
.-thumbnail-broker-phone-small {
  font-size: var(--text-thumbnail-broker-sm-phone-small);
  line-height: var(--line-height-100);

}

.-thumbnail-broker-phone-medium {
  font-size: var(--text-thumbnail-broker-md-phone-medium);
  line-height: var(--line-height-100);
}

.-thumbnail-broker-phone-large {
  font-size: var(--text-thumbnail-broker-lg-phone-large);
  line-height: var(--line-height-100);
}

// BLOG
// Title
.-thumbnail-blog-title-sm {
  font-size: var(--text-thumbnail-blog-title-sm-size);
  font-weight: var(--text-thumbnail-blog-title-sm-weight);
  line-height: var(--line-height-100);
  font-family: var(--font-family-title);
}

.-thumbnail-blog-title-md {
  font-size: var(--text-thumbnail-blog-title-md-size);
  font-weight: var(--text-thumbnail-blog-title-md-weight);
  font-family: var(--font-family-title);
  line-height: var(--line-height-100);
  letter-spacing: var(--letter-spacing-n064);
}

.-thumbnail-blog-title-lg {
  font-size: var(--text-thumbnail-blog-title-lg-size);
  font-weight: var(--text-thumbnail-blog-title-lg-weight);
  line-height: var(--line-height-100);
  font-family: var(--font-family-title);

}

// Meta
.-thumbnail-blog-meta-sm {
  font-size: var(--text-thumbnail-blog-meta-sm-size);
  font-weight: var(--text-thumbnail-blog-meta-sm-weight);
  font-family: var(--font-family-meta);
  line-height: var(--line-height-100);
}

.-thumbnail-blog-meta-md {
  font-size: var(--text-thumbnail-blog-meta-md-size);
  font-weight: var(--text-thumbnail-blog-meta-md-weight);
  font-family: var(--font-family-meta);
  line-height: var(--line-height-100);
}

.-thumbnail-blog-meta-lg {
  font-size: var(--text-thumbnail-blog-meta-lg-size);
  font-weight: var(--text-thumbnail-blog-meta-lg-weight);
  font-family: var(--font-family-meta);
  line-height: var(--line-height-100);
}

// Excerpt
.-thumbnail-blog-excerpt-sm {
  line-height: var(--line-height-100);
}

.-thumbnail-blog-excerpt-md {
  font-size: var(--text-thumbnail-blog-excerpt-md-size);
  font-weight: var(--text-thumbnail-blog-excerpt-md-weight);
  line-height: var(--line-height-100);
}

.-thumbnail-blog-excerpt-lg {
  font-size: var(--text-thumbnail-blog-excerpt-lg-size);
  font-weight: var(--text-thumbnail-blog-excerpt-md-weight);
  line-height: var(--line-height-100);
}


// // // //
//  CTA  // 
// // // // 

.-cta-small {
  font-size: var(--text-cta-sm-size);
  line-height: var(--line-height-100);
  font-weight: var(--text-cta-sm-weight);
  font-family: var(--font-family-cta);
}

.-cta-medium {
  font-size: var(--text-cta-md-size);
  line-height: var(--line-height-100);
  font-weight: var(--text-cta-md-weight);
  font-family: var(--font-family-cta);
}

.-cta-large {
  font-size: var(--text-cta-lg-size);
  line-height: var(--line-height-100);
  font-weight: var(--text-cta-lg-weight);
  font-family: var(--font-family-cta);
}

// // // // //
//  TABLE   // 
// // // // //

.-table-accent {
  font-size: var(--text-table-accent-size);
  font-weight: var(--text-table-accent-weight);
  line-height: var(--line-height-100);
}

.-table-value {
  font-size: var(--text-table-value-size);
  font-weight: var(--text-table-value-weight);
  line-height: var(--line-height-100);
}

// // // // //
//  FORM    // 
// // // // //

.-form-label {
  font-size: var(--text-form-label-size);
  font-weight: var(--text-form-label-weight);
  line-height: var(--line-height-120);
}

.-form-select {
  font-size: var(--text-form-select-size);
  font-weight: var(--text-form-select-weight);
  line-height: var(--line-height-120);
}

.-form-placeholder {
  font-size: var(--text-form-placeholder-size);
  font-weight: var(--text-form-placeholder-weight);
  line-height: var(--line-height-120);
}

.-form-checkbox {
  font-size: var(--text-form-checkbox-size);
  font-weight: var(--text-form-checkbox-weight);
  line-height: var(--line-height-120);
}


// // // // //
//  HEADER  // 
// // // // //

.-header-menu {
  font-size: var(--text-nav-header-primary-size);
  font-weight: var(--text-nav-header-primary-weight);
  font-family: var(--font-family-nav);
}

.-header-sous-menu {
  font-size: var(--text-nav-header-secondary-size);
  font-weight: var(--text-nav-header-secondary-weight);
  font-family: var(--font-family-nav);
}

// // // // //
//  FOOTER  // 
// // // // //

.-footer-title {
  font-size: var(--text-nav-footer-primary-title);
  font-weight: var(--text-nav-footer-primary-menu); // might check with variable name in figma, dont make sense
  font-family: var(--font-family-title);
}

.-footer-menu {
  font-size: var(--text-nav-footer-primary-menu);
  font-weight: var(--text-nav-footer-secondary-menu); // / might check with variable name in figma, dont make sense
  font-family: var(--font-family-nav);
}