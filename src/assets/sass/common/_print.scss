/**
  * This file contains code overriding style for printing purposes
  */

@media print {
  @page {
    size: auto;
    margin: 0.5cm;
  }

  html,
  body {
    width: 210mm;
    height: 297mm;
  }

  * {
    /* Black prints faster: h5bp.com/s */
    box-shadow: none !important;
    text-shadow: none !important;
  }

  pre,
  blockquote {
    border: 1px solid #999;
    page-break-inside: avoid;
  }

  thead {
    display: table-header-group;
  }

  img {
    display: block;
    max-width: 100% !important;
    page-break-inside: avoid;
  }

  a,
  a:visited {
    text-decoration: underline;
  }

  a:after,
  a[href^='javascript:']:after,
  a[href^='#']:after {
    content: '';
  }

  p,
  h1,
  h2,
  h3,
  h4 {
    orphans: 3;
    widows: 3;
  }

  h1,
  h2,
  h3 {
    page-break-after: avoid;
  }

  hr {
    margin: 20px 0 !important;
  }

  ul {
    list-style: square !important;
    padding: 0 !important;
    margin-left: 10px !important;

    li {
      padding: 0 !important;
      font-size: 12px !important;
      &:before { display: none; }
    }
  }

  form {
    display: none;
  }

  .container {
    width: 100%;
    max-width: 100%;
  }

  .primary-menu,
  .secondary-menu,
  .menu-mobile-ctn,
  .main-footer,
  .pretty-embed,
  .footer-cta {
    display: none !important;
  }
}
