/* Masquer les éléments avec data-scroll pendant le loading */
[data-scroll] {
    opacity: 0;
    transform: translateY(20px);
    transition: transform 1s $default-easing, opacity 1s $default-easing;

    &.-show {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Classes utilitaires pour les animations */
.fade-in {
    opacity: 1;
    transform: translateY(0);
    transition: transform 1s $default-easing, opacity 1s $default-easing;

    &:not(.-show) {
        opacity: 0;
    }
}

.slide-up {
    opacity: 1;
    transform: translateY(0);
    transition: transform 1s $default-easing, opacity 1s $default-easing;

    &:not(.-show) {
        opacity: 0;
        transform: translateY(50px);
    }
}

.slide-left {
    opacity: 1;
    transform: translateX(0);
    transition: transform 1s $default-easing, opacity 1s $default-easing;

    &:not(.-show) {
        opacity: 0;
        transform: translateX(30px);
    }
}

.slide-right {
    opacity: 1;
    transform: translateX(0);
    transition: transform 1s $default-easing, opacity 1s $default-easing;

    &:not(.-show) {
        opacity: 0;
        transform: translateX(-30px);
    }
}

.scale-in {
    opacity: 1;
    transform: scale(1);
    transition: transform 1s $default-easing, opacity 1s $default-easing;

    &:not(.-show) {
        opacity: 0;
        transform: scale(0.9);
    }
}