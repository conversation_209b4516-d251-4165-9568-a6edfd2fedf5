body {
    font-family: var(--font-family-body);
    font-size: var(--font-size-medium);
    line-height: var(--line-height-100);
    color: var(--color-text-body-primary);
    background-color: var(--color-bg-default);

    // Fix weird macos rendering
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;

    &.-no-scroll{
        margin: 0;
        height: 100%;
        overflow: hidden;
    }
}

.container{
    @include container;

    &.-narrow, .container-narrow {
        margin-left: grid-space(math.div(2,12), 1);
        margin-right: grid-space(math.div(2,12), 1);

        @media (max-width: $tablet-lg) {
            margin-left: unset;
            margin-right: unset;
        }
    }

    &.-small, .container-small {
        margin-left: grid-space(math.div(1,12), 1);
        margin-right: grid-space(math.div(1,12), 1);

        @media (max-width: $tablet-lg) {
            margin-left: unset;
            margin-right: unset;
        }
    }

    &.-wide, .container-wide {
        --container-margin: 20px;
    }
}

.main-content{
    min-height: vh(100);
}

// GSAP ScrollSmoother styles
#smooth-wrapper {
    overflow: hidden;
}

#smooth-content {
    overflow: visible;
    width: 100%;
}

// Optimization for performance
.smooth-scroll-optimized {
    will-change: transform;
    transform: translateZ(0);
}

// deactivate smooth scroll if needed
.no-smooth {
    will-change: auto;
    transform: none;
}