$sliderContentBg: var(--color-bg);

.slider-content-cpn{
    padding: 100px 0;
    background-color: $sliderContentBg;
    
    .title{
        text-align: center;
    }

    .text {
        grid-column-start: 7;
    }

    .swipers-ctn{
        position: relative;
        min-height: 500px;
        align-items: center;
        overflow: hidden;
    }

    .swiper-content-img{
        position: absolute;
        max-width: 60%;

        .swiper-container{

            .swiper-slide{
                width: 100% !important;
                img{
                    width: 100%;
                    max-width: 100%;
                }
                
            }
        }

        @media (max-width: 992px) {
            max-width: 100%;
        }
        
    }

    .swiper-content{
        background-color: white;
        padding: 50px 50px 30px;
        z-index: 1;
        position: relative;

        .swiper-slide{
            width: 100% !important;
            .head{
                display: flex;
                align-items: center;
                justify-content: space-between;
                flex-wrap: wrap;

                .title{
                    font-size: 22px;
                    line-height: 27px;
                    text-align: left;
                    width: calc(100% - 100px);
                }
            }

            .content{
                color: var(--color-text);
                font-size: 14px;
                line-height: 24px;
            }
        }

        .swiper-nav{
            text-align: right;
            margin-top: 20px;
        }

        .swiper-btn{
            width: 50px;
            height: 50px;
            background: var(--primary-color);
            border-radius: 50%;
            top: initial;
            margin-top: 0;
            position: relative;
            display: inline-block;
            left: initial;
            right: initial;
    
            &:before {
                position: absolute;
                font-family: 'icomoon' !important;
                speak: none;
                font-style: normal;
                font-weight: normal;
                font-variant: normal;
                text-transform: none;
                line-height: 1;
                font-size: 20px;
                color: $propertyHeroSettingsColor;
                left: 50%;
                top: 50%;
                transform: translate(-50%, -50%);
    
            }
    
            &.swiper-button-prev {
    
                &:before {
                    content: "\e909";
                }
            }
    
            &.swiper-button-next {

                margin-left: 10px;

                &:before {
                    content: "\e90a";
                }
            }

            &.swiper-button-disabled{
                opacity: 0.5 !important;
            }
        }

    }

    @media(max-width: 992px){

        .swipers-ctn{
            display: block;
        }

        .swiper-content-img{
            position: relative;

            .swiper-container{
                max-width: 100%;
            }
        }
    }

    @media(max-width: 425px){

        padding: 60px 0;
        
        .swiper-content{
            padding: 20px;

            .swiper-slide{
                .head {
                    align-items: flex-end;
                    flex-direction: column-reverse;

                    .title{
                        margin-bottom: 0;
                        width: 100%;
                    }

                    .step{
                        margin: 20px 0 0;
                    }
                }                
            }

            .swiper-nav{
                margin-bottom: 10px;
                .swiper-btn{
                    width: 35px;
                    height: 35px;

                    &:before{
                        font-size: 16px;
                    }

                }
            }
        }
    }
}