.reviews-slider-cpn {
	background-color: #F1ECE1;
	padding: 100px 0 170px;

	@media (max-width: 992px) {
		padding: 50px 0 100px;
	}

	.container { 
		position: relative; 
	}

	.title {
		gap: 20px;

		&:before, &:after {
			content: "";
			display: inline-block;
			width: 60px;
			height: 1px;
			background-color: $black;

			@media (max-width: 576px) {
				display: none;
			}
		}
	}

	.swiper-container {
		overflow: hidden;
	}

	.review {
		background-color: $white;
		padding: 32px;
		width: 33.33%;

		&-header {
			display: flex;
			align-items: center;
			margin-bottom: 20px;
			gap: 20px;

			&-left {
				display: flex;
				align-items: center;

				.reviewer-image {
					width: 80px;
					height: 80px;
					border-radius: 100px;
				}
			}

			&-right {
				.reviewer-name {
					font-size: 18px;
					font-style: normal;
					font-weight: 500;
					line-height: normal;
					margin-bottom: 8px;
				}

				.reviewer-rating {
					display: flex;
					align-items: center;
					margin-bottom: 8px;

					.rating-stars {
						display: flex;

						.fa-star {
							color: #ccc;
							margin-right: 2px;

							&.checked {
								color: #ffcc00;
							}
						}
					}

					.rating-value {
						font-size: 18px;
					}
				}

				.reviewer-date {
					color: #777;
					font-size: 13px;
					font-style: normal;
					font-weight: 400;
					line-height: 19.6px;
				}
			}
		}

		&-body {

			.review-text {
				color: var(--color-text);
				font-size: 15px;
				font-style: normal;
				font-weight: 400;
				line-height: 24px;
			}
		}
	}

	.swiper-btn {
		background: none;
		width: 18px;
		height: 30px;
		transform: translate(0%, 50%);
		margin-top: 0;

		&:before {
			position: absolute;
			font-family: 'icomoon' !important;
			speak: none;
			font-style: normal;
			font-weight: normal;
			font-variant: normal;
			text-transform: none;
			line-height: 1;
			font-size: 28px;
			color: $black;
			left: 50%;
			top: 50%;
			transform: translate(-50%, -50%);
		}

		@media (max-width: 992px) {
			display: none;
		}

		&.swiper-button-prev {

			&:before {
				content: "\e909";
			}
		}

		&.swiper-button-next {

			&:before {
				content: "\e90a";
			}
		}
	}

	.swiper-pagination {
		bottom: -40px;

		.swiper-pagination-bullet {
			margin-right: 10px;
			background: rgba(0, 0, 0, 0.2);
			width: 11px;
			height: 11px;

			&-active {
				background: $black;
			}

			&:last-child {
				margin-right: 0;
			}
		}
	}
}

