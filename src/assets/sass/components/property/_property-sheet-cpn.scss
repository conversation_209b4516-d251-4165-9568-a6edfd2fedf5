/*
	Property Sheet Component
*/
$propertySheetTableBGOdd: var(--color-bg);
$propertySheetTableBGEven: white;
$propertySheetTableTextSize: 14px;
$propertySheetTableTextLineHeight: 19px;
$propertySheetTableTextColor: $light-grey;
$propertySheetTableTextEmphaseColor: #000000;

$propertySheetSpacing: 60px;

.property-sheet {
	padding-top: $propertySheetSpacing;
	background-color: $white;
	@include clearfix;

	@media (max-width: 767px) {
		padding-top: 44px;
	}

	.property-sheet-content {
		padding-left: 0;

		@media (max-width: 992px) {
			width: 100%;
			padding: 0;
		}
	}

	.property-mobile-sidebar {
		margin-top: 40px;
		display: none;

		@media (max-width: 992px) {
			display: block;
		}
	}

	.property-sheet-sidebar {
		padding-right: 0;
		position: sticky;
		top: 30px;
		margin-bottom: 30px;

		@media (max-width: 992px) {
			display: none;
		}
	}

	.table-ctn-dual {
		display: flex;
		align-items: flex-start;
		justify-content: space-between;
		flex-wrap: wrap;

		@media (max-width: 1200px) {
			align-items: flex-start;
		}
	}

	.table-ctn {
		.detail {
			display: flex;
		}

		.table-row {
			padding: 10px 20px;

			&:nth-child(odd) {
				background-color: $propertySheetTableBGOdd;
			}

			&:nth-child(even) {
				background-color: $propertySheetTableBGEven;
			}

			> p {
				padding: 0;
				margin-top: 0;
				margin-bottom: 0;
				color: $propertySheetTableTextColor;
				font-family: var(--font-secondary);
				font-size: $propertySheetTableTextSize;
				line-height: $propertySheetTableTextLineHeight;

				&:last-child {
					text-align: right;
				}

				&.date {
					text-align: left;
					display: flex;
    				align-items: center;

    				i {
    					font-size: 20px;
    				}

					span {
						margin-left: 15px;
						display: inline-block;
					}
				}
			}

			span {
				display: block;
				color: $propertySheetTableTextEmphaseColor;
				font-family: var(--font-secondary);
				font-size: $propertySheetTableTextSize;
				font-weight: 600;
				line-height: $propertySheetTableTextLineHeight;
			}
		}

		&.-dual {
			margin-right: 20px;
			width: calc(100% * 1/2 - 10px);

			@media (max-width: 767px) {
				width: 100%;
				margin-right: 0;
			}

			&:last-child {
				margin-right: 0;

				@media (max-width: 767px) {
					.table-row {
						&:nth-child(odd) {
							background-color: $propertySheetTableBGEven;
						}

						&:nth-child(even) {
							background-color: $propertySheetTableBGOdd;
						}
					}
				}
			}
		}

		&.-between {
			.table-row {
				justify-content: space-between;
			}
		}

		&.-has-button {
			position: relative;

			.table-wrap {
				max-height: 300px;
				overflow: hidden;
				transition: $transition;
			}

			.gradient {
				position: absolute;
				bottom: 0;
				left: 0;
				right: 0;
				height: 113px;
				width: 100%;
				background: linear-gradient(180deg, rgba(255,255,255,0) 0%, #FFFFFF 100%);
				transition: $transition;
			}

			.small-link {
				margin-top: 22px;
				display: block;
				transition: $transition;

				i {
					padding: 5px;
					position: relative;
					top: -2px;

          &::before {
            position: relative;
            top: 3px;
            content: '+';
            font-size: 32px;
            font-weight: 400;
          }
				}
			}
		}

		&.-opened {
			.table-wrap {
				max-height: 5000px;
			}

			.gradient {
				opacity: 0;
			}

			.small-link {
				opacity: 0;
			}
		}

		&.-center {
			.table-row {
				align-items: center;
			}
		}
	}

	.text-wrap {
		margin-bottom: 20px;
		color: var(--color-text);
		font-family: var(--font-secondary);
		font-size: var(--font-size-cta-small);
		line-height: var(--line-height-cta-small);
	}

	.dual-wrap {
		display: flex;
		align-items: flex-start;
		justify-content: space-between;
		flex-wrap: wrap;

		@media (max-width: 767px) {
			display: block;
		}

		.dual-div {
			margin-right: 20px;
			width: calc(100% * 1/2 - 10px);

			@media (max-width: 767px) {
				width: 100%;
			}

			&:last-child {
				margin-right: 0;
			}
		}
	}
}

.propertygroup-sheet {
	padding-bottom: 0px;
	background-color: $white;

	.propertygroup-description {

		.subtext {
			color: var(--color-text);
			font-family: var(--font-secondary);
			font-size: var(--font-size-small);
			line-height: var(--line-height-small);
		}
	}

	.property-sheet-sidebar {
		position: relative;
		top: 0;
	}
}
