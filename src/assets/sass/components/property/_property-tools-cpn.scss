/*
	Property Tools Component
*/
$propertyToolsTabLabelSize: 16px;
$propertyToolsTabLabelColor: #000000;
$propertyToolsTabLabelBG: #EEEEEE;
$propertyToolsTabLabelLineHeight: 20px;
$propertyToolsTabLabelWeight: bold;
$propertyToolsTabLabelPadding: 16px;
$propertyToolsTabLabelTransform: uppercase;

$propertyToolsStatsBorder: 1px solid #D8D8D8;

$propertyToolsFormLabelColor: #000000;
$propertyToolsFormLabelSize: 14px;
$propertyToolsFormLabelLineHeight: 19px;

$propertyToolsStatsNameSize: 14px;
$propertyToolsStatsNameLineHeight: 19px;
$propertyToolsStatsNameColor: $light-grey;
$propertyToolsStatsValueSize: 18px;
$propertyToolsStatsValueLineHeight: 24px;
$propertyToolsStatsValueColor: #000000;
$propertyToolsStatsValueWeight: 600;

.property-tools-cpn {
	padding-top: 80px;
	padding-bottom: 50px;

	.tabs-ctn {
		@include clearfix;

		&.-width-3{
			.tab-label {
				width: calc(100% / 3);

				&.-no-border-2{
					border-right: 2px solid var(--primary-color);
				}
			}
		}

		input:checked + label {
			background: white;
			border-top: 2px solid var(--primary-color);
			border-bottom: none;

			@media (max-width: 767px) {
				width: 100%;
				float: none;
				border: 2px solid var(--primary-color) !important;
			}
		}

		.tab-label {
			position: relative;
			float: left;
			display: inline-block;
			padding: $propertyToolsTabLabelPadding;
			width: calc(100% / 2);
			color: $propertyToolsTabLabelColor;
			font-size: $propertyToolsTabLabelSize;
			font-weight: $propertyToolsTabLabelWeight;
			line-height: $propertyToolsTabLabelLineHeight;
			text-transform: $propertyToolsTabLabelTransform;
			text-align: center;
			background-color: $propertyToolsTabLabelBG;
			border-bottom: 2px solid var(--primary-color);
			border-right: 2px solid var(--primary-color);
			height: 74px;
			display: flex;
			align-items: center;
			justify-content: center;

			&.-no-border,&.-no-border-2 {
				border-right: none;
			}

			@media (max-width: 767px) {
				width: 100% !important;
				float: none;
				border: none !important;

				.icon-round-dollar{
					padding-right: 15px;
				}
			}


			.icon-round-dollar{
				color: var(--primary-color);
				font-size: 30px;
			}
		}

		#tab-input1:checked ~ #tab-tool1,
		#tab-input2:checked ~ #tab-tool2,
		#tab-input3:checked ~ #tab-tool3 {
			display: block;
		}

		.tab-content {
			display: none;
			clear: both;

			.dual-wrap {
				padding-top: 10px;
				display: flex;
				align-items: center;
				justify-content: space-between;
				flex-wrap: wrap;

				@media (max-width: 767px) {
					display: block;
				}

				.stats-wrap, .form-payments {
					width: 280px;

					@media (max-width: 767px) {
						width: 100%;
					}
				}

				.graph-ctn {
					padding-top: 20px;
					width: calc(100% - 280px);

					@media (max-width: 767px) {
						width: 100%;
					}
				}
			}

			.stats {
				display: flex;
				align-items: center;
				justify-content: space-between;
				max-width: 280px;
				border-bottom: $propertyToolsStatsBorder;

				&:last-child, &.-no-border {
					border: none;
				}

				&.-total{
					border-top: solid 2px black;

					.name{
						color: black;
						font-weight: 700;
					}
				}

				@media (max-width: 767px) {
					max-width: none;
				}

				p {
					margin: 30px 0;
				}

				.name {
					color: $propertyToolsStatsNameColor;
					font-family: var(--font-secondary);
					font-size: $propertyToolsStatsNameSize;
					line-height: $propertyToolsStatsNameLineHeight;
				}

				.value {
					color: $propertyToolsStatsValueColor;
					font-family: var(--font-secondary);
					font-size: $propertyToolsStatsValueSize;
					font-weight: $propertyToolsStatsValueWeight;
					line-height: $propertyToolsStatsValueLineHeight;
					text-align: right;
				}
			}

			.form-payments {
				padding-top: 20px;

				.input-ctn {
					margin-bottom: 20px;
				}

				label {
					display: block;
					margin-bottom: 10px;
					color: $propertyToolsFormLabelColor;
					font-family: var(--font-secondary);
					font-size: $propertyToolsFormLabelSize;
					line-height: $propertyToolsFormLabelLineHeight;
				}
			}

			.value-ctn {
				display: flex;
				align-items: center;
				justify-content: space-between;

				p {
					color: $propertyToolsStatsNameColor;
					font-family: var(--font-secondary);
					font-size: $propertyToolsStatsNameSize;
					line-height: $propertyToolsStatsNameLineHeight;
				}

				.value {
					color: $propertyToolsStatsValueColor;
					font-family: var(--font-secondary);
					font-size: $propertyToolsStatsValueSize;
					font-weight: $propertyToolsStatsValueWeight;
					line-height: $propertyToolsStatsValueLineHeight;
				}
			}
		}
	}

	.ng-select.ng-select-single .ng-select-container {
		height: 42px;

		.ng-value-container {
			height: 42px;
		}
	}
}