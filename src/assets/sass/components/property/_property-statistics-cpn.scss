
$propertyStatisticsTitleBgColor: var(--primary-color);
$propertyStatisticsTitleColor: $white;

$propertyStatisticsTableRowBg: var(--color-bg);
$propertyStatisticsTableRowColor: var(--primary-color);
$propertyStatisticsTableRowBorder: solid 1px #dddddd;
$propertyStatisticsTableRowLabelColor: $light-grey;
$propertyStatisticsTableRowLabelSize: 14px;
$propertyStatisticsTableRowLabelHeight: 19px;

$propertyStatisticsEvaluationBG: var(--color-bg);

.property-statistics-cpn{
    background-color: $white;
    padding-top: 70px;
    border-top: solid 1px #E7E7E7;
    margin-top: 60px;

    .title-bg{
        margin-top: 0;
        display: inline-block;
        text-transform: uppercase;
        font-weight: 700;
        padding: 15px 30px;
        background-color: $propertyStatisticsTitleBgColor;
        color: $propertyStatisticsTitleColor;
    }

    .table-ctn{
        margin-bottom: 30px;
        .table-row {
            margin: 0;
			padding: 10px 20px;
			display: flex;
            align-items: flex-start;
            justify-content: space-between;
            border-top: $propertyStatisticsTableRowBorder;

            &:nth-child(2){
                border: 0;
            }

            &.-head{
                background-color: $propertyStatisticsTableRowBg;
                border: 0;

                & > p{
                    color: $propertyStatisticsTableRowColor;
                    text-transform: uppercase;
                    font-weight: 700;
                }
                
            }

            &.-mutation{
                flex-wrap: wrap;
                border: 0;

                div{
                    width: 100%;
                    margin-top: 15px;

                    p{
                        color: $propertyStatisticsTableRowLabelColor;
                        font-size: 14px;
                        line-height: 20px;
                        margin: 0;
                    }

                    a {
                        color: var(--primary-color);
                        transition: all ease 0.2s;

                        &:hover {
                            opacity: 0.7;
                        }
                    }
                }
            }

            > p {
				padding: 0;
				margin-top: 0;
                margin-bottom: 0;
                font-family: var(--font-secondary);
                color: $propertyStatisticsTableRowLabelColor;
                font-size: $propertyStatisticsTableRowLabelSize;
                line-height: $propertyStatisticsTableRowLabelHeight;

				&:last-child {
					text-align: right;
                }
                
                span{
                    color: $black;
                    font-weight: 600;

                    &.icon{
                        font-weight: 400;
                        color: #A2A5AA;
                        margin-left: 7px;
                        vertical-align: middle;
                    }
                }
			}
        }
    }

    .form-container{
        background-color: $propertyStatisticsEvaluationBG;
        padding: 50px 30px;

        form {
            .input-ctn{
                label{
                    font-weight: 700;
                }
            }

            .ng-select.ng-select-single .ng-select-container{
                height: 42px;
            }
        }

        .value-ctn{
            padding-top: 20px;
            border-top: $propertyStatisticsTableRowBorder;
            display: flex;
            justify-content: space-between;
            align-items: center;

            p{
                font-weight: 700;
                font-size: 15px;
                margin:0;

                &.value{
                    font-size: 22px;
                }
            }
        }
    }

    @media (max-width:992px) {
        .title-bg{
            font-size: 18px;
            line-height: 24px;
        }

        .table-ctn{
            .table-row {
                &.-head{
                    p{
                        font-size: 16px;
                        line-height: 26px;
                    }
                }
            }
        }

        .form-container{
            form {
                .input-ctn{
                    label{
                        font-size: 15px;
                        line-height: 24px;
                    }
                }
            }
        }
    }

    @media (max-width:425px) {
        padding-top: 50px;
        margin-top: 40px;
    }
}