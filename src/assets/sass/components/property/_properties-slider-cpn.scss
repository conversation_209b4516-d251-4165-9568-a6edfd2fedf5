.properties-slider-cpn {
    padding: 100px 0 80px;
  
    .container { position: relative; }
  
    h2 { margin-top: 0; }
  
    .small-link {
      position: absolute;
      right: 170px;
      top: 17px;
    }
  
    .nav-ctn {
      position: absolute;
      top: 0;
      right: 15px;
      @include flex();
      gap: 10px;
  
      a {
        min-width: auto;
        height: 50px;
        padding: 13px;
        border-radius: 8px;
  
        i { 
            color: var(--primary-color);
            font-size: 20px; 
        }
      }
    }
  
    @media (max-width: 991px) {
      .small-link { 
        position: relative;
        right: auto;
        top: 20px;
      }
    }
  
    @media (max-width: 550px) {
      padding: 60px 0;
      
      .nav-ctn {
        display: none;
      }
    }
  }