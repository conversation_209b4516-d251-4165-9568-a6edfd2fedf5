.properties-sold-map-cpn {
	overflow: hidden;
	$backgroundBannerTitle : white;

	.properties-sold-map-ctn {
		background-color: $backgroundBannerTitle;
		text-align: center;
	}

	.map {
		width: 100%;
		min-height: 600px;

		@media (max-width: 600px) {
			min-height: 400px;
		}
	}

	#map {
		transition: $transition;

		&.open{
			width: 100%;
		}

		@media (max-width: 600px) {
			max-height: 400px;
		}

		@media (max-width: 992px) {
			position: relative;
			top: 0;
			height: 800px;
		}

		.mapbox-logo {
			display: none;
		}

		.marker-cluster-small {
			background-color: rgba(var(--primary-color), 0.6);
		}

		.marker-cluster-small div {
			background-color: rgba(var(--primary-color), 0.6);
		}

		.marker-cluster-medium {
			background-color: rgba(var(--primary-color), 0.6);
		}

		.marker-cluster-medium div {
			background-color: rgba(var(--primary-color), 0.6);
		}

		.marker-cluster-large {
			background-color: rgba(var(--primary-color), 0.6);
		}
		.marker-cluster-large div {
			background-color: rgba(var(--primary-color), 0.6);
		}

		.marker-cluster {
			background-clip: padding-box;
			border-radius: 20px;
		}
		.marker-cluster div {
			width: 30px;
			height: 30px;
			margin-left: 5px;
			margin-top: 5px;

			text-align: center;
			border-radius: 15px;
			font-family: var(--font-secondary);
			font-size: var(--font-size-cta-small);
			font-weight: 800;
			color: white;
		}
		.marker-cluster span {
			line-height: 30px;
		}
	}
}
