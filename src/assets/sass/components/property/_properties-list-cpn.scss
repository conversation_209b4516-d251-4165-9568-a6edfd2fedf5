/*
	Properties List
*/

$propertiesListBG: var(--color-bg);

.properties-list-cpn {
	background-color: $propertiesListBG;
	
	.cpn-head {
		margin-bottom: 30px;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
}

.properties-list-ctn {
	display: flex;
	flex-flow: row wrap;
	justify-content: space-between;
	gap: 20px 0;

	.small-link {
		padding-right: 0px;
	}
}

.properties {
	position: relative;
	// margin-right: 20px;
	// margin-bottom: 20px;
	// width: calc(100% * 1/3 - 14px);
	overflow: hidden;
	cursor: pointer;

	//If Desktop apply this
	@media (min-width: 992px) {
		// &:nth-child(3n) {
		// 	margin-right: 0;
		// }
	}

	@media (max-width: 992px) {
		// width: calc(100% * 1/2 - 10px);

		&:nth-child(2n) {
			// margin-right: 0;
		}
	}

	@media (max-width: 767px) {
		// width: 100%;
		// margin-right: 0 !important;
	}

	.img-ctn {
		border-radius: 2px;

		img{
			width: 100%;
			height: auto;
		}
	}

	&:hover {
		.filter {
			background-color: rgba(0,0,0,0.3);
			opacity: 1;
		}

		.properties-info {
			max-height: 115px;

			.more-info {
				max-height: 250px;
				opacity: 1;
			}
		}
	}

	.gradiant {
		position: absolute;
		height: 50%;
		width: 100%;
		left: 0;
		right: 0;
		bottom:0;
		top: 50%;
		background: linear-gradient(180deg, rgba(0,0,0,0) 0%, #000000 100%);
	}

	.filter {
		position: absolute;
		height: 100%;
		width: 100%;
		left: 0;
		right: 0;
		top: 0;
		bottom:0;
		transition: $transition;
		transition-duration: 0.3s;
		background-color: rgba(0,0,0,0);

		@media (max-width: 992px) {
			opacity: 0;
			background-color: rgba(0,0,0,0.3);
		}
	}

	.icon-tab {
		position: absolute;
		top: 10px;
		right: 10px;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.play-btn {
		position: relative;
		margin-left: 10px;
		width: 35px;
		height: 35px;
		border-radius: 100%;
		background-color: rgba(0,0,0,0.5);
		cursor: pointer;
		color: white;
		font-size: 20px;
		display: flex;
		justify-content: center;
		align-items: center;

		.icon-play {
			width: 16px;
		}
	}

	.player360 {
		position: relative;
		margin-left: 10px;
		width: 35px;
		height: 35px;
		border-radius: 100%;
		background-color: rgba(0,0,0,0.5);
		cursor: pointer;
		color: white;
		font-size: 20px;
		display: flex;
		justify-content: center;
		align-items: center;

		a {
			color: white;
		}
	}

	.properties-info {
		position: absolute;
		z-index: 4;
		bottom: 0;
		left: 0;
		right: 0;
		transition: $transition;
		// max-height: 200px;

		@media (max-width: 992px) {
			// max-height: 250px !important;
		}

		p {
			margin: 0;
			color: white;
			font-family: var(--font-secondary);
		}

		.bloc-head {
			padding: 0 20px;
			padding-bottom: 15px;
			display: flex;
			justify-content: space-between;
			align-items: center;

			.price {
				font-size: 16px;
				font-weight: bold;
				line-height: 27px;
				white-space: nowrap;

				.small-tps{
					font-size: 10px;
					position: relative;
    				top: -5px;
				}
			}

			.type {
				font-size: 12px;
				font-weight: 600;
				line-height: 18px;
				text-align: right;
			}
		}

		.more-info {
			position: relative;
			top: -15px;
			padding: 0 20px;
			overflow: hidden;
			max-height: 0;
			text-align: left;
			transition: $transition;
			opacity: 0;

			@media (max-width: 992px) {
				max-height: 250px;
				opacity: 1;
			}

			.address, .location {
				font-size: 13px;
				color: white;
			}

			.align {
				display: flex;
				justify-content: space-between;
				align-items: center;

				.numbers {
					display: flex;

					.icon-ctn {
						padding-left: 32px;
						display: flex;
						align-items: center;

						i {
							// font-size: $primaryTextSize;
							color: white;
						}

						p {
							padding-left: 12px;
							font-size: var(--font-size-cta-small);
							font-weight: 600;
							line-height: 19px;
						}
					}
				}
			}
		}
	}
}

.containerBandeau {
	display: flex;
	flex-wrap: wrap;
	gap: 2px;
	position: absolute;
	z-index: 5;
	top: 15px;
	left: 15px;

	.properties-label {
		margin-right: 5px;
		padding: 5px 24px;
		border-radius: 3px;
		font-family: var(--font-secondary);
		@include fontSize(14px, 19px, 700);
		color: $white;
		text-transform: uppercase;
	
		&.-label-sold { background: $red; }
		&.-label-rental-possible { background: var(--primary-color); }
		&.-label-new { background: $yellow; }
		
		&.-label-price-changed { 
			background: var(--primary-color); 
			display: flex;
			align-items: center;

			i {
				margin-right: 6px;
				font-size: 16px;
				vertical-align: middle;
			}

			&.-down { background: $green; }
			&.-up { background: #D0103A; }
		}

		&.-label-view {
			// padding: 4px 12px;
			background: var(--primary-color);
			// @include fontSize(13px, 18px, 600);
			text-transform: none;
		}
	}	
}
