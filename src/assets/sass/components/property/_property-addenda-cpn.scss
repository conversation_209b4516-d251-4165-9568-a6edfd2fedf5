/*
	Property Addenda Component
*/

.property-addenda-cpn {
	.section-text {
		max-height: 310px;
		overflow: hidden;
		transition: $transition;
	}

	.text-wrap {
		position: relative;

		&.-opened {
			.section-text {
				max-height: 5000px;
			}

			.gradient {
				opacity: 0;
			}

			a {
				opacity: 0;
			}
		}
	}

  .small-link i::before {
    position: relative;
    top: 3px;
    content: '+';
    font-size: 32px;
    font-weight: 400;
  }

	.gradient {
		position: absolute;
		bottom: 0;
		left: 0;
		right: 0;
		height: 113px;
		width: 100%;
		background: linear-gradient(180deg, rgba(255,255,255,0) 0%, #FFFFFF 100%);
		transition: $transition;
	}
}