/*
Property Hero Component
*/

$propertyHeroPriceSize: 32px;
$propertyHeroPriceLineHeight: 39px;

$propertyHeroButtonHeight: 80px;
$propertyHeroButtonWidth: 80px;
$propertyHeroButtonHeightMobile: 40px;
$propertyHeroButtonWidthMobile: 40px;
$propertyHeroBackground: rgba(0,0,0,0.3);
$propertyHeroRadius: 40px;
$propertyHeroLeftButtonPosition: 30px;
$propertyHeroLeftButtonPositionMobile: 15px;
$propertyHeroRightButtonPosition: 30px;
$propertyHeroRightButtonPositionMobile: 15px;
$propertyHeroLeftArrowSVG: url('assets/images/SVG/UI/arrow-left.svg');
$propertyHeroRightArrowSVG: url('assets/images/SVG/UI/arrow-right.svg');
$propertyHeroArrowHeight: 28px;
$propertyHeroArrowWeight: 17px;

$propertyHeroHomeBtnArrow: url('assets/images/SVG/UI/arrow-right.svg');
$propertyHeroHomeBtnArrowWeight: 11px;
$propertyHeroHomeBtnArrowHeight: 18px;

$propertyHeroPricetagBackground: var(--primary-color);
$propertyHeroSettingsBackground: rgba(45, 47, 49, .9);
$propertyHeroSettingsBorderRadius: 100px;
$propertyHeroSettingsColor: white;
$propertyHeroSettingsButtonSize: 20px;
$propertyHeroSettingsSeeMoreSize: 14px;


.property-hero-cpn {
	position: relative;
	opacity: 1;
	transition: $transition;

	@media (min-width: 1024px) {
		max-height: 650px;
		min-height: 650px;
	}

	.title-ctn {
		position: absolute;
		z-index: 99;
	}

	.swiper {
		position: unset;
	}

	.swiper-slide {
		max-height: 650px;

		@media only screen and (min-device-width : 320px) and (max-device-width : 920px) and (orientation : landscape) {
			max-height: 350px;
		}

		a {
			height: 100%;
			width: 100%;
			pointer-events: none;
		}

		img {
			width: 100%;
			object-fit: cover;
			object-position: center;
		}
	}

	.swiper-container {
		&:hover{
			cursor: zoom-in;
		}

		.swiper-slide {
			display: flex;
			align-items: center;
			justify-content: center;

			@media (max-width: 767px) {
				height: 440px;
			}

			a { height: 100%; }

			img {
				width: 100%;

				@media (max-width: 767px) {
					max-width: none;
					height: 100%;
					object-fit: cover;
				}
			}
		}
	}

	&.classic{
		.swiper-container {
			&:hover{
				cursor: default;
			}
		}
	}

	.price-tag {
		margin: 0;
		z-index: 5;
		color: $white;
		width: grid-space(math.div(1,3), 0);

		p {
			margin: 0;
			background: var(--secondary-color);
			&:first-child:not(:only-child) { border-bottom: 1px solid rgba(#fff, 0.2); }
		}

		.-inner {
			@include flex();
			@extend .-h2;
			color: $white;
			margin: 0;
			padding: 20px;
			text-align: center;
		}

		span {
			display: block;
			color: $white;
			font-family: var(--font-secondary);
			@include fontSize(14px, 14px);

			&.small-tps { margin-left: 10px; }
		}

		.under-tag {
			margin-top: -20px;
			padding-bottom: 10px;
			text-align: center;
			font-weight: 400;
			text-transform: uppercase;
			opacity: 0.7;
		}

		@media (max-width: 767px) {
			left: 0px;
			right: 0px;
			padding-left: 0;

			.-inner {
				padding-top: 10px;
				padding-bottom: 10px;
				@include fontSize(24px, 24px);

			}

			.under-tag {
				margin-top: -12px;
				padding-bottom: 2px;
				font-size: 12px;
			}
		}

		&.-sold {
			text-transform: uppercase;
			font-weight: bold;
			.-inner { background: $red; }
		}
	}

	.absolute-ctn {
		@include flex($justify: space-between);
		position: absolute;
		width: 100%;
		left: 0;
		right: 0;
		bottom: -1px;
		padding-left: var(--container-margin);
		padding-right: var(--container-margin);

		@media (max-width: 767px) {
			display: block;
			padding-left: 0;
			padding-right: 0;
		}
	}

	.button-ctn {
		display: flex;
		align-items: flex-end;
		gap: 10px;
		padding-left: 0;
		padding-bottom: 30px;

		@media (max-width: 767px) {
			gap: 10px;
			flex-direction: column;
			bottom: 0px;
			// left: 15px;
			position: relative;
			padding-bottom: 0;
		}
	}

	.see-more-list {
		padding: 0 25px 0 14px;
		border-radius: $propertyHeroSettingsBorderRadius;
		background-color: $propertyHeroSettingsBackground;
		cursor: pointer;
		height: fit-content;
		color: $white;
		z-index: 99;
		@include flex($gap: 0);

		.icon-camera {
			margin-right: 10px;
			font-size: 20px;
			position: relative;
			top: -3px;
		}

		p {
			display: inline-block;
			position: relative;
			font-family: var(--font-secondary);
			font-size: $propertyHeroSettingsSeeMoreSize;
			font-weight: 600;
			line-height: 19px;
			padding-left: 20px;


			&:before {
				@include pseudoElement();
				top: 0;
				left: 10px;
				height: 100%;
				width: 1px;
				background: white;
				opacity: .3;
			}
		}

		.swiper-pagination {
			display: inline-block;
			position: relative;
			bottom: 0;
			left: inherit;
			text-align: left;
			width: auto;
			color: $propertyHeroSettingsColor;
			font-family: var(--font-secondary);
			font-size: 12px;
			line-height: 17px;
		}

		.total-photos {
			@include fontSize(18px, 18px, 600);
		}
	}

	@media (max-width: 767px) {
		.see-more-list, .dropdown {
			margin-right: 10px;
		}
	}

	.dropdown {
		position: relative;
		color: white;
		cursor: pointer;
		z-index: 999;

		.options {
			@extend .-no-list-style;
			background-color: $propertyHeroSettingsBackground;
			position: absolute;
			width: 100%;
			font-size: $propertyHeroSettingsSeeMoreSize;
			font-weight: 600;
			line-height: 19px;
			margin: 0;
			padding: 0px 10px 12px 65px;
			border-radius: 0 0 25px 25px;

			li {
				margin: 6px 0;

				a {
					color: white;
				}
			}
		}
	}

	.visit-list {
		@include flex($gap: 0);
		position: relative;
		padding: 0 30px;
		border-radius: $propertyHeroSettingsBorderRadius;
		background-color: $propertyHeroSettingsBackground;
		z-index: 99;

		p {
			font-size: 14px;
			line-height: 19px;
			font-weight: 600;
		}

		&.dropdown-open {
			border-radius: 25px 25px 0 0 ;

			.icon-fill-play {
				transform: rotate(-90deg);
			}
		}

		.icon-house_360 {
			margin-right: 10px;
			font-size: 22px;
			left: 15px;
    		top: 13px;
		}

		.icon-fill-play {
			display: block;
			font-size: 8px;
			margin: 10px;
			right: 25px;
			top: 20px;
			transform: rotate(90deg);
			transition: 200ms;
		}

	}

	// .play-btn {
	// 	position: relative;
	// 	margin-left: 10px;
	// 	width: 50px;
	// 	height: 50px;
	// 	border-radius: $propertyHeroSettingsBorderRadius;
	// 	background-color: $propertyHeroSettingsBackground;
	// 	cursor: pointer;
	// 	color: $propertyHeroSettingsColor;
	// 	font-size: $propertyHeroSettingsButtonSize;
	// 	display: flex;
	// 	justify-content: center;
	// 	align-items: center;

	// 	a {
	// 		color: white;
	// 	}
	// }

	// .player360 {
	// 	position: relative;
	// 	margin-left: 10px;
	// 	width: 50px;
	// 	height: 50px;
	// 	border-radius: $propertyHeroSettingsBorderRadius;
	// 	background-color: $propertyHeroSettingsBackground;
	// 	cursor: pointer;
	// 	color: $propertyHeroSettingsColor;
	// 	font-size: $propertyHeroSettingsButtonSize;
	// 	display: flex;
	// 	justify-content: center;
	// 	align-items: center;

	// 	a {
	// 		color: white;
	// 	}
	// }

	.swiper-btn {
		width: $propertyHeroButtonWidth;
		height: $propertyHeroButtonHeight;
		background: $propertyHeroBackground;
		border-radius: $propertyHeroRadius;
		transform: translate(0%, -50%);
		margin-top: 0;

		@media (max-width:992px) {
			width: $propertyHeroButtonWidthMobile;
			height: $propertyHeroButtonHeightMobile;
		}

		&:before {
			position: absolute;
			font-family: 'icomoon' !important;
			speak: none;
			font-style: normal;
			font-weight: normal;
			font-variant: normal;
			text-transform: none;
			line-height: 1;
			font-size: 28px;
			color: $propertyHeroSettingsColor;
			left: 50%;
			top: 50%;
			transform: translate(-50%, -50%);

			@media (max-width:992px) {
				font-size: 20px;
			}
		}

		&.swiper-button-prev {
			left: $propertyHeroLeftButtonPosition;

			@media (max-width:992px) {
				left: $propertyHeroLeftButtonPositionMobile;
			}

			&:before {
				content: "\e909";
			}
		}

		&.swiper-button-next {
			right: $propertyHeroRightButtonPosition;

			@media (max-width:992px) {
				right: $propertyHeroRightButtonPositionMobile;
			}

			&:before {
				content: "\e90a";
			}
		}
	}

	// .container {
	// 	position: absolute;
	// 	height: 20%;
	// 	left: 50%;
	// 	bottom: 0;
	// 	transform: translate(-50%, 0%);

	// 	@media (max-width: 767px) {
	// 		padding: 0;
	// 		left: 0;
	// 		transform: none;
	// 		width: 100%;
	// 	}
	// }
}

.full-screen-cpn {
	position: fixed;
	z-index: -1;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	opacity: 0;
	transition: z-index 0.5s step-end, opacity 0.5s ease;

	&.-opened {
		z-index: 5000;
		opacity: 1;
		transition: z-index 0.5s step-start, opacity 0.5s ease;
	}

	&.-gallery {
		.swiper-container {
			display: block;
		}

		.video-container {
			display: none;
		}
	}

	&.-video {
		.swiper-container {
			display: none;
		}

		.video-container {
			display: block;
		}
	}

	.close{
		position: absolute;
		right: 30px;
		top: 30px;
		width: 24px;
		height: 24px;
		z-index: 9;
		color: white;

		&:hover{
			cursor: pointer;
		}

		span{
			font-size: 24px;
		}
	}

	.background {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0,0,0,0.85);
	}

	.swiper-container {
    	position: relative;
		width: 80%;
		height: 300px;
		margin-left: auto;
		margin-right: auto;
	}

	.video-container {
		position: relative;
	    width: 80%;
	    max-width: 1000px;

	    @media (max-width:992px) {
	    	width: 90%;
	    }
	}

	.video-wrapper {
		position: relative;
		padding-bottom: 56.25%; /* 16:9 */
		padding-top: 25px;
		height: 0;

		iframe {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
		}
	}

	.swiper-slide {
		background-size: cover;
		background-position: center;
	}

  .swiper-button-prev::after {
    font-family: 'icomoon' !important;
    color: white;
    content: "\e909";
  }

  .swiper-button-next::after {
    font-family: 'icomoon' !important;
    color: white;
    content: "\e90a";
  }

  .gallery-thumbs {
	height: 15%;
	box-sizing: border-box;
	padding: 10px 0;

	.swiper-slide {
		width: 15%;
		height: 100%;
		opacity: 1;

		&:after{
			position: absolute;
			content: "";
			width: 100%;
			height: 100%;
			background-color: black;
			opacity: 0.5;
		}
	}

	.swiper-slide-active {
		opacity: 1;

		&:after{
			opacity: 0;
		}
	}

	@media (max-width: 768px ) {
		width: 90%;

		.swiper-slide{
			width: 70px;
			height: 47px;
		}
	}
}

	.info-ctn {
		padding: 5px 20px;
		z-index: 5001;
		position: absolute;
		left: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		// font-family: $primaryFont;
		font-size: var(--font-size-cta-small);
		font-weight: 600;
		line-height: 19px;
		color: white;

		&.hide {
			display: none;
		}
	}

	.gallery-top {
		height: 80%;
		width: 80%;

		.swiper-wrapper {
			width: auto;
		}
		.swiper-slide {
			text-align: center;

			img {
				max-width: inherit;
				max-height: 100%;
			}
		}

		.img-ctn {
			display: inline-block;
		}

		@media (max-width: 768px ) {
			height: auto;
			width: 90%;
			max-height: 60vw;

			.swiper-wrapper {
				align-items: flex-end;
			}

			.swiper-slide {
				height: 100%;

				img{
					max-width: 100%;
					object-fit: contain;
				}

			}
		}
	}
}

.property-group-hero-cpn {
	min-height: 0px !important;

	.absolute-ctn {
		display: flex;
		justify-content: flex-end;

		.price-tag {
			background-color: $propertyHeroSettingsBackground;
			text-align: center;

			.-inner-title {
				margin-top: 17px;
			}

			.-inner {
				background-color: transparent;
				padding-top: 0;
			}
		}
	}
}
