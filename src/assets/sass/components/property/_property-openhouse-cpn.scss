/*
	Property Visit Component
*/

$propertyOpenhouseIconColor: #444444;
$propertyOpenhouseIconSize: 24px;

.property-openhouse-cpn {
	.visit-ctn {
		.date {
			display: flex;
    		align-items: center;

    		@media (max-width: 767px) {
				display: inline-flex;
    		}

			span {
				margin-left: 15px;
			}

			i {
				color: $propertyOpenhouseIconColor;
				font-size: $propertyOpenhouseIconSize;
			}
		}

		.visit {
			@media (max-width: 767px) {
				display: block;

				.hour {
					display: inline-block;
					float: right;
				}

				a {
					margin-top: 10px;
					clear: both;
					display: block;
				}
			}
		}
	}
}