/*
	Location Pane Component
*/

.location-pane-cpn {
	position: absolute;
	margin: 0;
	z-index: 10;
	width: 400px;
	height: 100%;
	background-color: $white;
	box-shadow: 2px 0 4px 0 rgba(0,0,0,0.10);
	transition: $transition;
	margin-left: -400px;
	display: flex;
	align-items:center;

	@media (max-width: 767px) {
		display: none;
	}

	.toggle-pane {
		position: absolute;
		top: 15px;
		right: -30px;
		display: block;
		width: 30px;
		line-height: 60px;
		text-align: center;
		background-color: var(--primary-color);
		color: white;
		transition: $transition;
		cursor:pointer;

		div {
			transform: rotate(180deg);
		}

		&:hover {
			background-color: rgba(var(--primary-color), .60);
		}
	}
	&.open {
		margin: 0;

		.toggle-pane {
			div {
				transform: rotate(0);
			}
		}
	}

	.pane-content-ctn {
		padding: 0px 60px;

		.info-ctn {
			margin-top: 30px;
			padding-bottom: 30px;
			border-bottom: 1px solid #DCDCDC;

			&:last-child {
				padding-bottom: 0;
				border-bottom: none;
			}

			.sub-section-title {
				margin-top: 0;
				margin-bottom: 10px;
				color: $light-grey;
				font-family: var(--font-secondary);
				font-size: var(--font-size-cta-small);
				line-height: 19px;
			}

			.location {
				margin-top: 0;
				margin-bottom: 15px;
				color: $black;
				font-family: var(--font-secondary);
				// font-size: $primaryTextSize;
				font-weight: 600;
				line-height: var(--line-height-cta-small);
			}

			.direction {
				font-size: 14px;
				font-weight: 600;
				line-height: normal;
				text-transform: uppercase;
			}

			.description {
				margin-top: 0;
				margin-bottom: 15px;
				color: var(--color-text);
				font-family: var(--font-secondary);
				font-size: var(--font-size-cta-small);
				line-height: var(--line-height-cta-small);
			}
		}
	}
}
