/*
	Home Hero Component
	*/

	$homeHero1ButtonHeight: 80px;
	$homeHero1ButtonHeightMobile: 40px;
	$homeHero1ButtonWidth: 80px;
	$homeHero1ButtonWidthMobile: 40px;
	$homeHero1Background: rgba(0,0,0,0.3);
	$homeHero1Radius: 40px;
	$homeHero1LeftButtonPosition: 30px;
	$homeHero1LeftButtonPositionMobile: 15px;
	$homeHero1RightButtonPosition: 30px;
	$homeHero1RightButtonPositionMobile: 15px;
	$homeHero1LeftArrowSVG: url('assets/images/SVG/UI/arrow-left.svg');
	$homeHero1RightArrowSVG: url('assets/images/SVG/UI/arrow-right.svg');
	$homeHero1ArrowHeight: 28px;
	$homeHero1ArrowWeight: 17px;

	$homeHero1HomeBtnArrow: url('assets/images/SVG/UI/arrow-right.svg');
	$homeHero1HomeBtnArrowWeight: 11px;
	$homeHero1HomeBtnArrowHeight: 18px;

	$homeHero1BlocInfoWidth: 480px;
	$homeHero1BlocInfoRight: 25px;
	$homeHero1BlocInfoBottom: 80px;
	$homeHero1BlocBorder: 1px solid #D5D5D5;
	$homeHero1BlocPadding: 16px 20px 0px 20px;

	$homeHero1BlocPriceSize: 22px;
	$homeHero1BlocPriceWeight: bold;
	$homeHero1BlocPriceColor: #000000;
	$homeHero1BlocPriceLineHeight: 30px;
	$homeHero1BlocPriceFont: var(--font-secondary);

	$homeHero1BlocNumberSize: 16px;
	$homeHero1BlocNumberWeight: 500;
	$homeHero1BlocNumberColor: var(--color-text);
	$homeHero1BlocNumberLineHeight: 19px;
	// $homeHero1BlocNumberFont: $thirdFont;

	$homeHero1BlocLocationSize: 16px;
	$homeHero1BlocLocationColor: var(--color-text);
	$homeHero1BlocLocationLineHeight: 22px;
	$homeHero1BlocLocationFont: var(--font-secondary);

	$homeHero1BlocButtonTextColor: $light-grey;
	$homeHero1BlocButtonTextFont: var(--font-secondary);
	$homeHero1BlocButtonTextSize: 13px;
	$homeHero1BlocButtonTextLineHeight: 24px;

	$homeHero1BlocButtonBG: var(--primary-color);
	$homeHero1BlocButtonBGHover: var(--primary-color-darker);;

	.hero-loader{
		display: flex;
		justify-content: center;
		align-items: center;
		min-height: 650px;
		max-height: 650px;
	}

	.home-hero-ctn {
		opacity: 1;
		transition: $transition;

		@media (min-width: 1404px) {
			max-height: 650px;
			min-height: 650px;
		}

		@media (max-width: 1403px) {
			max-height: 650px;
		}

		.swiper-slide {
			max-height: 650px;

			.slide-img{
				width: 100%;
				max-height: 650px;
				object-fit: cover;
				min-height: 250px;
			}

			@media (max-width: 992px) {
				.slide-img {
					max-height: none;
					height: 500px;
				}
			}

			@media (max-width: 768px) {
				.slide-img {
					height: 350px;
				}
			}

			@media (max-width: 550px) {
				.slide-img {
					min-height: 0;
					height: 200px;
				}
			}
		}

		.swiper-btn {
			width: $homeHero1ButtonWidth;
			height: $homeHero1ButtonHeight;
			background: $homeHero1Background;
			border-radius: $homeHero1Radius;
			transform: translate(0%, -50%);
			margin-top: 0;

			@media (max-width:992px) {
				width: $homeHero1ButtonWidthMobile;
				height: $homeHero1ButtonHeightMobile;
			}

			&:focus{
				outline: none;
			}

			&:before {
				position: absolute;
				font-family: 'icomoon' !important;
				speak: none;
				font-style: normal;
				font-weight: normal;
				font-variant: normal;
				text-transform: none;
				line-height: 1;
				font-size: 28px;
				color: white;
				left: 50%;
				top: 50%;
				transform: translate(-50%, -50%);

				@media (max-width:992px) {
					font-size: 20px;
				}
			}

			&.swiper-button-prev {
				left: $homeHero1LeftButtonPosition;

				@media (max-width:992px) {
					left: $homeHero1LeftButtonPositionMobile;
				}

				&:before {
					content: "\e909";
				}


			}

			&.swiper-button-next {
				right: $homeHero1RightButtonPosition;

				@media (max-width:992px) {
					right: $homeHero1RightButtonPositionMobile;
				}

				&:before {
					content: "\e90a";
				}
			}
		}

		&.style-1{
		}

		.home-bloc-info-ctn {
			background: white;
			position: absolute;
			bottom: $homeHero1BlocInfoBottom;
			right: $homeHero1BlocInfoRight;
			width: $homeHero1BlocInfoWidth;

			@media (max-width: 992px) {
				bottom: 0px;
				right: 0;
				width: 100%;
				position: relative;
			}

			&:hover {
				.home-hero-btn-ctn {
					.home-hero-btn{
						background: $homeHero1BlocButtonBGHover;
					}
				}
			}

			.home-bloc-head {
				border-bottom: $homeHero1BlocBorder;
				padding: $homeHero1BlocPadding;

				.info-ctn {
					display: flex;
					align-items: center;
					justify-content: space-between;
				}

				.price {
					margin: 0;
					float: left;
					color: $homeHero1BlocPriceColor;
					font-family: $homeHero1BlocPriceFont;
					font-size: $homeHero1BlocPriceSize;
					font-weight: $homeHero1BlocPriceWeight;
					line-height: $homeHero1BlocPriceLineHeight;
				}

				.numbers {
					float: right;
					@include clearfix;

					.icon-ctn {
						float: left;
						padding-left: 32px;
						display: flex;
						align-items: center;

						img {
							height: 18px;
						}
					}

					p {
						padding-left: 14px;
						margin: 0;
						display: inline-block;
						color: $homeHero1BlocNumberColor;
						font-size: $homeHero1BlocNumberSize;
						font-weight: $homeHero1BlocNumberWeight;
						line-height: $homeHero1BlocNumberLineHeight;
					}
				}

				.location {
					margin-top: 5px;
					margin-bottom: 15px;
					clear: both;
					color: $homeHero1BlocLocationColor;
					font-family: $homeHero1BlocLocationFont;
					font-size: $homeHero1BlocLocationSize;
					line-height: $homeHero1BlocLocationLineHeight;
				}
			}

			.home-hero-btn-ctn {
				display: flex;
				justify-content: space-between;
				align-items:center;
				padding-left: 20px;

				p {
					margin: 0;
					padding-right: 26px;
					color: $homeHero1BlocButtonTextColor;
					font-family: $homeHero1BlocButtonTextFont;
					font-size: $homeHero1BlocButtonTextSize;
					line-height: $homeHero1BlocButtonTextLineHeight;
				}

				.info-ctn {
					display: flex;
				}

				.home-hero-btn {
					position: relative;
					height: 50px;
					width: 50px;
					background: $homeHero1BlocButtonBG;
					transition: $transition;

					&:after {
						content: "\e90a";
						font-family: 'icomoon' !important;
						speak: none;
						font-style: normal;
						font-weight: normal;
						font-variant: normal;
						text-transform: none;
						line-height: 1;
						position: absolute;
						left: 50%;
						top: 50%;
						transform: translate(-50%, -50%);
						color: white;
					}
				}
			}
		}

	// Style 2

	&.style-2 {
		position: relative;
		max-height: 820px;

		img {
			object-fit: cover;
			width: 100%;
		}

		@media (max-width: 992px) {
			> img {
				min-height: 450px;
			}
		}

		.filter {
			opacity: 1;
		}

		.search-simple-cpn {
			background: transparent;
			padding: 0;
		}
	}

	&.style-3 {
		background: linear-gradient(270deg, rgba(0,0,0,0) 0%, rgba(255,255,255,0.01) 36.39%, #FFFFFF 100%);

		.text-ctn {
			padding-top: 30px;
			padding-bottom: 15px;
		}

		p {
			margin-top: 0;
			color: #4B5054;
			font-family: var(--font-secondary);
			font-size: 16px;
			line-height: 30px;
		}
	}

	&.style-3 {
		position: relative;
		overflow: hidden;
		min-height: 600px;
		max-height: 750px;

		h1{
			position:absolute;
			visibility: hidden;
			
		}

		@media (max-width:1600px) {
			max-height: 650px;
		}

		@media (max-width:1200px) {
			max-height:550px;
			min-height: 500px;
		}

		@media (max-width:1300px) {
			max-height:450px;
			min-height: 400px;
		}
		

		@media (max-width:805px) {
			max-height:400px;
			min-height: 380px;
		}

		@media (max-width:715px) {
			max-height:360px;
			min-height: 360px;
		}

		@media (max-width:640px) {
			max-height:330px;
			min-height: 330px;
		}

		@media (max-width:600px) {
			max-height:unset;
			min-height: unset;
		}

		.mobile-img{
			display: none;
			@media (max-width:600px) {
				display: block;
				width: 100%;
				height: 100%;
				object-fit: cover;
			}
		}

		.video-wrapper {
			pointer-events: none;
			position: relative;
			padding-bottom: 56.25%; /* 16:9 */
			height: 0;
			z-index:2;

			@media (max-width:600px) {
				display: none;
			}
		
			iframe{
				position: absolute;
				bottom: 80px;
				left: 0;
				width: 100%;
				height: 100%;
				transform: scale(1.15);
				
				@media (max-width: 1600px) {
					bottom: 100px;
				}

				@media (max-width: 1460px) {
					bottom: 70px;
					transform: scale(1.2);
				}

				@media (max-width: 1360px) {
					bottom: 50px;
				}

				@media (max-width: 930px) {
					bottom: 27px;
					transform: scale(1.3);
				}
		
			
				@media (max-width: 870px) {
					transform: scale(1.5);
				}

				

				
			}
		}
	}
}
