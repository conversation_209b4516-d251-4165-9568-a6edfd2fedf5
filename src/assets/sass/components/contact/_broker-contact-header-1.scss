.broker-contact-header-1-cpn {

	.contact-form-ctn {
		@media (min-width: $tablet-lg) {
			padding-right: grid-space(math.div(1,12),0); // todo : do a dynamic class like col-x-xx for this
		}
	}

	.-page-description {
		margin: 0;
		color: var(--color-text);
		font-family: var(--font-secondary);
		font-size: var(--font-size-small);
		line-height: var(--line-height-small);
	}

	.-page-required{
		margin: 25px 0 0;
		color: var(--color-text);
		font-family: var(--font-secondary);
		font-size: 12px;
	}

	.info-box {
		background-color: $white;
		border-bottom: 5px solid #004b3c33;

		.info-text-ctn {
			padding: 35px;

			.block {
				display: flex;
				margin-bottom: 20px;

				&:last-child { margin-bottom: 0; }

				i {
					font-size: var(--font-size-small);
					color: var(--color-text);
					position: relative;
					top: 4px;
				}

				p, a {
					margin: 0;
					font-family: var(--font-secondary);
					color: var(--color-text);
				}

				.text { padding-left: 15px; }
			}

			.location-ctn {

				.text {
					font-size: var(--font-size-cta-small);
					line-height: 19px;

					.links { margin-top: 6px; }

					a {
						margin-right: 20px;
						color: var(--primary-color);
						font-family: var(--font-secondary);
						font-size: var(--font-size-cta-small);
						font-weight: 600;
						line-height: var(--line-height-cta-small);
					}
				}
			}

			.email-ctn {
				.text {
					font-size: var(--font-size-cta-small);
					line-height: 19px;
				}
			}

			.phone-ctn {
				.text {
					font-size: var(--font-size-small);
					font-weight: 600;
					line-height: 22px;
				}
			}
		}
	}

  @media (max-width: 991px) {
    .info-box {
      margin-top: 40px;
    
      .info-text-ctn {
        padding: 20px;
        .location-ctn .text .links a { display: block; }
      }
    }
  }
}