.broker-contact-header-2-cpn {
  padding: 60px 0;

	.sub-description {
		margin: 0 0 40px;
		color: var(--color-text);
		font-family: var(--font-secondary);
		font-size: var(--font-size-small);
		line-height: var(--line-height-small);
	}

	.row {
		display: flex;
		align-items: flex-end;
	}

	.dual-ctn {
		display: flex;
		flex-flow: row wrap;
	}

	.broker-head {
		padding: 0 15px 105px;
	}

	.block {
		display: flex;
		margin-bottom: 15px;

		&:last-child {
			margin-bottom: 0;
		}

		i {
			font-size: var(--font-size-small);
			color: var(--color-text);
			position: relative;
			top: 4px;
		}

		p, a {
			margin: 0;
			font-family: var(--font-secondary);
			color: var(--color-text);
		}

		.text {
			padding-left: 15px;
		}

		&.-dual {
			width: calc(100% * 1/2 - 25px);
			margin-right: 50px;

			&:nth-child(2n) {
				margin-right: 0;
			}
		}

		&.-clear {
			clear: both;
		}
	}

	.location-ctn {

		.text {
			font-size: var(--font-size-cta-small);
			line-height: 19px;

			.links {
				margin-top: 6px;
			}

			a {
				margin-right: 20px;
				color: var(--primary-color);
				font-family: var(--font-secondary);
				font-size: var(--font-size-cta-small);
				font-weight: 600;
				line-height: var(--line-height-cta-small);
			}
		}
	}

	.email-ctn {
		.text {
			font-size: var(--font-size-cta-small);
			line-height: 19px;
		}
	}

	.phone-ctn {
		.text {
			font-size: var(--font-size-small);
			font-weight: 600;
			line-height: 22px;
		}
	}

  @media (max-width: 992px) {
    .row {
      display: block;
    }
  }
}
