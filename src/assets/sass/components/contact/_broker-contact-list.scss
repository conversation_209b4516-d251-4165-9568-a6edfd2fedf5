.broker-contact-list-cpn {
	padding: 85px 0;
	background-color: var(--color-bg);

	h3 {
    margin: 80px 0 30px 0;
  }

	.list-ctn {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 40px 20px;
	}

  .broker-card-ctn {
    @include flex(flex-start, flex-start);

    .img-ctn {
      width: 100px;
      min-width: 100px;
      height: 100px;
      border-radius: 50%;
      overflow: hidden;
    }

    .text-ctn {
    
      .role {
        margin: 10px 0;
        @include fontSize(13px, 24px);
        color: $light-grey;
      }
    
      .phone {
        margin: 0;
        color: var(--color-text);
      }
    }
  }

  .main-button {
    margin-top: 80px;
  }

	@media (max-width: 991px) {
    .list-ctn { grid-template-columns: 1fr 1fr; }
	}
  
	@media (max-width: 767px) {
		padding: 45px 0;
    .list-ctn { grid-template-columns: 1fr; }
	}
}
