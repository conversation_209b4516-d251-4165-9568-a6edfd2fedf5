.search-sell-cpn {
	display: flex;
	align-items: center;
  justify-content: center;
	min-height: 680px;
	padding: 40px;
	background-image: url("/assets/images/placeholder/banner/ban-2.jpg");
	background-repeat: no-repeat;
	background-size: cover;

  .container { 
    padding: 50px 80px; 
    background-color: $white;

    @media (max-width: 992px) {
      padding: 50px 30px;
    }
  }

  .title {

    .icon {
      display: block;
      margin-bottom: 30px;
      @include fontSize(60px);
      color: var(--primary-color);
    }
  }

  p { 
    color: $light-grey;
    text-align:center; 
  }

  .input-ctn { 
    margin-top: 30px;
    position: relative; 
  }

  .main-button {
    position: absolute;
    right: 5px;
    top: 5px;
    height: 50px;
    border-radius: 3px;
    text-transform: uppercase;
  }

  // Change button style on tablet
  @media (max-width: 992px) {
    .main-button {
      width: 50px;
      min-width: 50px;
      padding: 7px 18px;

      span { display: none; }

      &:before {
        content: "\e90a";
        font-family: 'icomoon' !important;
        font-weight: normal;
      }
    }
  }

  @media (max-width: 767px) {
    padding: 20px;
  }
}