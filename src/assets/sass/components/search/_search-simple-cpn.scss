$searchSimpleSearchPadding: 40px 0;
$searchSimpleSearchBG: var(--primary-color-darker);;

$searchSimpleIndicatorColor: white;
$searchSimpleTabColor: white;
$searchSimpleTabFont: var(--font-secondary);
$searchSimpleTabSize: 18px;
$searchSimpleTabWeight: 600;

$searchSimpleSearchOpacity: 0.6;
$searchSimpleSearchColor: white;
$searchSimpleSearchFont: var(--font-secondary);
$searchSimpleSearchSize: 13px;
$searchSimpleSearchWeight: 600;
$searchSimpleSearchLineHeight: 18px;

.search-simple-cpn {
  padding: $searchSimpleSearchPadding;
  background-color: $searchSimpleSearchBG;

  .search-list-dropdown {
    position: absolute;
    top: 50px;
    min-height: 60px;
    left: 0;
    width: 100%;
    background: white;
    border-radius: 5px;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
    padding: 20px;
    z-index: 1000;
  }

  .tabs-ctn {
    float: none;
    margin: 0 auto;

    @media (max-width: 992px) {
      position: relative;
      padding-bottom: 40px;
    }

    .input-tab {
      display: none;
    }

    input:checked+label {
      &:after {
        width: 100%;
      }
    }

    label {
      position: relative;
      padding: 0 12px 5px;
      color: $searchSimpleTabColor;
      font-family: $searchSimpleTabFont;
      font-size: $searchSimpleTabSize;
      font-weight: $searchSimpleTabWeight;
      text-align: center;
      text-transform: none;
      overflow: inherit;

      &:hover {
        cursor: pointer;
      }

      &:after {
        position: absolute;
        bottom: 0;
        left: 0;
        content: "";
        background: #fff;
        height: 2px;
        display: block;
        width: 0px;
        transition: all 0.5s cubic-bezier(0.19, 1, 0.22, 1);
      }
    }

    .tab-head {
      display: flex;
      align-items: center;
      justify-content: flex-start;

      @media (max-width: 992px) {
        display: block;
      }
    }

    #tab1:checked~#acheter,
    #tab2:checked~#vendre,
    #tab3:checked~#centris {
      display: block;
    }

    .tabs {
      padding-left: 0;
      background: transparent;
      margin: 0;
      width: auto;

      .indicator {
        background: $searchSimpleIndicatorColor;
      }
    }

    // Advanced button
    .special-search {
      padding-left: 20px;
      opacity: $searchSimpleSearchOpacity;
      color: $searchSimpleSearchColor;
      font-family: $searchSimpleSearchFont;
      font-size: $searchSimpleSearchSize;
      font-weight: $searchSimpleSearchWeight;
      line-height: $searchSimpleSearchLineHeight;

      @media (max-width: 992px) {
        padding-left: 0;
        position: absolute;
        bottom: 0;
        right: 15px;
      }

      .icon-arrow-externe {
        margin-left: 5px;
        display: inline-block;
        font-size: 12px;
      }
    }

    .tab-content {
      display: none;
      padding-top: 10px;

      .input-ctn {
        position: relative;
        margin-top: 12px;
      }

      .ap-input-icon {
        display: none;
      }

      .main-button {
        position: absolute;
        right: 5px;
        top: 5px;
        border-radius: 3px;

        @media (max-width: 992px) {
          width: 50px;
          padding: 7px 18px;

          span {
            display: none;
          }

          &:before {
            content: "\e90a";
            font-family: 'icomoon' !important;
            speak: none;
            font-style: normal;
            font-weight: normal;
            font-variant: normal;
            text-transform: none;
            line-height: 1;
          }
        }
      }
    }
  }
}