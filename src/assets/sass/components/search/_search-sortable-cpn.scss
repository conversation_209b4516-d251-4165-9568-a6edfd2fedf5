.search-sortable-cpn {
	padding: 20px 40px;
	border-bottom: solid 1px #DDDDDD;
	line-height: 42px;

	@media (max-width: 992px) {
		padding: 20px;
		@include clearfix;
	}

	h3 {
		display: inline-block;
		text-transform: uppercase;
		line-height: 0;
	}

	.left {
		float: left;
	}

	.right {
		float: right;
	}

	.sortable-list {
		float: left;
		margin: 0;
		padding: 0;
		width: auto;

		.tab {
			margin: 0 12.5px;

			a {
				padding: 0;

				&.active {
					color: var(--primary-color);
				}
			}
		}
		.indicator {
			background-color: var(--primary-color);
		}

		@media (max-width: 992px) {
			position: relative;
			top: -5px;
		}
	}

	.options {
		float: left;

		.icon {
			margin-left: 25px;
			cursor: pointer;
			font-size: 22px;
			line-height: 44px;
		}
		.dropdown {
			.dropdown-wrapper {
				width: 380px;
				right: 0;
				line-height: initial;

				h4 {
					margin-top: 0;
				}
			}
		}
	}

	.order-filter {
		min-width: 140px;

		* {
			box-shadow: none !important;
		}

		.ng-control {
			border: none;

			&:focus, &:hover {
				outline: none;
				box-shadow: none;
			}

			.ng-placeholder {
				font-size: 14px;
				margin-right: 10px;
			}

			.ng-input input{
				pointer-events: none;
			}
		}
		
		.ng-select-dropdown-outer {
			width: 140px;
			right: 0;
			font-size: 13px;
			z-index: 15;
			
			.ng-option.selected{
	        	pointer-events: none;
	      	}
		}

		.ng-value-wrapper {
			font-size: 14px;
			margin-right: 10px;
			color: #aaa;
		}

		@media (max-width: 992px) {
			font-size: 14px;
			min-width: 115px;

			.ng-select-container {
				border: none;

				.ng-value-container .ng-input {
					padding-right: 20px;
				}
			}

			.ng-clear-wrapper {
				display: none;
			}
		}
	}
}
