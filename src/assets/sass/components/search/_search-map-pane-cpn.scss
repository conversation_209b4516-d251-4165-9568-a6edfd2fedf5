.search-map-pane-cpn {
	#map {
		position: fixed;
    width: 100%;
    min-height: 500px;
		height: 100%;
		right: 0;
    top: 80px;
		transition: $transition;

		.mapbox-logo { display: none; }
    .mapboxgl-ctrl-top-right { top: 160px; }

    .inner-popup {
      display: flex;
      cursor: pointer;

      img {
        width: 70px;
      }

      .content {
        padding-left: 10px;

        .price {
          margin-top: 0;
          margin-bottom: 5px;
          color: $black;
          font-family: var(--font-secondary);
          font-size: 16px;
          font-weight: bold;
          line-height: 22px;
        }
  
        .location {
          margin: 0;
          color: var(--color-text);
          font-family: var(--font-secondary);
          font-size: 12px;
          line-height: 17px;
        }
      }
    }
	}

	@media (max-width: 992px) {
    #map { 
      display: none;
      position: relative; 
      top: 0;

      .mapboxgl-ctrl-top-right { top: 20px; }
    }

		&.show #map { display: block; }
	}
}
