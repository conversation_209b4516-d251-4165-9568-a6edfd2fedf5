.text-card-2-cpn {

	.card-ctn {
		display: flex;
		align-items: center;
		padding: 80px 60px;
		background-color: #F6F6F6;

		.img-ctn {
			text-align: center;
		}

		.text-wrap {

			p {
				margin-bottom: 30px;
				color: var(--color-text);
				font-family: var(--font-secondary);
				font-size: var(--font-size-small);
				line-height: var(--line-height-small);
			}
		}
	}

	.left {
		.img-ctn {
			padding-left: 0;
			text-align: center;
		}

		.text-wrap {
			padding-right: 0;
		}
	}

	.right {
		.img-ctn {
			padding-right: 0;
			text-align: center;
		}

		.text-wrap {
			padding-left: 0;
		}
	}

	@media (max-width: 767px) {
		margin: 60px 0;

		.card-ctn {
			padding: 30px 0px;
			flex-direction: column;
			
			.img-ctn {
				margin-top: 30px;
				margin-bottom:40px;
			}

			.text-wrap {
				a.main-button {
					margin: auto;
				}
			}
		}

		.left {
			.img-ctn {
				padding-left: 15px;
			}

			.text-wrap {
				padding-right: 15px;
				text-align: center;
			}
		}
	}
}
