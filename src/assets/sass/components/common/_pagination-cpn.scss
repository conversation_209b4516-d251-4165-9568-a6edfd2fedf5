.paginiation-controls {
	.ngx-pagination {
    @include flex();
    gap: 5px;
		margin: rem(80px) 0 rem(120px) 0;
		padding: 0;
		text-align: center;

		li {
      width: 40px;
      height: 40px;
      padding: 0;
			border: 2px solid var(--primary-color);
			border-radius: 6px;

      > span, > a { 
        font-size: rem(18px);
        line-height: rem(36px);
        font-weight: 600;
        color: var(--primary-color);
        display: block;
        padding: 0;
      }

      >a { height: 100%; }

			&.disabled { display: none; }

			&.current {
				background: var(--primary-color);
        border-color: var(--primary-color);
				span { color: white; }
			}

      &:hover a { background-color: rgba($primary-color, 0.4); }
    }

		.pagination-previous, .pagination-next {
			border: 2px solid var(--primary-color);

      a { 
        height: 100%;
        
        &:after, &::before { 
          font-family: 'icomoon';
          font-size: 14px;
          color: var(--primary-color);
          position: relative;
          top: -1px;
          margin: 0;
        }
      }
    }

		.pagination-previous a::before { content: "\e909"; }
		.pagination-next a::after { content: "\e90a"; }
	}
}
