// Main footer
$footerMainBackground: $white;
$footerMainPadding: 65px 0px 85px 0px;
$footerMainLogoHeight: 92px;
$footerMainBorder: 0px none;

	// Title link
	$footerMainSubSectionLinkColor: var(--dark-color);
	$footerMainSubSectionLinkSize: 16px;
	$footerMainSubSectionLinkWeight: 600;
	$footerMainSubSectionLinkLineHeight: 22px;
	$footerMainSubSectionLinkTextTransform: uppercase;

	// Regular link
	$footerMainItemLinkColor: var(--color-text);
	$footerMainItemLinkSize: 14px;
	$footerMainItemLinkLineHeight: 18px;

	// Info
	$footerMainInfoColor: var(--color-text);
	$footerMainInfoSize: 14px;
	$footerMainInfoWeight: 400;
	$footerMainInfoLineHeight: 18px;

// Sub footer
$subFooterMainBackground: $white;
$subFooterHeight: 48px;
$subFooterBorder: 1px solid rgba(255, 255, 255, 0.233);

	$subFooterCopyrightColor: var(--color-text);
	$subFooterCopyrightFont: var(--font-secondary);
	$subFooterCopyrightSize: 13px;
	$subFooterCopyrightLineHeight: 18px;

	$subFooterShareColor: var(--color-text);
	$subFooterShareColorHover: var(--color-text);

#footer {
	position: relative;

	// Add opacity hover on all active links
	[href]:not([href=""]) {
		opacity: 1;
		transition: all .2s ease-in-out;
		&:hover { opacity: 0.7; }
	}

	// Links & site info
	.main-footer {
		background-color: $footerMainBackground;
		padding: $footerMainPadding;
		border-top: $footerMainBorder;

		.container {
			// display: flex;

			.footer-links {
				display: flex;
				flex-wrap: wrap;
				width: 100%;

				.footer-bloc {
					padding: 0px 10px;
					flex-basis: 32%;

					// Regular links
					a {
						display: block;
						margin-bottom: 6px;
						color: $footerMainItemLinkColor;
						font-size: $footerMainItemLinkSize;
						line-height: $footerMainItemLinkLineHeight;
					}

					// Title links
					h6 a {
						margin-bottom: 24px;
						color: $footerMainSubSectionLinkColor;
						font-size: $footerMainSubSectionLinkSize;
						font-weight: $footerMainSubSectionLinkWeight;
						line-height: $footerMainSubSectionLinkLineHeight;
						text-transform: $footerMainSubSectionLinkTextTransform;
					}
				}
			}

			.footer-info {
				display: flex;
				flex-direction: column;
				width: 320px;

				.logo-ctn {
					margin-bottom: 50px;
					margin-top: 0px;

					img {
						height: $footerMainLogoHeight;
					}
				}

				.description-ctn,
				.location-ctn,
				.mail-ctn,
				.phone-ctn {
					width: auto;
					display: flex;
					align-items: flex-start;
					margin-top: 15px;

					a, p {
						color: $footerMainInfoColor;
						font-size: $footerMainInfoSize;
						font-weight: $footerMainInfoWeight;
						line-height: $footerMainInfoLineHeight;
					}

					.icon {
						color: var(--primary-color);
						padding-right: 15px;

						&-pin {
							font-size: 23px;
							padding-right: 9px;
							position: relative;
							left: -3px;
						}
					}
				}

				.phone-ctn a {
					font-size: 16px;
					font-weight: bold;
					line-height: 19px;
				}
			}
		}
	}

	// Copyright & social links
	.sub-footer {
		background-color: $subFooterMainBackground;
		height: $subFooterHeight;
		border-top: $subFooterBorder;
		padding-top: 15px;
		padding-bottom: 15px;

		.container {
			display: flex;
			align-items: center;
			gap: 10px;

			@media (max-width: 768px) {
				
				flex-direction: column;
				align-items: flex-start;

				.share-ctn {
					margin-left: unset !important;
				}
			}

			.share-ctn, .copyright-ctn {
				display: flex;

				a {
					opacity: 0.7;
					transition: all .2s ease-in-out;
					&:hover { opacity: 1 !important; }
				}
			}

			.copyright-ctn {
				gap: 10px;

				span, a {
					color: $subFooterCopyrightColor;
					font-family: $subFooterCopyrightFont;
					font-size: $subFooterCopyrightSize;
					line-height: $subFooterCopyrightLineHeight;
					margin-right: 40px;

					@media (max-width: 768px) {
						font-size: 14px;
						line-height: 20px;
						margin-right: 0;
					}

					&:last-child {
						margin-right: 0;
					}
				}
			}

			.share-ctn {
				gap: 25px;
				margin-left: auto;

				a {
					color: $subFooterShareColorHover;
					font-size: 18px;
				}
			}
		}
	}

	// Tablet
	@media (max-width: 992px) {
		.main-footer {
			padding: 35px 0px 25px 0px;

			.container {
				gap: 60px 0;

				.footer-links {
					order: 2;
				}

				.footer-info {
					width: auto;
					margin: auto;
				}
			}
		}

		.sub-footer {
			height: auto;

			.container {
				.copyright-ctn {
					padding: 10px 0px;
					flex-direction: column;
				}
			}
		}
	}

	// Mobile
	@media (max-width: 650px) {
		.footer-bloc {
			margin-bottom: 25px;
			 
			h6 a {
				margin-bottom: 15px !important;
			}
		}

		.sub-footer {
			.container {

				.copyright-ctn .copyright {
					margin-right: 0;
				}

				.share-ctn {
					margin-left: 0;
				}
			}
		}
	}
}
