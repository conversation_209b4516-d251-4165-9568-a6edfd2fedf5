.text-card-1-cpn{

	&.-grey {
		background-color: var(--color-bg);
	}

	&.alert-cta-1-cpn {
		background-color: $white;
		.container {
			.text-ctn {
				width: 100%;
			}
		}
	}

	.container {

		.text-ctn {
			p {
				margin-bottom: 30px;
				color: var(--color-text);
				font-family: var(--font-secondary);
				font-size: var(--font-size-small);
				line-height: var(--line-height-small);
			}
		}
	
		.img-ctn {
			text-align: center;
		}
	}

	@media (max-width: 767px) {
		display: block;
		padding: 60px 0;

		.container {

			.text-ctn {
				margin-top: 40px;
				padding: 0;
				text-align: center;

				.main-button {
					margin: auto;
				}
			}

			&.homestaging-cta-cpn {
				.text-ctn {
					margin-bottom: 20px;
				}
			}
		}
	}
}