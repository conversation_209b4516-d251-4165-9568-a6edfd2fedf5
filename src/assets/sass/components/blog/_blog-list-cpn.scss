/*
	Blog List Component
*/
$blogListPadding: 50px 0px 60px 0;
$blogListBackground: var(--color-bg);

$blogListDateColor: $light-grey;
$blogListDateSize: 13px;
$blogListDateLineHeight: 24px;

$blogListDescriptionColor: var(--color-text);
$blogListDescriptionSize: 14px;
$blogListDescriptionLineHeight: 24px;

.blog-list-cpn {
	background-color: $blogListBackground;

	h2 {
		margin-bottom: 30px;
	}

	&.blog-list-2, &.blog-list-3 {

	}

	.small-link {
		padding-right: 0px;
	}

	.cpn-head {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.article-list-ctn {
		padding-top: 20px;

		&.-full {
			.article {
				width: 100%;
				margin-right: 0;
			}

			img {
				@include img();
			}
		}
	}

	.article {
		position: relative;
		background: white;
		box-shadow: 0 5px 15px 0 rgba(0,0,0,0.08);
		flex-grow: 1;

		&:only-of-type{
			flex-grow: 0;
		}

		&:nth-child(3n), &:last-child{
			margin-right: 0;
		}

		&:nth-child(n+4){
			flex-grow: 0;
		}

		&:hover {
			.filter {
				opacity: 1;
			}
		}

		.filter {
			transition: $transition;
			position: absolute;
			background-color: rgba(0, 0, 0, 0.5);


			&:after{
				content: "";
				position: absolute;
				background-image: url("/assets/images/SVG/icons/more.svg");
				width: 50px;
				height: 50px;
				top: 50%;
				left: 50%;
				transform: translate(-50%,-50%);
				background-repeat: no-repeat;
			}
		}

		.img-ctn {
			position: relative;
			display: block;
			border-radius: 2px;

			img {
				min-height: 300px;
				width: 100%;
				object-fit: cover;
			}
		}

		.flex-wrap {
			display: flex;
			align-items: center;
			flex-wrap: wrap;

			.img-ctn {
				padding-left: 0;

				@media (max-width: 990px){
					padding-right: 0;
				}
			}
		}

		.article-info {
			position: relative;
			padding: 30px;
			padding-bottom: 45px;

			p {
				margin: 0;
				font-family: var(--font-secondary);
			}

			.date {
				margin-bottom: 10px;
				margin-top: 10px;
				font-size: $blogListDateSize;
				line-height: $blogListDateLineHeight;
				color: $blogListDateColor;
			}

			.description {
				font-size: $blogListDescriptionSize;
				line-height: $blogListDescriptionLineHeight;
				color: $blogListDescriptionColor;
			}
		}

		@media (max-width: 992px) {
			.article-info {
			}
		}

		@media (max-width: 767px) {
			width: 100%;
			margin-right: 0;
		}

		@media (max-width: 500px) {
			.img-ctn img {
				height: 200px;
			}
		}
	}

	.blog-button {
		position: absolute;
		right: 0;
		bottom: 0;
		height: 50px;
		width: 50px;
		background: var(--primary-color);
		color: $white;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: $transition;

		&:hover {
			background: var(--primary-color-darker);;
		}
	}

	.-show-mobile {
		margin-top: 25px;
	}
}
