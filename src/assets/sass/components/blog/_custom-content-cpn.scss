/*
	Blog Post Card Component
*/

$blogPostCardSpacingLeft: 80px;
$blogPostCardSpacingRight: 40px;

$blogPostCardTextColor: var(--color-text);

.custom-content-cpn {

	.post-card-content {
		background-color: $white;
	}

	.post-card {
		gap: 0;

		&:hover {
			.filter {
				opacity: 1;
			}

			.post-card-content {
				&:before{
					transform: translateX(calc(-100% + 2px));
					transition-delay: 0.5s;
				}
			}

			&.left-post .post-card-content {
				&:before {
					transform: translateX(calc(100% - 2px));
				}
			}
		}

		&.left-post {

			.post-card-content {
				order: 1;
			}

			.img-ctn {
				order: 2;

				@media (max-width: 992px) {
					order: 1;
				}
			}

			.post-card-content {

				&:before{
					left: inherit;
					right: 0;
				}
			}
		}

		.img-ctn {
			position: relative;

			img {
				width: 100%;
				height: 100%;
				object-fit: cover;
			}

			.filter {
				transition: $transition;
			}


			@media (max-width: 992px) {
				width: 100%;
				
				img {
					max-height:350px;
				}
			}
		}

		.post-card-content {
			display: flex;
			align-items: center;
			position: relative;

			&:before{
				content: "";
				position: absolute;
				width: 10%;
				height: 100%;
				left: 0;
				background-color: white;
				transition: $transition;
				z-index: 12;
			}

			@media (max-width: 992px) {
				justify-content: center;
				width: 100%;
        		margin-top: 50px;
				margin-bottom: 50px;
			}

			.content {
				padding-left: var(--container-margin);
				padding-right: var(--container-margin);
				padding-bottom: 50px;
				padding-top: 50px;
				z-index: 13;

				h2{
					margin-top: 0;
				}
			}

			.description {
				font-size: var(--font-size-small);
				line-height: var(--line-height-small);
				font-family: var(--font-secondary);
				color: $blogPostCardTextColor;
			}
		}
	}
}
