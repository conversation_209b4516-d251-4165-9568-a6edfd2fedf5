.hero-landing-cpn {
    position: relative;

    img.background {
        width: 100%;
        height: 100%;
        min-height: 200px;
        object-fit: cover;
    }

    .container {
        @include flex(center, center);
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        padding: 0;

        h1.title {
            background-color: white;
            padding: 30px 50px;
        }
    }

    @media (min-width: 1400px) {
        .container {
            h1.title { 
                padding: 50px 100px;
                font-size: 60px;
            }
        }
    }

    @media (max-width: 768px) {
        .container {
            justify-content: flex-start;

            h1.title { 
                padding: 20px 40px;
                font-size: 30px;
            }
        }
    }

    @media (max-width:425px) {
        .container {
            h1.title { 
                padding: 0px 20px;
                font-size: 24px;
            }
        }
    }
}