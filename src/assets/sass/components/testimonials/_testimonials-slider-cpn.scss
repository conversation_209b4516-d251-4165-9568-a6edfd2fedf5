/*
	Testimonials Slider Component
*/

$testimonialArrowHeight: 28px;
$testimonialArrowWidth: 17px;

$testimonialTitlePaddingTop: 75px;
$testimonialTitleSize: 24px;
$testimonialTitleColor: $white;
$testimonialTitleLineHeight: 29px;
$testimonialTitleWeight: bold;

$testimonialSwiperPaddingBottom: 100px;
$testimonialSwiperPaginationBottom: 40px;
$testimonialSwiperPaginationColor: rgba(255,255,255,0.5);
$testimonialSwiperPaginationActive: $white;

$testimonialDescriptionColor: $white;
$testimonialDescriptionSize: 24px;
$testimonialDescriptionLineHeight: 40px;
$testimonialDescriptionStyle: italic;
$testimonialDescriptionFont: var(--font-secondary);

$testimonialNameColor: $white;
$testimonialNameSize: 14px;
$testimonialNameLineHeight: 19px;
$testimonialNameWeight: 600;
$testimonialNameFont: var(--font-secondary);

$testimonialLineWidth: 15px;
$testimonialLineHeight: 1px;
$testimonialLineColor: $white;

.testimonials-slider-cpn {
	background: var(--primary-color);
	text-align: center;

	.swiper-testimonials {
		position: relative;

		.swiper-wrapper{
			display: flex;
			align-items: center;
		}
	}

	.swiper-pagination {
		bottom: $testimonialSwiperPaginationBottom;
		left: 50%;
		transform: translate(-50%, 0%);

		.swiper-pagination-bullet {
			margin-right: 10px;
			background: $testimonialSwiperPaginationColor;

			&-active {
				background: $testimonialSwiperPaginationActive;
			}

			&:last-child {
				margin-right: 0;
			}
		}
	}

	.container {
		.description {
			margin: 20px auto 20px auto;
			font-style: $testimonialDescriptionStyle;
			font-size: var(--font-size-testimony);
			line-height: var(--line-height-testimony);
			color: $testimonialDescriptionColor;

			p {
				font-size: var(--font-size-testimony);
				line-height: var(--line-height-testimony);
			}

			@media (max-width: 992px) {
				font-size: var(--font-size-small);
				line-height: 22px;
			}
		}

		.name {
			color: $testimonialNameColor;
			font-family: $testimonialNameFont;
			font-size: $testimonialNameSize;
			font-weight: $testimonialNameWeight;
			line-height: $testimonialNameLineHeight;

			.line {
				display: inline-block;
				width: $testimonialLineWidth;
				height: $testimonialLineHeight;
				background: $testimonialLineColor;
				position: relative;
				top: -3px;
        		margin-right: 5px;
			}
		}
	}

	.swiper-btn {
		background: none;
		width: $testimonialArrowWidth;
		height: $testimonialArrowHeight;
		transform: translate(0%, -50%);
		margin-top: 0;

		&:before {
			position: absolute;
			font-family: 'icomoon' !important;
			speak: none;
			font-style: normal;
			font-weight: normal;
			font-variant: normal;
			text-transform: none;
			line-height: 1;
			font-size: 28px;
			color: white;
			left: 50%;
			top: 50%;
			transform: translate(-50%, -50%);
		}

		@media (max-width: 992px) {
			display: none;
		}

		&.swiper-button-prev {
			left: 30px;

			&:before {
				content: "\e909";
			}
		}

		&.swiper-button-next {
			right: 30px;

			&:before {
				content: "\e90a";
			}
		}
	}
}
