/*
	Testimonial Card Component
	*/

	.testimonial-card-cpn {
		position: relative;
		width: 100%;

		.testimonial-content {
			padding: 40px;
			display: inline-block;
			box-shadow: 0 2px 4px 0 rgba(0,0,0,0.1);
			width: 100%;
			margin-bottom: 3px;
		}

		.testimonial-wrap {
			display: block;
			position: relative;
			width: 100%;
			margin-bottom: 20px;
			background-color: $white;
			border-radius: 3px;
		}

		.img-ctn {
			margin-bottom: 40px;

			img{
				width: 100%;
			}

			@media (max-width: 992px) {
				text-align: center;
			}
		}

		.description {
			margin-top: 0;
			margin-bottom: 20px;
			color: var(--color-text) !important;
			font-family: var(--font-secondary) !important;
			font-size: var(--font-size-cta-small) !important;
			line-height: var(--line-height-cta-small) !important;
		}

		.user-ctn {
			display: flex;
			align-items: center;
			color: var(--color-text);
			font-family: var(--font-secondary);
			font-size: 13px;
			line-height: var(--line-height-cta-small);

			img {
				width: 42px;
				height: 42px;
				border-radius: 50%;
				margin-right: 20px;
			}
		}

		&.-alt{
			.testimonial-content{
				display: flex;
				align-items: center;
				flex-wrap: wrap;
				flex-direction: row-reverse;

				.img-ctn,.inner{
					width: calc(50% - 20px) ;
				}

				.inner{
					flex-grow: 1;
				}

				.img-ctn{
					margin-left: 40px;
					margin-bottom: 0;
				}
			}

			@media (max-width:992px) {
				.testimonial-content{
					display: block;

					.img-ctn,.inner{
						width: 100%;
					}

					.img-ctn{
						margin-left: 0;
						margin-bottom: 40px;
					}
				}
			}
		}
	}
