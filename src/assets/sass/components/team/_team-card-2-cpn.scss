/*
	Team Card 2 Component
*/

$teamInterstice1ItemContentBG: var(--primary-color);

$teamInterstice1ItemContentNameColor: white;
$teamInterstice1ItemContentNameSize: 18px;
$teamInterstice1ItemContentNameWeight: 600;
$teamInterstice1ItemContentNameLineHeight: 28px;

$teamInterstice1ItemContentRoleColor: white;
$teamInterstice1ItemContentRoleSize: 13px;
$teamInterstice1ItemContentRoleLineHeight: 24px;

$teamInterstice1ExpenderBG: var(--color-bg);

$teamInterstice1CloseBG: #888;
$teamInterstice1CloseBGHover: #333;

$teamInterstice1DetailsNameColor: #000000;

$teamInterstice1DetailsRoleColor: var(--color-text);
$teamInterstice1DetailsRoleSize: 14px;
$teamInterstice1DetailsRoleLineHeight: 24px;

$teamInterstice1DetailsTextColor: var(--color-text);
$teamInterstice1DetailsTextSize: 16px;
$teamInterstice1DetailsTextLineHeight: 30px;

$teamInterstice1IconColor: var(--color-text);
$teamInterstice1IconSize: 20px;

.og-grid {
	list-style: none;
	padding: 20px 0;
	margin: 0 auto;
	text-align: center;
	width: 100%;
	display: flex;
	flex-flow: row wrap;
}

.og-grid li {
	display: block;
	margin-right: 20px;
	margin-bottom: 20px;
	vertical-align: top;
	width: calc(100% * 1/4 - 15px);
	transition: height 350ms ease;

	&:nth-child(4n) {
		margin-right: 0;
	}

	@media (max-width: 992px) {
		width: calc(100% * 1/2 - 15px);

		&:nth-child(2n) {
			margin-right: 0;
		}
	}

	@media (max-width: 767px) {
		width: 100%;
		margin-right: 0;
	}
}

.grid-content {
	padding: 20px;
	background-color: $teamInterstice1ItemContentBG;
	text-align: left;
	transition: all 350ms ease;

	.name {
		margin: 0;
		color: $teamInterstice1ItemContentNameColor;
		font-family: var(--font-secondary);
		font-size: $teamInterstice1ItemContentNameSize;
		font-weight: $teamInterstice1ItemContentNameWeight;
		line-height: $teamInterstice1ItemContentNameLineHeight;
		transition: all 350ms ease;
	}

	.role {
		margin-top: 5px;
		margin-bottom: 0;
		color: $teamInterstice1ItemContentRoleColor;
		font-family: var(--font-secondary);
		font-size: $teamInterstice1ItemContentRoleSize;
		line-height: $teamInterstice1ItemContentRoleLineHeight;
		transition: all 350ms ease;
	}
}

.og-grid li.og-expanded .grid-content {
	background: var(--primary-color);

	.name, .role {
		color: white;
	}
}

.og-grid li .item,
.og-grid li .item img {
	border: none;
	outline: none;
	display: block;
	position: relative;
	max-width: 100%;
	transition: all 350ms ease;
	width: 100%;
}

.og-grid li.og-expanded .item img {
	transform: scale(1.1);
}

.og-grid li .item .img-ctn {
	position: relative;
	overflow: hidden;
}

.filter {
	z-index: 3;
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0,0,0,0.3);
	opacity: 0;
}

.og-grid li:hover {
	.item img {
		transform: scale(1.1);
	}

	.grid-content {
		background: var(--primary-color-darker);;

		.name, .role {
			color: white;
		}
	}

	.filter {
		opacity: 1;
		font-family: 'icomoon';
		speak: none;
		font-style: normal;
		font-weight: normal;
		font-variant: normal;
		text-transform: none;
		line-height: 1;
		-webkit-font-smoothing: antialiased;
		-moz-osx-font-smoothing: grayscale;

		&:after {
			position: absolute;
			font-size: 28px;
			color: white;
			content: "\e93e";
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
		}
	}
}

.og-grid li.og-expanded .filter {
	opacity: 1
}

.og-grid li.og-expanded .item::after {
	top: auto;
	border: solid transparent;
	content: " ";
	height: 0;
	width: 0;
	position: absolute;
	pointer-events: none;
	border-bottom-color: $teamInterstice1ExpenderBG;
	border-width: 30px;
	left: 50%;
	margin: -40px 0 0 -30px;
}

.og-expander {
	position: absolute;
	background-color: $teamInterstice1ExpenderBG;
	top: auto;
	left: 0;
	width: 100%;
	margin-top: 20px;
	margin-bottom: 20px;
	text-align: left;
	height: 0;
	overflow: hidden;
	transition: height 350ms ease;
}

.og-expander-inner {
	padding-top: 60px;
	padding-bottom: 60px;
	position: relative;
	display: flex;
}

.og-close {
	position: absolute;
	width: 30px;
	height: 30px;
	top: 20px;
	right: 20px;
	cursor: pointer;
	z-index: 5;
}

.og-close::before,
.og-close::after {
	content: '';
	position: absolute;
	width: 100%;
	top: 50%;
	height: 3px;
	background: $teamInterstice1CloseBG;
	transform: rotate(45deg);
}

.og-close::after {
	transform: rotate(-45deg);
}

.og-close:hover::before,
.og-close:hover::after {
	background: $teamInterstice1CloseBGHover;
}

.og-fullimg,
.og-details {
	//width: 50%;
	position: relative;
}

.og-details {
	@media (min-width: 992px) {
		padding-left: grid-space(math.div(1,12), 0);
	}

	@media (max-width: 767px) {
		width: 100%;
		padding: 0;
	}

	.name {
		margin-top: 0;
		margin-bottom: 10px;
		color: $teamInterstice1DetailsNameColor;
	}

	.role {
		margin-top: 0;
		color: var(--color-text);
		font-family: var(--font-secondary);
		font-size: $teamInterstice1DetailsRoleSize;
		line-height: $teamInterstice1DetailsRoleLineHeight;
	}

	p {
		margin-top: 0;
		color: var(--color-text);
		font-family: var(--font-secondary);
		font-size: $teamInterstice1DetailsTextSize;
		line-height: $teamInterstice1DetailsTextLineHeight;
	}

	.social-ctn {
		.block {
			display: inline;

			@media (max-width: 992px) {
				display: block;
			}
		}
	}
}

.social-ctn {
	padding-top: 20px;
	padding-bottom: 20px;
	gap: 20px;
	display: flex;
	flex-wrap: wrap;
	align-items: center;

	.-mail {
		i {
			margin-top: 3px;
			margin-right: 10px;
		}

		@media (max-width: 767px) {
			font-size: 16px;
		}
	}

	a {
		color: var(--color-text);
		display: flex;
		gap: 5px;
		align-items: center;
	}

	.social-icon {
		display: inline-block;
		margin-right: 15px;
		color: $teamInterstice1IconColor;
		font-size: $teamInterstice1IconSize;

		@media (max-width: 767px) {
			margin-top: 15px;
		}
	}
}

.og-fullimg {
	position: relative;
	text-align: center;
	transition: all 350ms ease;
	padding: 0;

	&:has(iframe) {
		aspect-ratio: 16/9;
	}

	iframe {
		height: 100%;
		width: 100%;
	}

	@media (max-width: 767px) {
		display: none;
	}
}

.og-fullimg img {
	display: inline-block;
	max-height: 100%;
	max-width: 100%;
	width: 100%;
}
