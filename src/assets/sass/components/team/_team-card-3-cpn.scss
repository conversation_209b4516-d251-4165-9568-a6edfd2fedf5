/*
	Team Card 3 Component
*/

.team-card-3-cpn {
	padding-top: 100px;
	padding-bottom: 120px;
	display: flex;
	flex-flow: row wrap;
	@include clearfix;

	@media (max-width: 767px) {
		padding-top: 50px;
		padding-bottom: 60px
	}

	.team-img {
		img {
			max-width: 100%;
		}

		@media (max-width: 767px) {
			order: 1;
			margin-bottom: 20px;
		}
	}

	.team-card-details {
		@media (max-width: 767px) {
			order: 2;
		}
	}

	.name {
		margin-top: 0;
		margin-bottom: 15px;
	}

	.role {
		margin-top: 0px;
		margin-bottom: 35px;
		color: var(--color-text);
		font-family: var(--font-secondary);
		font-size: var(--font-size-cta-small);
		line-height: var(--line-height-cta-small);
	}

	.team-card-text {
		color: var(--color-text);
		font-family: var(--font-secondary);
		font-size: var(--font-size-small);
		line-height: var(--line-height-small);
	}

	.main-button {
		@media (max-width: 767px) {
			display: block;
		}
	}
}