/*
	Team Interstice 2 Component
*/

$teamInterstice2PaddingTop: 100px;
$teamInterstice2PaddingBottom: 60px;
$teamInterstice2InfoBoxBorder: 5px solid #004b3c33;

$teamInterstice2InfoBoxTitleSize: 22px;
$teamInterstice2InfoBoxTitleColor: #000000;
$teamInterstice2InfoBoxTitleLineHeight: 27px;

.team-interstice-2-cpn {
	background-color: $white;
	position: inherit;
	padding-top: $teamInterstice1PaddingTop;
	padding-bottom: $teamInterstice2PaddingBottom;

	.team-info-box {
		padding: 30px 20px;
		border-bottom: $teamInterstice2InfoBoxBorder;
		text-align: center;
		display: flex;
		align-items: center;

		.team-info-box-content {
			margin: 0 auto;
			float: none;
			width: 100%;
			padding: 0;
		}

		.main-button {
			min-width: 0;
		}
	}
}