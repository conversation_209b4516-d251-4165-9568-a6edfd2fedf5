/*
	Team Interstice 1 Component
*/

$teamInterstice1PaddingTop: 100px;
$teamInterstice1InfoBoxBorder: 5px solid #004b3c33;

$teamInterstice1InfoBoxTitleSize: 22px;
$teamInterstice1InfoBoxTitleColor: #000000;
$teamInterstice1InfoBoxTitleLineHeight: 27px;

$teamInterstice1InfoBoxDescriptionSize: 14px;
$teamInterstice1InfoBoxDescriptionColor: var(--color-text);
$teamInterstice1InfoBoxDescriptionLineHeight: 24px;

.team-interstice-1-cpn {
	position: inherit;
	padding-top: $teamInterstice1PaddingTop;

	.team-info-box {
		margin-top: 40px;
		margin-bottom: 80px;
		padding: 60px 20px;
		border: $teamInterstice1InfoBoxBorder;
		text-align: center;
		@include clearfix;

		.team-info-box-content {
			margin: 0 auto;
			float: none;
		}

		.description {
			margin-top: 0;
			margin-bottom: 30px;
			color: $teamInterstice1InfoBoxDescriptionColor;
			font-family: var(--font-secondary);
			font-size: $teamInterstice1InfoBoxDescriptionSize;
			line-height: $teamInterstice1InfoBoxDescriptionLineHeight;
			text-align: center;
		}
	}
}