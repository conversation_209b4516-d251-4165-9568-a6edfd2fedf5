/*
	Team Hero 1 Component
*/

.team-hero-1-cpn {
	
	.description {
		position: relative;
		margin-top: 60px;

		&.-opened {
			.text-ctn {
				max-height: 5000px;
			}

			.small-link {
				display: none;
			}

			.gradient {
				opacity: 0;
			}
		}

		.text-ctn {
			max-height: 200px;
			overflow: hidden;
			transition: $transition;

			p {
				margin-top: 0;
				color: var(--color-text);
				font-family: var(--font-secondary);
				font-size: var(--font-size-small);
				line-height: 30px;
			}
		}

		.small-link {
			display: block;
			margin-top: 10px;
			font-size: var(--font-size-cta-small);
			text-transform: none;
		}

		.gradient {
			position: absolute;
			bottom: 0;
			left: 0;
			right: 0;
			height: 113px;
			width: 100%;
			background: linear-gradient(180deg, rgba(255,255,255,0) 0%, #FFFFFF 100%);
			transition: $transition;
		}
	}

	ul {
      padding-left: 0px;
      padding-bottom: 30px;

      li {
        position: relative;
        padding: 16px 0px 16px 40px;
        font-family: var(--font-secondary);
        font-size: var(--font-size-small);
        line-height: var(--line-height-small);
        border-bottom: 1px solid #D8D8D8;

        &:before {
          color: var(--primary-color);
          font-family: 'icomoon';
          speak: none;
          font-style: normal;
          font-weight: normal;
          font-variant: normal;
          text-transform: none;
          line-height: 1;
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
          content: "\e90c";
          position: absolute;
          left: 0;
          top: 22px;
        }
      }
    }
}

