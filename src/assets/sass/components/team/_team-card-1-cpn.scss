/*
	Team Card 1 Component
*/

.team-card-cpn {
	@media (max-width: 992px) {
		display: flex;
    	flex-flow: row wrap;
	}
}

.team-card-1 {
	padding: 32px 24px;
	border-radius: 16px;
	background-color: var(--color-bg);
	margin-bottom: 30px;
	box-sizing: border-box;

	@media (max-width: 992px) {
		width: calc(100% * 1/2 - 10px);
		margin-right: 20px;

		&:nth-child(2n){
			margin-right: 0;
		}
	}

	@media (max-width: 767px) {
		width: 100%;
		margin-right: 0;
	}

	.card-content {
		padding-bottom: 20px;
		display: flex;
		align-items: center;
		flex-wrap: wrap;
	}

	.img-ctn {
		width: 100px;
		height: 100px;
		border-radius: 50%;
		overflow: hidden;

		@media (max-width: 1200px) {
			width: 75px;
			height: 75px;
		}
	}

	.info-ctn {
		margin-left: 20px;
		width: calc(100% - 120px);

		@media (max-width: 1200px) {
			width: calc(100% - 95px);
		}

		p {
			margin: 0;
		}

		.name {
			padding-bottom: 5px;
			color: $black;
			font-family: var(--font-secondary);
			font-weight: 600;
			line-height: 28px;
		}

		.role {
			padding-bottom: 10px;
			color: $light-grey;
			font-family: var(--font-secondary);
			font-size: 13px;
			line-height: var(--line-height-cta-small);
		}

		.number {
			color: var(--color-text);
			font-family: var(--font-secondary);
			font-size: var(--font-size-small);
			font-weight: 600;
			line-height: 22px;
			display: block;
			margin-bottom: 5px;

			i {
				margin-right: 12px;
			}
		}
	}

	.main-button {
		width: 100%;
		font-size: var(--font-size-cta-small);
		min-width: 0;
	}
}