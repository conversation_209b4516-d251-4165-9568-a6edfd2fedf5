/*
	Specialist Card Component
*/

.specialist-card-cpn {
	position: relative;
	width: 100%;

	.specialist-content {
		padding: 40px;
		width: 100%;
		display: inline-block;
		box-shadow: 0 2px 4px 0 rgba(0,0,0,0.1);
		background-color: $white;
		margin-bottom: 4px;
	}

	.specialist-wrap {
		display: block;
		position: relative;
		width: 100%;
		margin-bottom: 20px;
		border-radius: 3px;
	}

	.img-ctn {
		text-align: center;
		img {
			margin-bottom: 40px;
			max-height: 280px;
		}
	}

	.description {
		margin-top: 0;
		margin-bottom: 20px;
		color: var(--color-text);
		font-family: var(--font-secondary);
		font-size: var(--font-size-cta-small);
		line-height: var(--line-height-cta-small);
	}

	.user-ctn {
		.name {
			margin-top: 0;
			margin-bottom: 10px;
			color: $black;
			font-family: var(--font-secondary);
			font-size: var(--font-size-cta-small);
			font-weight: 600;
			line-height: 19px;
		}

		.address {
			margin: 0;
			max-width: 250px;
			color: var(--color-text);
			font-family: var(--font-secondary);
			font-size: var(--font-size-cta-small);
			line-height: var(--line-height-cta-small);
		}

		.contact-ctn {
			display: flex;
			align-items: center;
			margin-top: 40px;

			@media (max-width:570px) {
				flex-wrap: wrap;
			}

			a {
				margin-right: 35px;
				color: var(--primary-color);
				font-size: var(--font-size-cta-small);
				font-weight: 900;
				line-height: 17px;
				text-transform: uppercase;

				@media (max-width:570px) {
					width: 100%;
					margin-bottom: 15px;
				}

				&:last-child {
					margin-right: 0;
				}

				&.mobile {
					display: flex;
					align-items: center;

					i {
						font-size: 18px;
						margin-right: 5px;
					}
				}

				&.website {
					display: flex;
					align-items: center;

					i {
						font-size: 18px;
						margin-right: 5px;
					}
				}

				&.icon-mail {
					font-size: 16px;
				}
			}
		}
	}
}
