.landing-sheet-cpn {
  h1 {
    color: #2D2F31;
    font-size: 46px;
    line-height: 60px;
    font-weight: 600;
  }

  .article-share-cpn {
    position: sticky;
    height: 0;
    top: 40vh;
    margin-left: -80px;

    div {
      display: flex;
      padding-top: 20px;
      align-items: baseline;
      flex-direction: column;

      a, button {
        margin-bottom: 20px;
        margin-right: 0;
      }

      .fa, i, button::before {
        font-size: 20px;
        color: var(--color-text);
      }
    
    }
  }

  .main-img-ctn {
    text-align: center;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      max-height: 540px;
      background-position: center;
    }
  }

  .main-text-ctn {
    position: relative;
    padding-top: 60px;
    padding-bottom: 100px;
    font-family: var(--font-secondary);
    font-size: var(--font-size-small);
    line-height: var(--line-height-small);
    color: var(--color-text);

    ul {
      padding-left: 0px;
      padding-bottom: 30px;

      li {
        position: relative;
        padding: 16px 0px 16px 40px;
        border-bottom: 1px solid #F2F2F2;

        &:before {
          color: var(--primary-color);
          font-family: 'icomoon';
          speak: none;
          font-style: normal;
          font-weight: normal;
          font-variant: normal;
          text-transform: none;
          line-height: 1;
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
          content: "\e90d";
          position: absolute;
          left: 0;
          top: 22px;
        }

        p { margin: 0; }
      }
    }
  }

  .cta-ctn {
    padding: 90px 0;
    background-color: var(--primary-color);
    color: white;
    text-align: center;

    .main-button { margin: auto; }
  }

  @media (max-width: 767px) {
    h1 {
      font-size: 30px;
      line-height: 38px;
    }

    .article-share-cpn { display: none; }

    .main-text-ctn {
      padding-top: 30px;
      padding-bottom: 30px;
      font-size: 14px;
      line-height: 26px;

      h2 {
        font-size: 24px;
        line-height: 30px;
      }
    }
  }
}