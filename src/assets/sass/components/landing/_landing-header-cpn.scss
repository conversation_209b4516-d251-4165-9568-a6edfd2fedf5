.landing-header-cpn {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 120px;
  padding: 0 40px;

  .logo-ctn a { display: block; }

  .main-button {
    min-width: auto;
    font-size: var(--font-size-cta-small);
    line-height: var(--line-height-cta-small);
    text-transform: none;
  }

  a {
    color: var(--color-text);
    transition: $transition;
    cursor: pointer;
    &:hover { color: var(--primary-color); }
  }
  
  nav {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 30px;
    height: 100%;
  }

  @media (max-width: 767px) {
    .main-button { 
      padding: 10px 22px;
    }
  }

  @media (max-width: 570px) {
    padding: 0 15px;

    nav {
      gap: 10px;
    }

    .main-button {
      height: auto;
      margin-left: 10px;
      font-size: 11px;
    }
    
    .language-switcher { 
      padding-left: 20px;
    }
  }
}