/*
	CTA Broker Component
*/

$ctaBrokerPadding: 80px 0;

$ctaBrokerObliqueBG: #F6F6F6;
$ctaBrokerObliqueWidth: 240px;
$ctaBrokerObliqueRight: 290px;
$ctaBrokerObliqueSkew: skew(-45deg);

$ctaBrokerTagSize: 13px;
$ctaBrokerTagLineHeight: 18px;
$ctaBrokerTagNameWeight: 700;

.cta-broker-cpn {
	overflow: hidden;
	padding-top: 30px;
	padding-bottom: 30px;
	position: relative;
	background: url('/assets/images/common/home-hero-bg.jpg');
	background-size: cover;
	align-items: flex-end;

	.list {
		padding-left: 0;
	}

	.grid {
		align-items: flex-end;

		@media (max-width: 992px) {
			.cta-broker-content {
				order: 1;
			}

			.cta-broker-img {
				order: 2;
			}
		}
	}

	.cta-broker-content {
		// padding: $ctaBrokerPadding;
		display: flex;
		flex-direction: column;
		justify-content: center;

		@media (max-width: 992px) {
			order: 2;
		}

		.subtext {
			font-size: var(--font-size-small);
			line-height: var(--line-height-small);
			margin-top: 15px;
			margin-bottom: 15px;
			color: #4B5054;
			font-family: var(--font-secondary);
		}

		a {
			max-width: 300px;
		}

		ul {
			padding-left: 0px;
			padding-bottom: 30px;

			li {
				position: relative;
				padding: 16px 0px 16px 40px;
				font-family: var(--font-secondary);
				font-size: var(--font-size-small);
				line-height: var(--line-height-small);
				border-bottom: 1px solid #D8D8D8;

				&:before {
					color: var(--primary-color);
					font-family: 'icomoon';
					speak: none;
					font-style: normal;
					font-weight: normal;
					font-variant: normal;
					text-transform: none;
					line-height: 1;
					-webkit-font-smoothing: antialiased;
					-moz-osx-font-smoothing: grayscale;
					content: "\e90c";
					position: absolute;
					left: 0;
					top: 22px;
				}
			}
		}
	}

	.oblique {
		display: block;
		background: $ctaBrokerObliqueBG;
		height: 100%;
		width: $ctaBrokerObliqueWidth;
		position: absolute;
		z-index: 1;
		bottom: 0%;
		right: $ctaBrokerObliqueRight;
		transform: $ctaBrokerObliqueSkew;
	}

	.cta-broker-img {
		text-align: center;
		position: relative;

		img{
			max-height: 560px;
		}

		@media (max-width: 992px) {
			order: 1;
			padding-top: 20px;
			width: 100%;
		}
	}

	.broker-tag {
		min-width: 280px;
		padding: 12px 20px;
		position: absolute;
		right: 20px;
		bottom: 60px;
		background-color: rgba(0,0,0,0.5);
		text-align: left;

		@media (max-width: 992px) {
			right: 0;
		}

		p {
			margin: 0;
			color: $white;
			font-family: var(--font-secondary);
			font-size: $ctaBrokerTagSize;
			line-height: $ctaBrokerTagLineHeight;
		}

		.name {
			font-weight: $ctaBrokerTagNameWeight;
		}
		.role {
			font-weight: normal;
		}
	}
}

.cta-broker-cpn.-home-hero-type {
	
	@media (max-width: 992px) {
		padding-top: 0;
		background: white;
	}

	.cta-broker-content {
		@media (max-width: 992px) {
			padding-top: 34px;
			padding-bottom: 60px;
		}
	}

	.background-ctn {
		position: relative;
		display: none;

		@media (max-width: 992px) {
			display: block;
		}
	}

	.cta-broker-img {
		@media (max-width: 992px) {
			display: none;
		}
	}

	.broker-tag {
		@media (max-width: 992px) {
			bottom: 15px;
			min-width: 220px;
		}
	}
}
