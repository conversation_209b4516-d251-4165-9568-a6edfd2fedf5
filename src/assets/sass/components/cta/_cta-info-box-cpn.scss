$infoBoxBorder: 5px solid #004b3c33;

$infoBoxTitleSize: 22px;
$infoBoxTitleColor: #000000;
$infoBoxTitleLineHeight: 27px;

$infoBoxDescriptionSize: 14px;
$infoBoxDescriptionColor: var(--color-text);
$infoBoxDescriptionLineHeight: 24px;

.team-info-box{
    padding: 40px 20px 50px;
    border: $infoBoxBorder;
    text-align: center;
    @include clearfix;

    .team-info-box-content {
        margin: 0 auto;
        float: none;
    }

    .description {
        margin-top: 0;
        margin-bottom: 30px;
        color: $infoBoxDescriptionColor;
        font-family: var(--font-secondary);
        font-size: $infoBoxDescriptionSize;
        line-height: $infoBoxDescriptionLineHeight;
        text-align: center;
    }

    .main-button {
        margin: auto;

        @media (max-width: 992px) {
            min-width: inherit;
        }
    }

    &.-large{

        text-align: left;
        padding: 75px 50px;

        .title{
            margin-bottom: 20px;
        }

        .description{
            text-align: left;
            margin-bottom: 0;
        }

        @media (max-width:992px) {
            padding: 50px 20px;
        }

        @media (max-width:768px) {

            text-align: center;

            .team-info-box-content{
                display: block;
            }
            
            .description{
                text-align: center;
                margin-bottom: 20px;
            }
        }
    }
}
