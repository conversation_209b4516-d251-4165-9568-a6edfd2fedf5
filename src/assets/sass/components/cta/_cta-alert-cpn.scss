// Box CTA with full-width background

$cta-alert-cpn-width: 600px;
$cta-alert-cpn-background: $white;
$cta-alert-cpn-color: auto;
$cta-alert-cpn-image: '/assets/images/placeholder/cta/banner-1.png';

.cta-alert-cpn {
	background-image: url($cta-alert-cpn-image);
	background-size: cover;

	.container {
		.text-ctn {
			width: 100%;
			max-width: $cta-alert-cpn-width;
			margin: 100px auto;
			padding: 50px;
			background-color: $cta-alert-cpn-background;
			color: $cta-alert-cpn-color;
			text-align: center;
	
			.button-ctn {
				width: max-content;
				margin: 40px auto 0;
			}
		}
	}

	@media (max-width: 768px) {
		.container {
			.text-ctn {
				padding: 50px 20px;
			}
		}
	}

	@media (max-width: 450px) {
		.container {
			.text-ctn {
				.button-ctn {
					width: 100%;

					a.main-button {
						width: 100%;
						padding: 0 30px;
					}
				}
			}
		}
	}
}





.homestaging-cta-cpn {
	display: flex;
	.container { align-items: center; }

	.text-wrap {
		padding-left: 0;
	}

	.img-ctn {
		width: 100%;

		img {
			@include img();
		}
	}

	@media (max-width: 767px){
		
		.img-ctn {
			width: 100%;
			order: 1;
			padding: 0 !important;
		}

		.text-wrap {
			order: 2;
		}
	}
}

