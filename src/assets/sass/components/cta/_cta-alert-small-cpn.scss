// Plain full-width CTA with icon

$cta-alert-small-cpn-color: white;
$cta-alert-small-cpn-background: var(--primary-color);

.cta-alert-small-cpn {
	background: $cta-alert-small-cpn-background;

	.container {

		.text-ctn {
			color: $cta-alert-small-cpn-color;
		}

		.icon-ctn i {
			margin-left: auto;
			color: $cta-alert-small-cpn-color;
			font-size: 130px;
		}
	}
	
	@media (max-width: 768px) {
		.container {
			text-align: center;
			flex-direction: column;
			gap: 40px;

			.text-ctn {
				max-width: none;

				.button-ctn {
					width: max-content;
					margin: 40px auto 0;

					a.-white {
						padding: 5px 30px;
					}
				}
			}
		}
	}
}
