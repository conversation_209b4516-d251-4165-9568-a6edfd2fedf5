// Plain full-width CTA with icon

$cta-evaluation-small-cpn-color: white;
$cta-evaluation-small-cpn-background: var(--primary-color);

.cta-evaluation-small-cpn {
	background: $cta-evaluation-small-cpn-background;

	.container {
		@include flex(center, space-between);
		padding: 85px 0;

		.text-ctn {
			color: $cta-evaluation-small-cpn-color;
			max-width: 50%;

			.button-ctn {
				margin-top: 30px;
			}
		}

		.icon-ctn i {
			margin-left: auto;
			color: $cta-evaluation-small-cpn-color;
			font-size: 130px;
		}
	}
	
	@media (max-width: 768px) {
		.container {

			.text-ctn {
				max-width: none;

				.button-ctn {
					width: max-content;
					margin: 40px auto 0;

					a.-white {
						padding: 0 30px;
					}
				}
			}
		}
	}
}
