.pdf-guide-cta-cpn {
    margin: 80px 0;

    &.full-width {
        background-color: var(--color-bg);
        margin: 0;
    }

    .container {

        .img-ctn {
            @include flex();

            img {
                max-width: 300px;
                width: 100%;
            }
        }
        
        .text-ctn {
            width: 100%;
        
            p.description {
                margin-bottom: 30px;
                color: var(--color-text);
                font-family: var(--font-secondary);
                font-size: var(--font-size-small);
                line-height: var(--line-height-small);
            }
        }
    }

	@media (max-width: 767px) {

        margin: 60px 0;

		.container {
			
			.img-ctn {
                width: auto;
                margin: 30px 0 40px;
			}

			.text-ctn {
                text-align: center;

				a.main-button {
					margin: auto;
				}
			}
		}
	}
}
