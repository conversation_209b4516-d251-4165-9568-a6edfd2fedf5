// Box CTA with full-width background

$cta-evaluation-cpn-background: $white;
$cta-evaluation-cpn-color: auto;
$cta-evaluation-cpn-image: '/assets/images/placeholder/cta/banner-2.jpg';

.cta-evaluation-cpn {
	background-image: url($cta-evaluation-cpn-image);
	background-size: cover;
	
	.container {
		min-height: 60vh;
		.text-ctn {
			width: 100%;
			padding: 100px 50px;
			background-color: $cta-evaluation-cpn-background;
			color: $cta-evaluation-cpn-color;
			text-align: center;
	
			.button-ctn {
				width: max-content;
				margin: 40px auto 0;
			}
		}

		.icon-ctn {
			margin-left: auto;
		}
	}

	@media (max-width: 768px) {
		.container {
			.text-ctn {
				padding: 50px 20px;
			}
		}
	}

	@media (max-width: 450px) {
		.container {
			.text-ctn {
				.button-ctn {
					width: 100%;

					a.main-button {
						width: 100%;
						padding: 5px 30px;
					}
				}
			}
		}
	}
}
