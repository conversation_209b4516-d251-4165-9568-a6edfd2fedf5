.neighborhood-hero-cpn {
	@media (min-width: 1024px){
		min-height: 450px;
	}
	

	.filter {
		position: absolute;
		top: 0;
		bottom: 0;
		left: 0;
		right: 0;
		z-index: 2;
		background: rgba(0,0,0,0.2);
		opacity: 1;
	}
	
	.container {

		.inner{
			max-width: 500px;
			background-color: #FFFFFF;
			padding: 50px 70px;

			.name{
				color: black;
				font-size: 38px;
				line-height: 49px;
				margin-top: 0;
				margin-bottom: 0;
			}
	
			.open-video{
				font-size: 18px;
				font-weight: bold;
				text-transform: uppercase;
				color: var(--primary-color);
				text-align: left;
				margin: 0;
				margin-top: 30px;
				vertical-align:middle;
				display: flex;
				align-items: center;
				max-width: 170px;
				span{
					transition: all 0.5s ease-in-out;
					height: 100%;
					margin-left: 10px;
					
				}
				&:hover{
					cursor: pointer;
					span{
						margin-left: 15px;
					}
				}
			}

			@media (max-width:992px) {
				padding: 30px 50px;

				.name{
					font-size: 30px;
				}
			}

			@media (max-width:768px) {
				display: none;
			}

		}

		
	}

	.swiper-slide{

		@media (max-width:425px) {
			max-height: 250px;
		}
	}
}

.full-screen-cpn{
  .video-ctn{
    z-index: 2;

    @media (max-width: 768px){
      width: 100%;
    }

    .video-inner{
      position: relative;
      padding-bottom: 56.2%;
      height: 0;
      z-index: 2;


      iframe{
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
      }
    }


  }
}