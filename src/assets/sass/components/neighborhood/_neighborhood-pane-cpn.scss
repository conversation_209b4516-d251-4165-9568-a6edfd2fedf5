.neighborhood-pane-cpn {
	padding-top: 60px;
	padding-bottom: 60px;
	background-color: $white;
	display: block;
	width: 35%;
	position: relative;
    margin: 0;
    z-index: 10;
    min-height: 600px;
	margin-left: -35%;

	@media (max-width: 1100px){
		width: 45%;
		margin-left: -45%;
	}

	@media (max-width: 992px){
		display: none;
		width: 100%;
		margin-left: 0;

		&.show {
			display: block;
		}
	}

	.dropdown { 
		padding-top: 40px;
	}

	.toggle-pane {
		position: sticky;
		top: 185px;
		right: 0px;
		margin-right: -30px;
		margin-top: 20px;
		float: right;
		display: block;
		width: 30px;
		line-height: 60px;
		text-align: center;
		background-color: var(--primary-color);
		color: white;
		transition: $transition;
		cursor:pointer;
		opacity: 1;
		transition: $transition;

		div {
			transform: rotate(180deg);
		}

		&:hover { background-color: rgba($color: var(--primary-color), $alpha: 0.4); }

		&.selected {
			pointer-events: none; 
			background-color: rgba(153,153,153,0.7);
		}

		@media (max-width: 992px) {
			display: none;
		}
	}

	&.open {
		margin: 0;

		div {
			transform: rotate(0);
		}
	}

	.pane-content-ctn {
    	max-width: 100%;
		padding: 0 25px;

		@media (max-width: 420px){
			padding: 0;
		}

		.info-ctn {
			border-bottom: none;

			.sub-section-title {
				font-size: 14px;
				line-height: 24px;
				color: var(--color-text);
			}
		}

		.selection-list {
			max-height: 100%;
	  
			.neighborhood-card {
			  display: flex;
			  margin-bottom: 15px;
			  overflow: hidden;
			  align-items: center;
			  z-index: 1;
			  position: relative;
			  height: 60px;
			  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.1);
			  transition: $transition;
	  
			  img {
				min-width: 100px;
				max-width: 100px;
				width: 100px;
				height: 100%;
				object-fit: cover;
				z-index: 0;
				transition: $transition;
			  }
	  
			  .link-ctn {
				background-color: $white;
				height: 100%;
				width: 100%;
				z-index: 99;
			  }

			  p {
				display: flex;
				align-items: center;
				font-size: 1rem;
				color: var(--primary-color);
				font-weight: 600;
				padding: 0 20px 0 20px;
				width: 100%;
				height: 100%;
				margin: 0;
				z-index: 0;
				transition: $transition;
			  }
	  
			  &:hover {
				cursor: pointer;
				p {
				  background-color: var(--primary-color);
				  color: $white;
				  transition: $transition;
				}
				img {
				  transform: scale(1.1);
				  opacity: 0.7;
				  transition: $transition;
				}
			  }
	  
			  &.active {
				p {
				  color: $white;
				  background-color: var(--primary-color);
				}
				img {
				  transform: scale(1.1);
				}
			  }
			}
		  }
	}
}