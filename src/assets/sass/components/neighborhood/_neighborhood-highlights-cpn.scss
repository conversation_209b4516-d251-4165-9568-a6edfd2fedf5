.neighborhood-highlights-cpn {
	background: $white;
	
	
	.neighborhood-description {
		
		.open-video{
			display: none;
			color: var(--primary-color);
			text-transform: uppercase;
			font-weight: bold;
			max-width: 150px;
			align-items: center;
			span{
				vertical-align:middle;
				margin-left: 10px;
			}
			@media (max-width:768px) {
				display: flex;
			}
		}
		
			
	}

	.small-list {
		color: var(--color-text);
		font-family: var(--font-secondary);
		font-size: var(--font-size-small);
		line-height: var(--line-height-small);
		display: flex;
		flex-wrap: wrap;
		padding: 0;
		list-style: none;

		li {
			position: relative;
			width: calc(50% - 15px);
			font-weight: 600;
			font-size: 14px;
		    line-height: 20px;
			color: var(--color-text);
			padding: 10px 30px;

			&:nth-child(2n+1){
				margin-right: 30px;
			}

			&:nth-child(4n+1){
				background-color: var(--color-bg);
			}

			&:nth-child(4n+2){
				background-color: var(--color-bg);
			}
		}
	}

	.blocks-ctn {
		@media (max-width: 992px) {
			margin-top: 30px;
		}

		@media (min-width: 992px) {
			padding-left: grid-space(math.div(1,12),0);
		}

		.block {
			margin-bottom: 30px;
			display: flex;
			align-items: flex-start;
			justify-content: center;

			@media (max-width: 992px) {
				justify-content: flex-start;
			}

			.icon-ctn {
				padding-right: 30px;

				i {
					font-size: 35px;
					color: var(--primary-color);
				}
			}

			.description {
				margin-top: 7px;
				margin-bottom: 0;
				color: var(--color-text);
				font-family: var(--font-secondary);
				font-size: var(--font-size-cta-small);
				line-height: var(--line-height-cta-small);
			}
		}
	}

	@media (max-width:425px) {
		padding: 40px 0 80px;
	}
}