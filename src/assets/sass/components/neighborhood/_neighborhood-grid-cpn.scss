$nbhItemBackground: $white;
$nbhItemShadow: 0 2px 4px 0 rgba(0,0,0,0.1);

.neighborhood-grid-cpn {
	background-color: var(--color-bg);
	padding-bottom: 80px;
  
	.page-head {
	  max-width: 80%;
	  margin: 0 auto 80px;
	  text-align: center; 
  
	  .text { padding: 0 40px; }
	}
  
	.nbh-grid {
	  display: grid;
	  grid-template-columns: 1fr 1fr 1fr;
	  gap: 100px 30px;
	}
  
	.nbh-item {
		@include flex(flex-start, space-between, column);
		
	  .item-img {
		position: relative;
		height: 300px;
		overflow: hidden;
  
		img { 
		  @include img();
		  transition: 0.4s all ease;
		}
  
		// Darker overlay
		&::after {
		  @include pseudoElement();
		  top: 0;
		  width: 100%;
		  height: 100%;
		  background-color: black;
		  opacity: 0;
		  transition: 0.4s all ease;
		  pointer-events: none; 
		}
	  }
  
	  .item-title {
		margin: 30px 0 20px;
		@include fontSize(32px, 40px, 600);
	  }
  
	  .main-button { 
		margin-top: 20px; 
		// width: 100%;
	  }
  
	  &:hover {
		.item-img {
		  img { transform: scale(1.06); }
		  &::after { opacity: 0.2; }
		  cursor: pointer;
		}
	  }
	}
  
	.loading { padding: 60px 0 100px; }

	&.-vertical{
		.nbh-grid {
			grid-template-columns: 1fr;
			gap: 60px;
		}
		
		.nbh-item {
			@include flex(flex-start, space-between, row);
			box-shadow: $nbhItemShadow;
			background-color: $nbhItemBackground;
			min-height: 400px;
			
			.item-img {
				width: 40%;
				height: 100%;
			}

			.content{
				width: 60%;
				padding: 40px 80px;
				

				.item-title{
					margin-top: 0;
				}

				.main-button{
					margin-top: 40px;
				}
			}
		}
	
	}

	@media (max-width: 991px) {
	  .page-head {
		max-width: none;
		// .title { @include fontSize(60px, 66px); }
	  }
  
	  .nbh-grid {
		grid-template-columns: 1fr 1fr;
	  }

	  &.-vertical{
		.nbh-item {
			flex-direction: column;

			.item-img {
				min-height: 250px;
				width: 100%;
			}

			.content{
				padding: 40px;
				width: 100%;
			}
		}
	  }
	}
  
	@media (max-width: 600px) {
	  .page-head {
		// .title { 
		//   margin: 70px 0 40px;
		//   @include fontSize(44px, 50px);
		// }
	  }

	  .nbh-item {
		.main-button { width: 100%; }
	  }
  
	  .nbh-grid {
		grid-template-columns: 1fr;
	  }

	  &.-vertical{
		.nbh-item {
			.content{
				padding: 30px;

				.main-button{
					width: auto;
				}
			}
		}
	  }
	}
  }