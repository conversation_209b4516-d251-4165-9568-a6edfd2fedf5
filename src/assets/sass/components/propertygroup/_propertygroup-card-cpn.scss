/*
	Property Group Card Component
*/

$propertyGroupsCardRadius: 0 0 2px 2px;
$propertyGroupsCardBG: #FFFFFF;
$propertyGroupsCardShadow: 0 2px 6px 0 rgba(0,0,0,0.1);

.propertygroup-card-cpn {
	padding: 30px;
	margin-bottom: 40px;
	border-radius: $propertyGroupsCardRadius;
	background-color: $propertyGroupsCardBG;
	box-shadow: $propertyGroupsCardShadow;
	@include clearfix;

	.img-ctn {
		a { height: 100%; }

		img { @include img(); }
	}

	.card-content {
		padding-right: 0;

		@media (max-width: 767px) {
			padding: 0;
		}
	}

	.propertygroup-card {
	}

	.description {
		margin-top: 0;
		margin-bottom: 15px;
		color: var(--color-text);
		font-family: var(--font-secondary);
		font-size: var(--font-size-cta-small);
		line-height: var(--line-height-cta-small);
	}
}