/*
	Evaluation Start Components
*/

.evaluation-start-cpn {
	padding-top: 60px;
	padding-bottom: 80px;
	background-color: var(--color-bg);

	.inner {
		padding: 64px;
		background: $white;
		border-radius: 24px;

		@media (max-width: 768px) {
			padding: 60px 20px;
		}
	}

	.-page-description {
		margin-top: 20px;
		margin-bottom: 0;
		padding-bottom: 30px;
		color: var(--color-text);
		font-family: var(--font-secondary);
		font-size: var(--font-size-small);
		line-height: var(--line-height-small);
		text-align: center;
	}

	.main-button {
		margin: 40px auto;
	}

  // Pin icon
  .input-ctn{
    position: relative;
    &::after {
      font-family: 'icomoon' !important;
      content: '\e91a';
      display: block;
      position: absolute;
      top: 17px;
      right: 15px;
      font-size: 20px;
      color: $light-grey;
    }
  }

  @media(max-width: 425px) {
    .main-button {
      width: 100%;
    }  
  }
}