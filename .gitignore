# See http://help.github.com/ignore-files/ for more about ignoring files.

# production environment
/src/environments/environment.prod.ts
/src/environments/environment.ts

# compiled output
/dist
/tmp
/out-tsc

# dependencies
/node_modules

/www
/wwwbkp

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# misc
/.angular/cache
/.sass-cache
/connect.lock
/coverage
/libpeerconnection.log
npm-debug.log
testem.log
/typings

# System Files
.DS_Store
*.DS_Store
Thumbs.db