# E-closion Mise en ligne
Avant mise en ligne, s'assurer que les points suivant ont été remplis
- [ ] Nom de domaine accessible ( Connexion name.com / goDaddy ...)
- [ ] Contenu Site bien enregistré dans l'API Prod ( https://v3.e-closion.ca/ )
- [ ] Metas Title / Description bien renseigné (Penser à la metas Image par défaut) 
- [ ] ( si demander )  Le script d'intégration Google Analytics
- [ ] ( si demander ) Le plan de redirection à mettre dans le .htaccess 
- [ ] Préparation Robots.txt (public/robots.txt) / Ajouter en prévision le sitemap ex: Sitemap: https://domain.com/sitemap.xml

## Mise en ligne serveur
Concernant la mise en ligne sur le serveur suivre étape mise en ligne classique 
Voir document mise en ligne. &#8594;

https://docs.google.com/document/d/1s23jTRBFhPj_PsZUwdP3JXOMB4dshu7biu-r61h5XR8/edit

### Rappel installation Projet: 
1. Installer les modules node: $ yarn
2. Copier le fichier d'environnement: $ cp src/environments/environment.dist.ts src/environments/environment.ts && cp src/environments/environment.dist.ts src/environments/environment.prod.ts
3. Modifier les fichiers d'environnement pour refléter votre configuration
4. Rouler le script de build: $ sh build.sh (l’output sera dans un dossier ./dist)
5. Créer un dossier www vide
6. Rouler le script de déploiement $ sh deploy.sh (./www sera déplacé dans ./wwwbkp et ./dist sera déplacé vers ./www)
7. Tester en pointant le domaine vers l’ip du serveur dans le fichier host local

### Configuration Admin
1. Se connecter à l’admin et ajouter le domaine de production dans la section Domaines autorisés pour l'API en prenant soin de l’associer au bon site.
2. Toujours dans l’admin, atteindre la configuration du site et cocher “Activer la gateway ?” (toujours) et “Activer les rapports mensuels ?” (si applicable).

### Pour terminer
1. Une fois les tests d’affichage concluants, faire pointer les DNS du domaine principal sur l’ip du serveur et attendre la propagation.
2. Installer la clé SSL sur l’espace d’hébergement et faire les redirections nécessaires.

## Ajustement post mise en ligne
Dans la tâche mise en ligne vous trouverez une liste "rappel" des tâches à faire après mise en ligne.

### Ajout scripts sitemap automatique
A la racine du projet. (avant le public_html) 
1. Créer un dossier script et cloner le repo https://github.com/devkryzalid/e-closion-scripts
```
git clone https://github.com/devkryzalid/e-closion-scripts.git .
```
2. Dans le dossier sitemap suivre les démarches d'installation décrite dans le Readme.txt

#### Rappel: 
1.  Exécuter $ sh install.sh
2.  Ajuster l'url et l'output path dans settings.json
3.  Exécuter $ sh run.sh
4.  Après la première exécution pour un site, Google Search Console API demandera l'autorisation pour envoyer le sitemap via un lien unique. Ce lien unique doit être ouvert par le compte administratif du compte Google Search Console. Dans la majorité des cas il s'agit de  [<EMAIL>](mailto:<EMAIL>)

#### Ajout Cron Job pour le sitemap
1.  Configurer une cronjob qui exécute run.sh à une intervalle raisonnable
```
## Affiche la table de Cron du serveur ##
crontab -e
## Ajouter ligne correspondant à votre site exemple: ##
#marieclaudesirard sitemap generator
0 2 * * 3 $(cd /var/www/html/marieclaudesirard.ca/scripts/sitemap; /bin/timeout -s 2 3600 sh run.sh)
```

#### Vérification Robots.txt
Vérification que la ligne concernant le sitemap soit bien dans le robots.txt

## Validation SEO
Une validation SEO peut être intéressant une fois la mise en ligne terminé. Vous pouvez vous fier aux critères https://www.woorank.com/fr/ 

Et Ajuster aux besoins 

### Ajout Dans search console ( si pas déjà fait ) 
1. Se connecter à Google search console avec le compte *<EMAIL>* (accès dans OnePassword) 
2.  Ajouter la propriété si non existante. / Préférer la validation DNS / Sinon Validation HTML 

## ⚠️ Si je ne vois pas mes modifications 
Les serveurs de production et notamment E-closion ont un système de cache bien à eux qui peut être un peu contraignant, il est donc recommandé de **clear ce cache** d'une mise en ligne. 

- Ajouter **/clearcache?key=Uw4pfAPRyz** à l'url de votre site en production
- Même choses pour les clear les routes API **https://v3.e-closion.ca/clearcache?key=Uw4pfAPRyz**