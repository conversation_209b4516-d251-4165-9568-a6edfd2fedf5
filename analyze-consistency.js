#!/usr/bin/env node

const fs = require('fs');

// Read both files
const tokensData = JSON.parse(fs.readFileSync('tokens.json', 'utf8'));
const tokens = JSON.parse(tokensData);
const scssContent = fs.readFileSync('src/assets/sass/configs/_generated-variables.scss', 'utf8');

console.log('🔍 ANALYSIS: JSON tokens vs SCSS variables\n');

let missingVariables = [];
let totalTokens = 0;
let processedTokens = 0;

// Check typography
console.log('📝 TYPOGRAPHY:');
if (tokens.typography) {
    for (const [key, value] of Object.entries(tokens.typography)) {
        totalTokens++;
        const cssVarName = `--${key.replace(/_/g, '-')}`;
        
        if (typeof value === 'object' && value.desktop !== undefined) {
            if (scssContent.includes(cssVarName)) {
                processedTokens++;
            } else {
                missingVariables.push(`Typography: ${cssVarName} (${JSON.stringify(value)})`);
            }
        }
    }
}

// Check colors
console.log('\n🎨 COLORS:');
if (tokens.colors) {
    for (const [key, value] of Object.entries(tokens.colors)) {
        totalTokens++;
        const cssVarName = `--${key.replace(/_/g, '-')}`;
        
        if (typeof value === 'object' && (value.light || value.dark)) {
            if (scssContent.includes(cssVarName)) {
                processedTokens++;
            } else {
                missingVariables.push(`Colors: ${cssVarName} (${JSON.stringify(value)})`);
            }
        }
    }
}

// Check interface
console.log('\n📐 INTERFACE:');
if (tokens.interface) {
    for (const [key, value] of Object.entries(tokens.interface)) {
        totalTokens++;
        const cssVarName = `--${key.replace(/_/g, '-')}`;
        
        if (typeof value === 'object' && value.desktop !== undefined) {
            if (scssContent.includes(cssVarName)) {
                processedTokens++;
            } else {
                missingVariables.push(`Interface: ${cssVarName} (${JSON.stringify(value)})`);
            }
        }
    }
}

console.log('\n📊 SUMMARY:');
console.log(`Total tokens in JSON: ${totalTokens}`);
console.log(`Tokens found in SCSS: ${processedTokens}`);
console.log(`Missing variables: ${missingVariables.length}`);

if (missingVariables.length > 0) {
    console.log('\n❌ MISSING VARIABLES:');
    missingVariables.forEach(missing => console.log(`   ${missing}`));
} else {
    console.log('\n✅ All tokens from JSON are present in SCSS!');
}

// Check for extra variables in SCSS that might not be in JSON
const scssVariables = [...scssContent.matchAll(/--([a-z0-9-]+):/g)].map(match => match[1]);
const jsonKeys = [];

// Collect all JSON keys
if (tokens.typography) {
    Object.keys(tokens.typography).forEach(key => {
        jsonKeys.push(key.replace(/_/g, '-'));
    });
}
if (tokens.colors) {
    Object.keys(tokens.colors).forEach(key => {
        jsonKeys.push(key.replace(/_/g, '-'));
    });
}
if (tokens.interface) {
    Object.keys(tokens.interface).forEach(key => {
        jsonKeys.push(key.replace(/_/g, '-'));
    });
}

const extraVariables = scssVariables.filter(scssVar => !jsonKeys.includes(scssVar));

if (extraVariables.length > 0) {
    console.log('\n⚠️  EXTRA VARIABLES IN SCSS (not in JSON):');
    extraVariables.forEach(extra => console.log(`   --${extra}`));
}

console.log(`\n🔄 Consistency: ${missingVariables.length === 0 ? 'PERFECT' : 'NEEDS ATTENTION'}`);
