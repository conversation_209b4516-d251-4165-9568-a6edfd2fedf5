- Rework le header
- Rework le link.scss
- Rework les formulaire (css, js, html markup) ((horrible dès qu'on doit les modifiers))
- Overwrite les titre pour la partie blog (feuille de style special blog)
- Faire le menage dans le bas de common.scss, marquer comme // OLD TO REWORK
- je sais pas comment mais essayer de faire du menage dans fr.json et en.json

- rework la card property 



<!-- Notes -->
- page **Accueil**, **acheter**, **vendre**, **liste blog** plus ou moins refaite
Certains element moins important en todo a refaire avec la nouvelle structure mais moins genant dans l'immediat
- page qui presse dans l'immediat : **detail blog**, **equipe**, 
- Dans un second temps : **quartier**, **contact**
- En dernier : Alerte immo, visite libre, estimation, homestaging, projet immo, nos specialiste, témoignage, carrière