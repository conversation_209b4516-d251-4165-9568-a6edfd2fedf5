{"compileOnSave": false, "compilerOptions": {"skipLibCheck": true, "downlevelIteration": true, "outDir": "./dist/out-tsc", "baseUrl": "src", "paths": {"@/*": ["app/*"]}, "sourceMap": true, "mapRoot": "./", "declaration": false, "moduleResolution": "node", "experimentalDecorators": true, "target": "ES2022", "types": ["node"], "lib": ["es2021", "dom"], "module": "es2020", "useDefineForClassFields": false}, "angularCompilerOptions": {"preserveWhitespaces": false}}