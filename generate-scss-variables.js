#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Configuration - Change this to 'dark' for dark theme
const THEME_MODE = process.argv[2] || 'light'; // Can be overridden by command line argument
const INPUT_FILE = 'tokens.json';
const OUTPUT_FILE = 'src/assets/sass/configs/_generated-variables.scss';

function generateScssVariables() {
  try {
    // Validate theme mode
    if (!['light', 'dark'].includes(THEME_MODE)) {
      throw new Error('Theme mode must be either "light" or "dark"');
    }

    // Read the JSON file
    const tokensData = JSON.parse(fs.readFileSync(INPUT_FILE, 'utf8'));
    const tokens = JSON.parse(tokensData);

    let scss = '/* Generated SCSS Variables from tokens.json */\n';
    scss += `/* Theme mode: ${THEME_MODE} */\n`;
    scss += '/* Generated on: ' + new Date().toISOString() + ' */\n\n';
    scss += ':root {\n';

    // Process typography tokens
    if (tokens.typography) {
      scss += '\n    // ==========================================\n'
      scss += '    // TYPOGRAPHY VARIABLES\n'
      scss += '    // ==========================================\n\n'

      // Separate font families and sizes for better organization
      const fontFamilies = [];
      const fontSizes = [];
      const fontWeights = [];
      const otherTypo = [];

      for (const [key, value] of Object.entries(tokens.typography)) {
        if (key.includes('font-family')) {
          fontFamilies.push([key, value]);
        } else if (key.includes('size')) {
          fontSizes.push([key, value]);
        } else if (key.includes('weight')) {
          fontWeights.push([key, value]);
        } else {
          otherTypo.push([key, value]);
        }
      }

      // Process font families first
      if (fontFamilies.length > 0) {
        scss += '    /* Font Families */\n'
        fontFamilies.forEach(([key, value]) => {
          const cssVarName = `--${key.replace(/_/g, '-')}`;
          if (typeof value === 'object' && value.desktop !== undefined) {
            if (typeof value.desktop === 'string') {
              scss += `    ${cssVarName}: "${value.desktop}";`;
              if (value.mobile && value.mobile !== value.desktop) {
                scss += ` /* Mobile: "${value.mobile}" */`;
              }
              scss += '\n';
            }
          }
        });
        scss += '\n';
      }

      // Process font sizes
      if (fontSizes.length > 0) {
        scss += '    /* Font Sizes */\n'
        fontSizes.forEach(([key, value]) => {
          const cssVarName = `--${key.replace(/_/g, '-')}`;
          if (typeof value === 'object' && value.desktop !== undefined && typeof value.desktop === 'number') {
            const mobileValue = value.mobile !== undefined && value.mobile !== 0 ? value.mobile : value.desktop;
            if (mobileValue !== value.desktop) {
              scss += `    ${cssVarName}: #{responsive-size(${mobileValue}px, ${value.desktop}px, $desktop-lg)};\n`;
            } else {
              scss += `    ${cssVarName}: #{rem(${value.desktop}px)};\n`;
            }
          }
        });
        scss += '\n';
      }

      // Process font weights
      if (fontWeights.length > 0) {
        scss += '    /* Font Weights */\n'
        fontWeights.forEach(([key, value]) => {
          const cssVarName = `--${key.replace(/_/g, '-')}`;
          if (typeof value === 'object' && value.desktop !== undefined && typeof value.desktop === 'number') {
            const mobileValue = value.mobile !== undefined && value.mobile !== 0 ? value.mobile : value.desktop;
            // Font weights use media queries instead of responsive-size
            scss += `    ${cssVarName}: ${value.desktop};\n`;
          }
        });
        scss += '\n';
      }

      // Process other typography tokens
      if (otherTypo.length > 0) {
        scss += '    /* Other Typography */\n'
        otherTypo.forEach(([key, value]) => {
          const cssVarName = `--${key.replace(/_/g, '-')}`;
          if (typeof value === 'object' && value.desktop !== undefined) {
            if (typeof value.desktop === 'string') {
              scss += `    ${cssVarName}: "${value.desktop}";`;
              if (value.mobile && value.mobile !== value.desktop) {
                scss += ` /* Mobile: "${value.mobile}" */`;
              }
              scss += '\n';
            } else if (typeof value.desktop === 'number') {
              const mobileValue = value.mobile !== undefined && value.mobile !== 0 ? value.mobile : value.desktop;
              if (mobileValue !== value.desktop) {
                scss += `    ${cssVarName}: #{responsive-size(${mobileValue}px, ${value.desktop}px, $desktop-lg)};\n`;
              } else {
                scss += `    ${cssVarName}: #{rem(${value.desktop}px)};\n`;
              }
            }
          }
        });
        scss += '\n';
      }
    }

    // Process colors tokens
    if (tokens.colors) {
      scss += '\n    // ==========================================\n'
      scss += '    // COLOR VARIABLES\n'
      scss += '    // ==========================================\n\n'

      // Organize colors by category
      const brandColors = [];
      const textColors = [];
      const backgroundColors = [];
      const ctaColors = [];
      const elementColors = [];
      const navColors = [];
      const baseColors = [];
      const otherColors = [];

      for (const [key, value] of Object.entries(tokens.colors)) {
        if (key.includes('brand')) {
          brandColors.push([key, value]);
        } else if (key.includes('text')) {
          textColors.push([key, value]);
        } else if (key.includes('bg')) {
          backgroundColors.push([key, value]);
        } else if (key.includes('cta')) {
          ctaColors.push([key, value]);
        } else if (key.includes('elements')) {
          elementColors.push([key, value]);
        } else if (key.includes('nav')) {
          navColors.push([key, value]);
        } else if (key.includes('base')) {
          baseColors.push([key, value]);
        } else {
          otherColors.push([key, value]);
        }
      }

      // Process each category
      const processColorCategory = (category, name) => {
        if (category.length > 0) {
          scss += `    /* ${name} */\n`
          category.forEach(([key, value]) => {
            const cssVarName = `--${key.replace(/_/g, '-')}`;
            if (typeof value === 'object' && value[THEME_MODE] !== undefined) {
              scss += `    ${cssVarName}: ${value[THEME_MODE]};\n`;
            }
          });
          scss += '\n';
        }
      }

      processColorCategory(brandColors, 'Brand Colors');
      processColorCategory(textColors, 'Text Colors');
      processColorCategory(backgroundColors, 'Background Colors');
      processColorCategory(ctaColors, 'CTA Colors');
      processColorCategory(elementColors, 'Element Colors');
      processColorCategory(navColors, 'Navigation Colors');
      processColorCategory(baseColors, 'Base Colors');
      processColorCategory(otherColors, 'Other Colors');
    }

    // Process interface tokens
    if (tokens.interface) {
      scss += '\n    // ==========================================\n'
      scss += '    // INTERFACE VARIABLES\n'
      scss += '    // ==========================================\n\n'

      // Organize interface tokens by category
      const spacing = [];
      const padding = [];
      const sizing = [];
      const radius = [];
      const grid = [];
      const stroke = [];
      const scale = [];
      const icon = [];
      const otherInterface = [];

      for (const [key, value] of Object.entries(tokens.interface)) {
        if (key.includes('spacing')) {
          spacing.push([key, value]);
        } else if (key.includes('padding')) {
          padding.push([key, value]);
        } else if (key.includes('sizing')) {
          sizing.push([key, value]);
        } else if (key.includes('radius')) {
          radius.push([key, value]);
        } else if (key.includes('grid')) {
          grid.push([key, value]);
        } else if (key.includes('stroke')) {
          stroke.push([key, value]);
        } else if (key.includes('scale')) {
          scale.push([key, value]);
        } else if (key.includes('icon')) {
          icon.push([key, value]);
        } else {
          otherInterface.push([key, value]);
        }
      }

      // Process each interface category
      const processInterfaceCategory = (category, name) => {
        if (category.length > 0) {
          scss += `    /* ${name} */\n`
          category.forEach(([key, value]) => {
            const cssVarName = `--${key.replace(/_/g, '-')}`;
            if (typeof value === 'object' && value.desktop !== undefined) {
              if (typeof value.desktop === 'string') {
                scss += `    ${cssVarName}: "${value.desktop}";`;
                if (value.mobile && value.mobile !== value.desktop) {
                  scss += ` /* Mobile: "${value.mobile}" */`;
                }
                scss += '\n';
              } else if (typeof value.desktop === 'number') {
                const mobileValue = value.mobile !== undefined && value.mobile !== 0 ? value.mobile : value.desktop;
                if (mobileValue !== value.desktop) {
                  scss += `    ${cssVarName}: #{responsive-size(${mobileValue}px, ${value.desktop}px, $desktop-lg)};\n`;
                } else {
                  scss += `    ${cssVarName}: #{rem(${value.desktop}px)};\n`;
                }
              }
            }
          });
          scss += '\n';
        }
      }

      processInterfaceCategory(radius, 'Border Radius');
      processInterfaceCategory(spacing, 'Spacing');
      processInterfaceCategory(padding, 'Padding');
      processInterfaceCategory(grid, 'Grid System');
      processInterfaceCategory(scale, 'Scale System');
      processInterfaceCategory(stroke, 'Strokes');
      processInterfaceCategory(sizing, 'Sizing');
      processInterfaceCategory(icon, 'Icon Spacing');
      processInterfaceCategory(otherInterface, 'Other Interface');
    }

    scss += '}\n\n';

    // Add organized mobile media queries
    scss += '// ==========================================\n';
    scss += '// MOBILE OVERRIDES\n';
    scss += '// ==========================================\n\n';
    scss += '@media (max-width: $tablet-lg) {\n';
    scss += '    :root {\n';

    // Mobile typography overrides
    if (tokens.typography) {
      const mobileOverrides = [];
      const fontWeightOverrides = [];
      
      for (const [key, value] of Object.entries(tokens.typography)) {
        if (typeof value === 'object' && value.mobile && value.mobile !== value.desktop) {
          if (typeof value.mobile === 'string') {
            mobileOverrides.push([key, value]);
          } else if (typeof value.mobile === 'number' && key.includes('weight') && value.mobile !== 0) {
            fontWeightOverrides.push([key, value]);
          }
        }
      }

      if (mobileOverrides.length > 0) {
        scss += '        /* Typography Mobile Overrides */\n';
        mobileOverrides.forEach(([key, value]) => {
          const cssVarName = `--${key.replace(/_/g, '-')}`;
          scss += `        ${cssVarName}: "${value.mobile}";\n`;
        });
        scss += '\n';
      }
      
      if (fontWeightOverrides.length > 0) {
        scss += '        /* Font Weight Mobile Overrides */\n';
        fontWeightOverrides.forEach(([key, value]) => {
          const cssVarName = `--${key.replace(/_/g, '-')}`;
          scss += `        ${cssVarName}: ${value.mobile};\n`;
        });
        scss += '\n';
      }
    }

    // Mobile interface overrides
    if (tokens.interface) {
      const mobileInterfaceOverrides = [];
      for (const [key, value] of Object.entries(tokens.interface)) {
        if (typeof value === 'object' && value.mobile && typeof value.mobile === 'string' && value.mobile !== value.desktop) {
          mobileInterfaceOverrides.push([key, value]);
        }
      }

      if (mobileInterfaceOverrides.length > 0) {
        scss += '        /* Interface Mobile Overrides */\n';
        mobileInterfaceOverrides.forEach(([key, value]) => {
          const cssVarName = `--${key.replace(/_/g, '-')}`;
          scss += `        ${cssVarName}: "${value.mobile}";\n`;
        });
        scss += '\n';
      }
    }

    scss += '    }\n';
    scss += '}\n';

    // Create directory if it doesn't exist
    const outputDir = path.dirname(OUTPUT_FILE);
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // Write the SCSS file
    fs.writeFileSync(OUTPUT_FILE, scss);

    console.log('✅ SCSS variables generated successfully!');
    console.log(`📁 Output file: ${OUTPUT_FILE}`);
    console.log(`🎨 Theme mode: ${THEME_MODE}`);
    console.log('📊 Processed:');
    console.log(`   - Typography: ${Object.keys(tokens.typography || {}).length} variables`);
    console.log(`   - Colors: ${Object.keys(tokens.colors || {}).length} variables`);
    console.log(`   - Interface: ${Object.keys(tokens.interface || {}).length} variables`);
  } catch (error) {
    console.error('❌ Error generating SCSS variables:', error.message);
    process.exit(1);
  }
}

// Show usage if help is requested
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log('Usage: node generate-scss-variables.js [theme]');
  console.log('');
  console.log('Arguments:');
  console.log('  theme    Theme mode: "light" or "dark" (default: "light")');
  console.log('');
  console.log('Examples:');
  console.log('  node generate-scss-variables.js');
  console.log('  node generate-scss-variables.js light');
  console.log('  node generate-scss-variables.js dark');
  console.log('');
  console.log('Or using npm scripts:');
  console.log('  npm run generate-variables');
  console.log('  npm run generate-variables-dark');
  process.exit(0);
}

// Run the script
generateScssVariables();
