{"name": "e-closion-v3-frontend", "version": "3.13.0", "license": "MIT", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "generate-variables": "node generate-scss-variables.js light", "generate-variables-dark": "node generate-scss-variables.js dark"}, "private": true, "dependencies": {"@angular/animations": "16.2.12", "@angular/cdk": "^16.2.14", "@angular/common": "16.2.12", "@angular/compiler": "16.2.12", "@angular/core": "16.2.12", "@angular/forms": "16.2.12", "@angular/google-maps": "^15.0.0", "@angular/platform-browser": "16.2.12", "@angular/platform-browser-dynamic": "16.2.12", "@angular/platform-server": "16.2.12", "@angular/router": "16.2.12", "@fortawesome/free-solid-svg-icons": "^6.0.0", "@ng-select/ng-select": "^11.0.0", "@ngx-translate/core": "^14.0.0", "@ngx-translate/http-loader": "^7.0.0", "chart.js": "^4.0.0", "core-js": "^3.18.1", "gsap": "^3.13.0", "instafeed.js": "^2.0.0", "mapbox-gl": "^2.5.0", "moment": "^2.22.0", "ng-recaptcha": "^12.0.0", "ng2-charts": "^4.1.1", "ng2-date-picker": "^16.0.0", "ng2-file-upload": "^5.0.0", "ngx-mask": "^13.1.1", "ngx-page-scroll": "^11.0.0", "ngx-page-scroll-core": "^11.0.0", "ngx-pagination": "^6.0.1", "ngx-pipes": "^3.2.2", "ngx-sharebuttons": "^13.0.0", "ngx-webstorage": "^12.0.0", "normalize.css": "^8.0.1", "nouislider": "^15.5.0", "numeral": "^2.0.6", "rxjs": "^6.6.7", "rxjs-compat": "^6.3.3", "sw-precache": "^5.2.1", "swiper": "^11.2.8", "tslib": "^2.0.0", "zone.js": "~0.13.3"}, "devDependencies": {"@angular-devkit/build-angular": "^16.2.16", "@angular/cli": "^16.2.16", "@angular/compiler-cli": "16.2.12", "@angular/language-service": "^16.2.12", "@types/mapbox-gl": "^3.4.1", "@types/node": "^17.0.9", "@typescript-eslint/eslint-plugin": "^5.10.0", "@typescript-eslint/parser": "^5.10.0", "eslint": "^7.12.1", "eslint-config-standard": "^16.0.3", "eslint-plugin-import": "^2.22.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "sass": "^1.85.1", "ts-node": "~3.0.4", "typescript": "4.9.3"}}